import React, { useState, useEffect } from 'react';

import { PiSpinnerGapBold } from 'react-icons/pi';

import { useSubscriptionStore } from '@/store/subscription';

import { getSubscriptionUserData } from '@/utils/subscription';

import GreenCheckIcon from '@/assets/icons/GreenCheckIcon';

import AppModal from '@/core/components/app-modal';
import AppSelect from '@/core/components/app-select';
import AppTextField from '@/core/components/app-text-field';

import { Plan } from '../PlanCard';

interface UpgradeModalProps {
  open: boolean;
  onClose: () => void;
  plan: Plan;
  nextPlan?: Plan | null;
  billingCycle: 'monthly' | 'yearly';
  billingDetails: {
    name: string;
    email: string;
    promoCode: string;
  };
  onBillingDetailsChange: (field: string, value: string) => void;
  onProceed: () => void;
  onAddFeaturesClick: () => void;
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({
  open,
  onClose,
  plan,
  nextPlan,
  billingCycle,
  billingDetails,
  onBillingDetailsChange,
  onProceed,
  onAddFeaturesClick,
}) => {
  const {
    setBillingCycle,
    selectedAddOnFeatures,
    calculateProrated,
    proratedCalculation,
    isCalculatingProrated,
  } = useSubscriptionStore();

  const [selectedBillingPeriod, setSelectedBillingPeriod] = useState<
    'monthly' | 'yearly'
  >(billingCycle);

  // Auto-fill billing details from signup data
  useEffect(() => {
    const userData = getSubscriptionUserData();
    if (userData) {
      if (userData.name && !billingDetails.name) {
        onBillingDetailsChange('name', userData.name);
      }
      if (userData.email && !billingDetails.email) {
        onBillingDetailsChange('email', userData.email);
      }
    }
  }, []);

  // Get billing period options based on plan validity
  const getBillingPeriodOptions = () => {
    if (!nextPlan?.apiPlan) return [];

    const validity = nextPlan.apiPlan.validity;
    const options = [];

    if (validity === 'Monthly' || validity === 'Both') {
      options.push({
        value: 'monthly',
        label: `₹ ${nextPlan.monthlyPrice.toLocaleString()}/month`,
      });
    }

    if (validity === 'Yearly' || validity === 'Both') {
      options.push({
        value: 'yearly',
        label: `₹ ${nextPlan.yearlyPrice.toLocaleString()}/year`,
      });
    }

    return options;
  };

  // // Calculate prorated amount when plan or billing period changes
  // useEffect(() => {
  //   if (nextPlan && plan) {
  //     calculateProrated({
  //       currentPlanId: plan.id,
  //       newPlanId: nextPlan.id,
  //       billingPeriod: selectedBillingPeriod,
  //       currentPlanStartDate: new Date().toISOString(), // This should come from user's current subscription
  //     });
  //   }
  // }, [nextPlan, plan, selectedBillingPeriod, calculateProrated]);

  const handleBillingPeriodChange = (newValue: any) => {
    const value = Array.isArray(newValue) ? newValue[0] : newValue;
    const period = value as 'monthly' | 'yearly';
    setSelectedBillingPeriod(period);
    setBillingCycle(period);
  };

  // Calculate add-on features total
  const addOnFeaturesTotal = selectedAddOnFeatures.reduce((total, feature) => {
    const amount =
      selectedBillingPeriod === 'monthly'
        ? feature.monthlyAmount
        : feature.yearlyAmount;
    return total + amount;
  }, 0);
  return (
    <AppModal
      open={open}
      onClose={onClose}
      title="Subscription Details"
      classes={{
        root: 'w-[900px] max-h-[90vh]',
        body: 'p-8',
      }}
    >
      <div className="flex flex-col h-[70vh]">
        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto">
          <div className="flex gap-8 p-2">
            {/* Left Side - Plan Details */}
            <div className="flex-1 space-y-6">
              {/* Plan Header */}
              <div>
                <h3 className="text-2xl font-bold text-[#012436]">
                  Upgrade to {nextPlan?.name || 'Premium'}
                </h3>
                <p className="text-[14px] font-normal text-black mt-1">
                  Valid for{' '}
                  {selectedBillingPeriod === 'monthly' ? '30 Days' : '1 year'}
                </p>
              </div>

              {/* Billing Period Selection */}
              <div>
                <h4 className="text-[16px] font-bold text-[#012436] mb-3">
                  Select Billing Period
                </h4>
                <div className="max-w-[300px]">
                  <AppSelect
                    label="Billing Period"
                    value={selectedBillingPeriod}
                    onChange={handleBillingPeriodChange}
                    options={getBillingPeriodOptions()}
                    placeholder="Select billing period"
                  />
                </div>
              </div>

              {/* Add Features Section - Only show if plan has add-on features */}
              {nextPlan?.hasAddOnFeatures && (
                <div className="flex items-center gap-2">
                  <span className="text-[14px] font-bold text-[#162B60]">
                    Add more features to customise you plan
                  </span>
                  <button
                    onClick={onAddFeaturesClick}
                    className="bg-[#53BDF5] text-white px-4 py-1 rounded-[88px] text-[14px] font-normal hover:bg-[#42a8e0]"
                  >
                    Add Features
                  </button>
                </div>
              )}

              {/* Plan Benefits */}
              {nextPlan && (
                <div>
                  <h4 className="text-[16px] font-bold text-[#012436] mb-3">
                    Plan Benefits
                  </h4>
                  <div className="space-y-2">
                    {nextPlan.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <GreenCheckIcon width={16} height={16} />
                        <span className="text-[14px] font-normal text-black">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Billing Details */}
              <div>
                <h4 className="text-[16px] font-bold text-[#012436] mb-3">
                  Billing Details
                </h4>
                <div className="space-y-3 max-w-[300px]">
                  <AppTextField
                    value={billingDetails.name}
                    onChange={(value) =>
                      onBillingDetailsChange('name', String(value))
                    }
                    placeholder="Enter your name"
                  />
                  <AppTextField
                    value={billingDetails.email}
                    onChange={(value) =>
                      onBillingDetailsChange('email', String(value))
                    }
                    placeholder="Enter your email"
                  />
                  <AppTextField
                    value={billingDetails.promoCode}
                    onChange={(value) =>
                      onBillingDetailsChange('promoCode', String(value))
                    }
                    placeholder="Enter promo code"
                  />
                </div>
              </div>
            </div>

            {/* Right Side - Pricing Summary (Scrollable) */}
            <div className="w-64 space-y-2">
              {isCalculatingProrated ? (
                <div className="flex justify-center py-4">
                  <PiSpinnerGapBold className="animate-spin text-lg" />
                </div>
              ) : (
                <>
                  {/* New Plan Amount */}
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-[14px] font-normal text-black">
                      {nextPlan?.name} Plan
                    </span>
                    <span className="text-[14px] font-normal text-black">
                      ₹{' '}
                      {proratedCalculation?.newPlanAmount?.toLocaleString() ||
                        (nextPlan
                          ? (selectedBillingPeriod === 'monthly'
                              ? nextPlan.monthlyPrice
                              : nextPlan.yearlyPrice
                            ).toLocaleString()
                          : '0')}
                    </span>
                  </div>

                  {/* Current Plan Refund */}
                  {proratedCalculation?.currentPlanRefund &&
                    proratedCalculation.currentPlanRefund > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span className="text-[14px] font-normal">
                          Current Plan Refund
                        </span>
                        <span className="text-[14px] font-normal">
                          -₹{' '}
                          {proratedCalculation.currentPlanRefund.toLocaleString()}
                        </span>
                      </div>
                    )}

                  {/* Add-on Features */}
                  {selectedAddOnFeatures.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-[14px] font-normal text-black">
                        Add-on Features
                      </span>
                      <span className="text-[14px] font-normal text-black">
                        ₹ {addOnFeaturesTotal.toLocaleString()}
                      </span>
                    </div>
                  )}

                  {/* Sub Total */}
                  <div className="flex justify-between">
                    <span className="text-[14px] font-normal text-black">
                      Sub Total
                    </span>
                    <span className="text-[14px] font-normal text-black">
                      ₹{' '}
                      {(
                        (proratedCalculation?.finalAmount ||
                          (nextPlan
                            ? selectedBillingPeriod === 'monthly'
                              ? nextPlan.monthlyPrice
                              : nextPlan.yearlyPrice
                            : 0)) + addOnFeaturesTotal
                      ).toLocaleString()}
                    </span>
                  </div>

                  {/* Tax */}
                  <div className="flex justify-between">
                    <span className="text-[14px] font-normal text-black">
                      Tax
                    </span>
                    <span className="text-[14px] font-normal text-black">
                      ₹ 0
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Fixed Total Amount and Proceed Button at bottom */}
        <div className="border-t mt-4 pt-4 bg-white">
          <div className="flex justify-end">
            <div className="w-64 space-y-3">
              <div className="flex justify-between border-t border-b py-3">
                <span className="text-[16px] font-bold text-black">
                  Total Amount :
                </span>
                <span className="text-[16px] font-bold text-black">
                  ₹{' '}
                  {(
                    (proratedCalculation?.finalAmount ||
                      (nextPlan
                        ? selectedBillingPeriod === 'monthly'
                          ? nextPlan.monthlyPrice
                          : nextPlan.yearlyPrice
                        : 0)) + addOnFeaturesTotal
                  ).toLocaleString()}
                </span>
              </div>

              {/* Proceed Button */}
              <button
                onClick={onProceed}
                disabled={isCalculatingProrated}
                className="w-full py-2 px-3 bg-[#012436] text-white rounded-lg text-[14px] font-medium hover:bg-[#013547] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCalculatingProrated ? 'Calculating...' : 'Proceed to Pay'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </AppModal>
  );
};

export default UpgradeModal;
