# Payment Integration Implementation Guide

## Overview

This guide provides the complete implementation for passing `paymentId` from Razorpay to all MRD and EMR APIs after successful payment, following the subscription pattern.

## Current Issue

- Subscription flow correctly passes `paymentId` to subscription APIs
- MRD patient registration, consultation booking, and EMR lab test/prescription APIs are NOT receiving `paymentId`
- This prevents proper payment tracking and reconciliation

## Required Changes

### 1. MRD Patient Registration

**File:** `src/views/mrd/manage-patient/manage/index.tsx`

**Change Required:** Update the Razorpay success callback to capture paymentId and pass it to patient creation API.

```typescript
// BEFORE (Line 194):
async () => {

// AFTER (Line 194):
async (response) => {
```

**Add paymentId to finalData object:**

```typescript
const finalData: Patient = {
  ...patientData,
  // ... existing fields ...
  contact: {
    phone: patientData.contact?.phone || '',
    email: patientData.contact?.email || '',
  },
  // Add this line:
  paymentId: response.razorpay_payment_id,
};
```

### 2. MRD Consultation Booking

**File:** `src/views/mrd/manage-patient/book-consultation/index.tsx`

**Change Required:** Update the Razorpay success callback to capture paymentId and pass it to appointment creation API.

```typescript
// BEFORE (Line 254):
async (response) => {
  try {
    await handleSubmit(async (data) => {
      const updatedData = {
        ...data,
        consultation: data.consultation.map((consult) => ({
          ...consult,
          paymentStatus: PAYMENT_STATUS.PAID,
          consultationFee: consult.consultationFee
            ? consult.consultationFee
            : consult.doctorId?.consultationFee || null,
        })),
      };
      await onSubmit(updatedData);
    })();

// AFTER:
async (response) => {
  try {
    await handleSubmit(async (data) => {
      const updatedData = {
        ...data,
        consultation: data.consultation.map((consult) => ({
          ...consult,
          paymentStatus: PAYMENT_STATUS.PAID,
          consultationFee: consult.consultationFee
            ? consult.consultationFee
            : consult.doctorId?.consultationFee || null,
          // Add paymentId to each consultation:
          paymentId: response.razorpay_payment_id,
        })),
      };
      await onSubmit(updatedData);
    })();
```

### 3. EMR Prescription Creation

**File:** `src/views/emr/prescription/NewPrescription.tsx`

**Change Required:** Update the Razorpay success callback to capture paymentId and pass it to prescription creation API.

```typescript
// BEFORE (Line 419):
async (response) => {
  setIsProcessingPayment(false);
  setActiveTab(PRESCRIPTION_HISTORY);

  try {
    const prescriptionPayload = {
      patientId: patient.id,
      doctor: doctorName,
      medicines: currentPrescriptions,
      doctorEmail: userData.email,
      status: 'Paid' as const,
    };

// AFTER:
async (response) => {
  setIsProcessingPayment(false);
  setActiveTab(PRESCRIPTION_HISTORY);

  try {
    const prescriptionPayload = {
      patientId: patient.id,
      doctor: doctorName,
      medicines: currentPrescriptions,
      doctorEmail: userData.email,
      status: 'Paid' as const,
      // Add paymentId for payment tracking:
      paymentId: response.razorpay_payment_id,
    };
```

### 4. EMR Lab Test Creation

**File:** `src/views/emr/lab/new-test/index.tsx`

**Change Required:** Update the Razorpay success callback to capture paymentId and pass it to lab test creation API.

```typescript
// BEFORE (Line 398):
async (response) => {
  setIsProcessingPayment(false);
  try {
    const currentLabTests = getValues('labTest');
    const today = new Date().toISOString().split('T')[0];
    const formattedLabTests = (currentLabTests ?? []).map((test) => ({
      testName: test.testName,
      qty: test.quantity,
      instructions: test.instructions,
      cost: test.cost,
      toBeDoneBy: formatDate(test.toBeDoneBy),
      date: today,
      testId: test.id,
      results: null,
      reference: null,
      status: 'Paid',
    }));

    const payload = {
      patientId: patient?.id,
      labTests: formattedLabTests,
      doctorId,
    };

// AFTER:
async (response) => {
  setIsProcessingPayment(false);
  try {
    const currentLabTests = getValues('labTest');
    const today = new Date().toISOString().split('T')[0];
    const formattedLabTests = (currentLabTests ?? []).map((test) => ({
      testName: test.testName,
      qty: test.quantity,
      instructions: test.instructions,
      cost: test.cost,
      toBeDoneBy: formatDate(test.toBeDoneBy),
      date: today,
      testId: test.id,
      results: null,
      reference: null,
      status: 'Paid',
      // Add paymentId to each lab test:
      paymentId: response.razorpay_payment_id,
    }));

    const payload = {
      patientId: patient?.id,
      labTests: formattedLabTests,
      doctorId,
      // Add paymentId at payload level for tracking:
      paymentId: response.razorpay_payment_id,
    };
```

## Type Definitions Updates

### Patient Type

Update `src/types/mrd/manage-patient/patient.ts` to include paymentId:

```typescript
export interface Patient {
  // ... existing fields ...
  paymentId?: string; // Add this field for payment tracking
}
```

### Consultation Type

Update `src/types/mrd/manage-patient/consultation.ts` to include paymentId:

```typescript
export interface ConsultationForm {
  // ... existing fields ...
  paymentId?: string; // Add this field for payment tracking
}
```

### Prescription Type

Update `src/types/emr/prescription/index.ts` to include paymentId:

```typescript
export interface PrescriptionPayload {
  // ... existing fields ...
  paymentId?: string; // Add this field for payment tracking
}
```

### Lab Test Type

Update `src/types/emr/lab/index.ts` to include paymentId:

```typescript
export interface LabTestPayload {
  // ... existing fields ...
  paymentId?: string; // Add this field for payment tracking
}
```

## API Backend Expectations

The backend APIs should expect and store the `paymentId` for:

1. **Patient Registration API:** Store paymentId in patient record for registration fee tracking
2. **Appointment Creation API:** Store paymentId in appointment record for consultation fee tracking
3. **Prescription API:** Store paymentId in prescription record for medicine fee tracking
4. **Lab Test API:** Store paymentId in lab test record for test fee tracking

## Benefits

1. **Payment Reconciliation:** Link payments to specific operations
2. **Audit Trail:** Complete payment history for each patient operation
3. **Refund Management:** Easy identification of paid services for refund processing
4. **Analytics:** Track revenue by service type
5. **Consistency:** Follow the same pattern as subscription payments

## Implementation Priority

1. **High Priority:** MRD Patient Registration (immediate revenue impact)
2. **High Priority:** MRD Consultation Booking (core revenue stream)
3. **Medium Priority:** EMR Prescription (additional revenue)
4. **Medium Priority:** EMR Lab Tests (additional revenue)

## Testing Checklist

- [ ] Verify Razorpay success callback receives paymentId
- [ ] Confirm paymentId is passed to API payloads
- [ ] Test API accepts paymentId without breaking existing functionality
- [ ] Verify paymentId is stored in database
- [ ] Test payment failure scenarios
- [ ] Verify no impact on existing free/unpaid workflows
