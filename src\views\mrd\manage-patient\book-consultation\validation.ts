import dayjs from 'dayjs';
import * as yup from 'yup';

declare module 'yup' {
  interface StringSchema {
    validDate(message?: string): this;
  }
}

yup.addMethod(
  yup.string,
  'validDate',
  function (message = 'Invalid date format') {
    return this.test('valid-date', message, function (value) {
      if (!value) return true;
      return !isNaN(Date.parse(value));
    });
  }
);

export const consultationSchema = yup.object().shape({
  consultation: yup.array().of(
    yup.object().shape({
      doctorId: yup
        .object({ id: yup.string().required('Required') })
        .required('Required'),
      department: yup.string().required('Required'),
      date: yup.string().required('Required').validDate('Invalid date format'),
      time: yup
        .string()
        .required('Required')
        .test(
          'not-in-past-today',
          'Time cannot be in the past',
          function (value) {
            const { date } = this.parent as { date?: string };
            if (!date || !value) return true;
            const isToday = dayjs(date).isSame(dayjs(), 'day');
            if (!isToday) return true;
            const now = new Date();
            const currentMinutes = now.getHours() * 60 + now.getMinutes();
            const [timePart, period] = value.split(' ');
            const [h, m] = timePart.split(':');
            let hour = parseInt(h, 10) % 12;
            if (period === 'PM') hour += 12;
            const minutes = hour * 60 + parseInt(m, 10);
            return minutes >= currentMinutes;
          }
        ),
    })
  ),
});
