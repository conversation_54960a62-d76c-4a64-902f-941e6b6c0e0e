import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { attitudeStore } from '@/store/emr/lifestyle/physical-activity/attitude/attitude-store';

import { detectAmbientInObject } from '@/utils/ambient-detection';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import LifestyleModalWrapper from '@/views/emr/lifestyle/shared/LifestyleModalWrapper';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AttitudeForm from './AttitudeForm';

interface AttitudeModalProps {
  patientData?: QuestionnaireResponse | null;
  mode?: LifestyleMode;
  onAfterSubmit?: () => void;
  hideSaveButton?: boolean;
  initialValues?: any;
}

const AttitudeModal: React.FC<
  AttitudeModalProps & { onSaveRef?: MutableRefObject<(() => void) | null> }
> = ({
  patientData,
  mode = LifestyleMode.CREATE,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
  initialValues,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
  } = attitudeStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { patient } = useCurrentPatientStore();

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? initialValues ?? questions,
    mode: 'onChange',
  });

  const { handleSubmit } = methods;

  const formFields = useMemo(() => {
    if (!questions?.questions?.length) return [];

    return questions.questions;
  }, [questions]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      try {
        if (data?.id) {
          const updateData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
          };
          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
            // Include ambient listening data if available from initialValues
            ...(initialValues?.conversation && {
              conversation: initialValues.conversation,
            }),
            ...(initialValues?.recordingDuration && {
              recordingDuration: initialValues.recordingDuration,
            }),
          };
          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error('Error submitting attitude:', error);
      }
    },
    [
      setModalOpen,
      updateLifestyleData,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.general?.department,
      profile?.id,
      createLifestyleData,
      onAfterSubmit,
      initialValues, // Add initialValues to dependencies
    ]
  );

  const handleSaveClick = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = handleSaveClick;
    }
    return () => {
      if (onSaveRef) {
        onSaveRef.current = null;
      }
    };
  }, [onSaveRef, handleSaveClick]);

  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  useEffect(() => {
    if (patientData) {
      // Transform data to map individual field values to section value.fields structure
      const transformedData = {
        ...patientData,
        questions: patientData.questions?.map((questionGroup: any) => ({
          ...questionGroup,
          fields: questionGroup.fields?.map((field: any) => {
            // If field is a section with nested fields that have individual values
            if (field.type === 'section' && field.fields) {
              const persistedValueFields = field.value?.fields as
                | Array<{ value: any }>
                | undefined;

              const sectionFieldsWithValues = field.fields.map(
                (subField: any, idx: number) => {
                  // Use direct field value if it exists and is not empty, otherwise use persisted value
                  const directValue = subField.value;
                  const persistedValue = persistedValueFields?.[idx]?.value;

                  return {
                    ...subField,
                    value:
                      directValue !== undefined &&
                      directValue !== null &&
                      directValue !== ''
                        ? directValue
                        : (persistedValue ?? ''),
                  };
                }
              );

              const valueFields =
                (persistedValueFields &&
                  persistedValueFields.some((v) => v?.value) && // Only use persisted values if at least one has a non-empty value
                  persistedValueFields.map((v) => ({
                    value: v?.value ?? '',
                  }))) ||
                field.fields.map((subField: any) => ({
                  value: subField.value || '',
                }));

              return {
                ...field,
                fields: sectionFieldsWithValues,
                value: {
                  fields: valueFields,
                },
              };
            }

            return field;
          }),
        })),
      };

      // Create form data structure for React Hook Form
      const formData: any = {};
      transformedData.questions?.forEach(
        (questionGroup: any, groupIndex: number) => {
          if (!formData.questions) formData.questions = [];
          if (!formData.questions[groupIndex])
            formData.questions[groupIndex] = { fields: [] };

          questionGroup.fields?.forEach((field: any, fieldIndex: number) => {
            if (field.value !== undefined) {
              if (!formData.questions[groupIndex].fields[fieldIndex]) {
                formData.questions[groupIndex].fields[fieldIndex] = {};
              }
              formData.questions[groupIndex].fields[fieldIndex].value =
                field.value;
            }
          });
        }
      );

      methods.reset({ ...transformedData, ...formData });
    } else if (initialValues) {
      // Handle ambient listening data structure - same transformation
      const transformedData = {
        ...initialValues,
        questions: initialValues.questions?.map((questionGroup: any) => ({
          ...questionGroup,
          fields: questionGroup.fields?.map((field: any) => {
            // If field is a section with nested fields that have individual values
            if (field.type === 'section' && field.fields) {
              const persistedValueFields = field.value?.fields as
                | Array<{ value: any }>
                | undefined;

              const sectionFieldsWithValues = field.fields.map(
                (subField: any, idx: number) => ({
                  ...subField,
                  value:
                    subField.value !== undefined && subField.value !== null
                      ? subField.value
                      : (persistedValueFields?.[idx]?.value ?? ''),
                })
              );

              const valueFields =
                (persistedValueFields &&
                  persistedValueFields.map((v) => ({
                    value: v?.value ?? '',
                  }))) ||
                field.fields.map((subField: any) => ({
                  value: subField.value || '',
                }));

              return {
                ...field,
                fields: sectionFieldsWithValues,
                value: {
                  fields: valueFields,
                },
              };
            }

            // For non-section fields, preserve as is
            return field;
          }),
        })),
      };

      // Create form data structure for React Hook Form
      const formData: any = {};
      transformedData.questions?.forEach(
        (questionGroup: any, groupIndex: number) => {
          if (!formData.questions) formData.questions = [];
          if (!formData.questions[groupIndex])
            formData.questions[groupIndex] = { fields: [] };

          questionGroup.fields?.forEach((field: any, fieldIndex: number) => {
            if (field.value !== undefined) {
              if (!formData.questions[groupIndex].fields[fieldIndex]) {
                formData.questions[groupIndex].fields[fieldIndex] = {};
              }
              formData.questions[groupIndex].fields[fieldIndex].value =
                field.value;
            }
          });
        }
      );

      methods.reset({ ...transformedData, ...formData });
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, initialValues, questions, methods]);

  return (
    <FormProvider {...methods}>
      <LifestyleModalWrapper
        loading={questionLoading}
        onSubmit={handleSubmit(onSubmit)}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
        data={patientData || undefined}
        doctorName={profile?.general?.fullName}
        patientName={patient?.name}
      >
        <AttitudeForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          variant="modal"
          isAmbientForm={detectAmbientInObject(initialValues || patientData)}
        />
      </LifestyleModalWrapper>
    </FormProvider>
  );
};

export default memo(AttitudeModal);
