import React, { FC, memo, useCallback } from 'react';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import Loader from '@/core/components/app-loaders/Loader';
import { Conversation } from '@/types/emr/lifestyle/questionnaire';

type Props = {
  children: React.ReactNode;
  loading?: boolean;
  onSubmit?: () => void;
  updating?: boolean;
  mode: LifestyleMode;
  onEdit?: () => void;
  finalized: boolean;
  isFormValid?: boolean;
  hideSaveButton?: boolean;
  // Transcript view props
  conversation?: Conversation[];
  showTranscriptView?: boolean;
  onSwitchToTranscript?: () => void;
  onGoBackFromTranscript?: () => void;
};

const ModalWrapper: FC<Props> = ({
  children,
  loading,
  onSubmit,
  updating,
  mode,
  onEdit,
  finalized,
  isFormValid = true,
  hideSaveButton = false,
  conversation,
  showTranscriptView,
  onSwitchToTranscript,
  onGoBackFromTranscript,
}) => {
  const renderButtons = useCallback(() => {
    if (finalized) return <div />;

    // If in transcript view, don't show any buttons in the bottom bar
    if (showTranscriptView) return <div />;

    if (mode !== LifestyleMode.VIEW) {
      if (hideSaveButton) return null;

      return (
        <AppButton
          onClick={onSubmit}
          loading={updating}
          disabled={!isFormValid}
          endIcon={<AppIcon icon="material-symbols:save-outline" />}
          sx={{ borderRadius: 3 }}
        >
          Save Record
        </AppButton>
      );
    } else {
      // In view mode, show only edit button on the left
      return (
        <AppButton
          onClick={onEdit}
          endIcon={<AppIcon icon="line-md:edit-filled" />}
          sx={{ borderRadius: 3 }}
        >
          Edit Record
        </AppButton>
      );
    }
  }, [
    finalized,
    mode,
    onEdit,
    onSubmit,
    updating,
    hideSaveButton,
    showTranscriptView,
  ]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full w-full">
        <Loader />
      </div>
    );
  }

  const hasTranscript = conversation && conversation.length > 0;

  return (
    <div className="h-full w-full flex flex-col max-h-full relative">
      <form
        className="h-full w-full flex flex-col max-h-full"
        onSubmit={onSubmit}
      >
        <div className="flex-1 overflow-y-auto min-h-0">{children}</div>
        {!hideSaveButton && (
          <div className="flex p-base pb-0 border-t min-h-10">
            {renderButtons()}
          </div>
        )}
      </form>

      {/* Floating transcript button - bottom right */}
      {hasTranscript && mode === LifestyleMode.VIEW && (
        <div className="absolute bottom-1 right-4 z-10">
          <AppButton
            onClick={
              showTranscriptView ? onGoBackFromTranscript : onSwitchToTranscript
            }
            endIcon={
              <img
                src={
                  showTranscriptView ? '/images/back.svg' : '/images/record.svg'
                }
                alt={showTranscriptView ? 'back' : 'transcript'}
                className="w-4 h-4"
              />
            }
            sx={{
              borderRadius: 3,
              paddingTop: '2px',
              paddingBottom: '3px',
            }}
            variant="outlined"
          >
            {showTranscriptView ? 'Go back' : 'Switch to Transcript View'}
          </AppButton>
        </div>
      )}
    </div>
  );
};

export default memo(ModalWrapper);
