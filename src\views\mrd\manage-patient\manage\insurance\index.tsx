import { memo, useCallback } from 'react';

import { useFormContext } from 'react-hook-form';

import { useEnterKeyNavigation } from '@/hooks/useEnterKeyNavigation';

import { useManagePatientStore } from '@/store/mrd/manage-patient/manage';

import { formatToNumber } from '@/utils/format-value';

import { insuranceProvidersOptions } from '@/constants/mrd/manage-patient/select-options';

import ControlledImageUploader from '@/components/controlled-inputs/ControlledImageUploader';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const Insurance = () => {
  const { control, trigger } = useFormContext<PatientDetails>();
  const { setCurrentTab } = useManagePatientStore();
  const fieldOrder = ['insurance.provider', 'insurance.id', 'insurance.url'];

  const handleNavigateToNextTab = useCallback(async () => {
    const isValid = await trigger(undefined, { shouldFocus: true });

    if (isValid) {
      setCurrentTab(3);
    }
  }, [trigger, setCurrentTab]);

  const shouldTriggerSave = useCallback((fieldName: string) => {
    return fieldName === 'insurance.url';
  }, []);

  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder,
    shouldTriggerSave,
    onSave: handleNavigateToNextTab,
  });
  return (
    <div className="py-base flex flex-col gap-base">
      <div className="flex gap-base w-[65%]">
        <ControlledSelectField
          name="insurance.provider"
          control={control}
          label="Select Insurance Provider"
          placeholder="Select"
          options={insuranceProvidersOptions}
          initiallyReadonly
          onKeyDown={(e) => handleKeyDown(e, 'insurance.provider')}
        />
        <ControlledTextField
          name="insurance.id"
          control={control}
          label="Insurance Id"
          placeholder="0000 0000 000"
          initiallyReadonly
          fullWidth
          formatValue={formatToNumber}
          onKeyDown={(e) => handleKeyDown(e, 'insurance.id')}
        />
      </div>
      <div>
        <ControlledImageUploader
          name="insurance.url"
          control={control}
          label="Insurance Proof"
          accept=".png, .jpg, .jpeg, .pdf"
          maxSizeInMB={5}
          indicationLabel="(The file size must be less than 5MB.)"
          showError={false}
          onKeyDown={(e) => handleKeyDown(e, 'insurance.url')}
        />
      </div>
    </div>
  );
};

export default memo(Insurance);
