import React from 'react';

import { Controller, Path, Control, FieldValues } from 'react-hook-form';

type ControlledTextAreaProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  rows?: number;
  maxRows?: number;
  required?: boolean;
  rules?: any;
  fullWidth?: boolean;
  className?: string;
};

const ControlledTextArea = <T extends FieldValues>({
  name,
  control,
  label,
  placeholder = 'Enter text...',
  rows = 3,
  maxRows = 6,
  required = false,
  rules,
  fullWidth = true,
  className = '',
}: ControlledTextAreaProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => (
        <div className={`w-full ${className}`}>
          {label && (
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          <textarea
            {...field}
            placeholder={placeholder}
            rows={rows}
            className={`
              w-full px-3 py-2 border rounded-md resize-none
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
              ${fieldState.error ? 'border-red-500' : 'border-gray-300'}
              ${fullWidth ? 'w-full' : ''}
            `}
            style={{
              minHeight: `${rows * 1.5}rem`,
              maxHeight: maxRows ? `${maxRows * 1.5}rem` : undefined,
            }}
          />
          {fieldState.error && (
            <p className="text-red-500 text-xs mt-1">
              {fieldState.error.message}
            </p>
          )}
        </div>
      )}
    />
  );
};

export default ControlledTextArea;
