'use client';

import { useEffect } from 'react';

import { UseFormReturn } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import { SummarizeConversationRes } from '@/query/speech';

import {
  AnthropometryField,
  GeneralPhysicalExaminationField,
  HistoryField,
  SystemicExaminationField,
  VitalField,
} from '@/utils/constants/consultation';
import { archivo } from '@/utils/fonts';

import { calculateAge } from '@/helpers/dates';

import AppTab from '@/core/components/app-tab';

import ExaminationTab from './ExaminationTab';
import HistoryTab from './HistoryTab';

type SummaryFormProps = {
  className?: string;
  data?: {
    summary: Omit<SummarizeConversationRes['summary'], 'conversation'>;
  } | null;
  editable?: boolean;
  expanded?: boolean;
  form: UseFormReturn<SummarizeConversationRes['summary']>;
};

export default function SummaryForm({
  className,
  data,
  editable = false,
  expanded = false,
  form,
}: SummaryFormProps) {
  const { patient } = useCurrentPatientStore();

  const { setValue, reset } = form;

  // Detect if this consultation came from ambient listening
  const isAmbientForm =
    !!(data as any)?.summary?.conversation ||
    !!(data as any)?.summary?.recordingDuration;

  const handleChangeHistoryField = (field: HistoryField, newValue?: string) => {
    setValue(field, newValue);
  };

  const handleChangeLifestyleHistoryField = (
    field: HistoryField,
    newValue?: string
  ) => {
    setValue(field, newValue);
  };

  const handleChangeVitalSignField = (field: VitalField, newValue?: string) => {
    const currentVitals = form.getValues('vitals') || {};
    setValue(
      'vitals',
      {
        ...currentVitals,
        [field]: newValue ? Number(newValue) : null,
      },
      { shouldDirty: true }
    );
  };

  const handleChangeAnthropometryField = (
    field: AnthropometryField,
    newValue?: string
  ) => {
    setValue(`anthropometry.${field}`, newValue || '');

    if ((field === 'weight' || field === 'height') && newValue) {
      const currFormValue = form.getValues()?.anthropometry;
      const isValid =
        field === 'weight' ? !!currFormValue?.height : !!currFormValue?.weight;

      if (isValid) {
        const weight =
          field === 'weight'
            ? parseFloat(newValue)
            : parseFloat(currFormValue?.weight as string);

        const height =
          field === 'height'
            ? parseFloat(newValue)
            : parseFloat(currFormValue?.height as string);

        if (!isNaN(weight) && !isNaN(height)) {
          const heightInMeters = height / 100;
          const bmi = Number(
            (weight / (heightInMeters * heightInMeters)).toFixed(2)
          );
          setValue('anthropometry.bmi', bmi);
        }
      }
    }
  };

  const handleChangeGeneralPhysicalExaminationField = (
    field: GeneralPhysicalExaminationField,
    newValue: boolean | string
  ) => {
    // Get current form values
    const currentValues = form.getValues();
    const currentGeneralExamination =
      currentValues.generalPhysicalExamination || {};

    // Create a new object with existing values
    const updatedGeneralExamination = {
      ...currentGeneralExamination,
      [field]: newValue,
    };

    // If pedalEnema is being set to false, clear its notes
    if (field === 'pedalEnema' && newValue === false) {
      updatedGeneralExamination.pedalEnemaNotes = '';
    }

    // If lymphadenopathy is being set to false, clear its notes
    if (field === 'lymphadenopathy' && newValue === false) {
      updatedGeneralExamination.lymphadenopathyNotes = '';
    }

    // Update the form with the new values
    setValue('generalPhysicalExamination', updatedGeneralExamination, {
      shouldDirty: true,
    });
  };

  const handleChangeHEENTField = (newValue?: string) => {
    setValue('heent', newValue);
  };

  const handleChangeSystemicExaminationField = (
    field: SystemicExaminationField,
    newValue?: string
  ) => {
    const currentSystemicExamination =
      form.getValues('systemicExamination') || {};
    setValue(
      'systemicExamination',
      {
        ...currentSystemicExamination,
        [field]: newValue,
      },
      { shouldDirty: true }
    );
  };

  // Initialize form with data when component mounts or data changes
  useEffect(() => {
    if (data?.summary) {
      // Only update if we have new data
      reset(data.summary, { keepDirty: true });
    } else {
      reset();
    }
  }, [data, editable, reset]);

  // Patient info section
  const PatientInfo = () => {
    const age = patient?.age || calculateAge(patient?.dob || '');
    if (!editable && !expanded) {
      return (
        <div className="bg-[#E7EBEF] rounded-md px-4 py-2 flex items-center gap-2 mb-4 text-sm font-medium">
          <span>{patient?.name || '--'}</span>
          <span className="mx-2 border-l h-5" />
          <span>Patient ID: {patient?.id || 'Auto generated'}</span>
          <span className="mx-2 border-l h-5" />
          <span>
            Age:
            {age ? `${age}yrs` : '--'}
          </span>
        </div>
      );
    }
    // Default (editable or expanded)
    return (
      <section className="flex flex-col gap-2.5 mb-4">
        <h3 className="font-medium text-sm text-[#001926]">
          Patient Information
        </h3>
        <div className="text-sm font-medium">Name: {patient?.name || '--'}</div>
        <div className="text-sm font-medium">
          Patient ID: {patient?.id || '--'}
        </div>
        <div className="text-sm font-medium">Age: {age || '--'}</div>
      </section>
    );
  };

  const tabs = [
    {
      label: 'History',
      content: (
        <div>
          <PatientInfo />
          <HistoryTab
            editable={editable}
            data={data}
            form={form}
            isAmbientForm={isAmbientForm}
            handleChangeHistoryField={handleChangeHistoryField}
            handleChangeLifestyleHistoryField={
              handleChangeLifestyleHistoryField
            }
          />
        </div>
      ),
    },
    {
      label: 'Examination',
      content: (
        <div>
          <PatientInfo />
          <ExaminationTab
            editable={editable}
            data={data}
            expanded={expanded}
            form={form}
            isAmbientForm={isAmbientForm}
            handleChangeVitalSignField={handleChangeVitalSignField}
            handleChangeAnthropometryField={handleChangeAnthropometryField}
            handleChangeGeneralPhysicalExaminationField={
              handleChangeGeneralPhysicalExaminationField
            }
            handleChangeHEENTField={handleChangeHEENTField}
            handleChangeSystemicExaminationField={
              handleChangeSystemicExaminationField
            }
          />
        </div>
      ),
    },
  ];

  return (
    <div className={cn('bg-white', archivo.className, className)}>
      <AppTab
        tabs={tabs}
        variant="standard"
        centered
        scrollButtons={false}
        allowScrollButtonsMobile={false}
        TabIndicatorProps={{
          style: {
            height: 2,
            backgroundColor: '#012436',
          },
        }}
        sx={{
          '& .MuiTabs-flexContainer': {
            justifyContent: 'center',
            borderBottom: '2px solid #012436',
          },
          '& .MuiTab-root': {
            flex: 1,
            minWidth: 0,
            maxWidth: 'none',
            fontWeight: 400,
            fontSize: '18px',
            textTransform: 'none',
            '&.Mui-selected': {
              color: '#012436',
            },
          },
        }}
      />
    </div>
  );
}
