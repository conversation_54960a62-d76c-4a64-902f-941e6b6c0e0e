import React, { useState, useCallback } from 'react';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import LifestyleTranscriptView from './LifestyleTranscriptView';
import ModalWrapper from './ModalWrapper';

interface LifestyleModalWrapperProps {
  children: React.ReactNode;
  loading?: boolean;
  onSubmit?: () => void;
  updating?: boolean;
  mode: LifestyleMode;
  onEdit?: () => void;
  finalized: boolean;
  isFormValid?: boolean;
  hideSaveButton?: boolean;
  // Data for transcript view
  data?: QuestionnaireResponse;
  doctorName?: string;
  patientName?: string;
}

const LifestyleModalWrapper: React.FC<LifestyleModalWrapperProps> = ({
  children,
  loading,
  onSubmit,
  updating,
  mode,
  onEdit,
  finalized,
  isFormValid = true,
  hideSaveButton = false,
  data,
  doctorName,
  patientName,
}) => {
  const [showTranscriptView, setShowTranscriptView] = useState(false);

  const handleSwitchToTranscript = useCallback(() => {
    setShowTranscriptView(true);
  }, []);

  const handleGoBackFromTranscript = useCallback(() => {
    setShowTranscriptView(false);
  }, []);

  // Determine if transcript view should be shown
  const transcriptContent =
    showTranscriptView && data?.conversation ? (
      <LifestyleTranscriptView
        conversation={data.conversation}
        doctorName={doctorName}
        patientName={patientName}
        date={data?.created_on}
        duration={data?.recordingDuration}
      />
    ) : null;

  return (
    <ModalWrapper
      loading={loading}
      onSubmit={onSubmit}
      updating={updating}
      mode={mode}
      onEdit={onEdit}
      finalized={finalized}
      isFormValid={isFormValid}
      hideSaveButton={hideSaveButton}
      conversation={data?.conversation}
      showTranscriptView={showTranscriptView}
      onSwitchToTranscript={handleSwitchToTranscript}
      onGoBackFromTranscript={handleGoBackFromTranscript}
    >
      {showTranscriptView ? transcriptContent : children}
    </ModalWrapper>
  );
};

export default LifestyleModalWrapper;
