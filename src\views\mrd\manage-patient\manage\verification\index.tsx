import { memo, useCallback, useEffect, useRef } from 'react';

import { useFormContext, useWatch } from 'react-hook-form';

import { useEnterKeyNavigation } from '@/hooks/useEnterKeyNavigation';

import { useManagePatientStore } from '@/store/mrd/manage-patient/manage';

import { formatAadhar } from '@/utils/mrd/manage-patient/aadhar';
import {
  formatPAN,
  formatPassport,
} from '@/utils/mrd/manage-patient/validation';

import { proofOptions } from '@/constants/mrd/manage-patient/select-options';

import ControlledImageUploader from '@/components/controlled-inputs/ControlledImageUploader';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const Verification = () => {
  const { control, setValue, trigger } = useFormContext<PatientDetails>();
  const previousProofTypeRef = useRef<string | undefined>();
  const { setCurrentTab } = useManagePatientStore();

  const proofType = useWatch({
    control,
    name: 'proof.type',
  });

  const existingProofData = useWatch({
    control,
    name: 'proof',
  });

  useEffect(() => {
    const currentProofType = proofType?.value;
    const previousProofType = previousProofTypeRef.current;
    if (previousProofType && previousProofType !== currentProofType) {
      setValue('proof.aadharNumber', '');
      setValue('proof.passportNumber', '');
      setValue('proof.panNumber', '');
    }
    previousProofTypeRef.current = currentProofType;
  }, [proofType?.value, setValue]);

  const getActiveProofField = useCallback(() => {
    const proofTypeValue = proofType?.value;
    switch (proofTypeValue) {
      case 'Aadhar card':
        return 'proof.aadharNumber';
      case 'Passport':
        return 'proof.passportNumber';
      case 'Pan card':
        return 'proof.panNumber';
      default:
        if (existingProofData?.passportNumber) return 'proof.passportNumber';
        if (existingProofData?.panNumber) return 'proof.panNumber';
        return 'proof.aadharNumber';
    }
  }, [proofType?.value, existingProofData]);

  const fieldOrder = [
    'proof.type',
    getActiveProofField(),
    'proof.abhaNumber',
    'proof.url',
  ];

  const handleNavigateToNextTab = useCallback(async () => {
    const isValid = await trigger(undefined, { shouldFocus: true });

    if (isValid) {
      setCurrentTab(2);
    }
  }, [trigger, setCurrentTab]);

  const shouldTriggerSave = useCallback((fieldName: string) => {
    return fieldName === 'proof.url';
  }, []);

  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder,
    shouldTriggerSave,
    onSave: handleNavigateToNextTab,
  });

  const renderProofNumberField = () => {
    const proofTypeValue = proofType?.value;

    switch (proofTypeValue) {
      case 'Aadhar card':
        return (
          <ControlledTextField
            name="proof.aadharNumber"
            control={control}
            label="Aadhar Number"
            placeholder="0000 0000 000"
            initiallyReadonly
            fullWidth
            formatValue={formatAadhar}
            slotProps={{
              input: {
                inputProps: { maxLength: 14 },
              },
            }}
            onKeyDown={(e) => handleKeyDown(e, 'proof.aadharNumber')}
          />
        );
      case 'Passport':
        return (
          <ControlledTextField
            name="proof.passportNumber"
            control={control}
            label="Passport Number"
            placeholder="********"
            initiallyReadonly
            fullWidth
            formatValue={formatPassport}
            slotProps={{
              input: {
                inputProps: { maxLength: 9 },
              },
            }}
            onKeyDown={(e) => handleKeyDown(e, 'proof.passportNumber')}
          />
        );
      case 'Pan card':
        return (
          <ControlledTextField
            name="proof.panNumber"
            control={control}
            label="PAN Number"
            placeholder="**********"
            initiallyReadonly
            fullWidth
            formatValue={formatPAN}
            slotProps={{
              input: {
                inputProps: { maxLength: 10 },
              },
            }}
            onKeyDown={(e) => handleKeyDown(e, 'proof.panNumber')}
          />
        );
      default:
        if (existingProofData?.passportNumber) {
          return (
            <ControlledTextField
              name="proof.passportNumber"
              control={control}
              label="Passport Number"
              placeholder="********"
              initiallyReadonly
              fullWidth
              formatValue={formatPassport}
              slotProps={{
                input: {
                  inputProps: { maxLength: 9 },
                },
              }}
              onKeyDown={(e) => handleKeyDown(e, 'proof.passportNumber')}
            />
          );
        }
        if (existingProofData?.panNumber) {
          return (
            <ControlledTextField
              name="proof.panNumber"
              control={control}
              label="PAN Number"
              placeholder="**********"
              initiallyReadonly
              fullWidth
              formatValue={formatPAN}
              slotProps={{
                input: {
                  inputProps: { maxLength: 10 },
                },
              }}
              onKeyDown={(e) => handleKeyDown(e, 'proof.panNumber')}
            />
          );
        }

        return (
          <ControlledTextField
            name="proof.aadharNumber"
            control={control}
            label="Aadhar Number"
            placeholder="0000 0000 000"
            initiallyReadonly
            fullWidth
            formatValue={formatAadhar}
            slotProps={{
              input: {
                inputProps: { maxLength: 14 },
              },
            }}
            onKeyDown={(e) => handleKeyDown(e, 'proof.aadharNumber')}
          />
        );
    }
  };

  return (
    <div className="w-full h-full flex flex-col gap-base py-base">
      <div className="w-[85%] flex gap-base">
        <ControlledSelectField
          name="proof.type"
          control={control}
          label="Proof Type"
          placeholder="Select"
          options={proofOptions}
          initiallyReadonly
          onKeyDown={(e) => handleKeyDown(e, 'proof.type')}
        />
        {renderProofNumberField()}
        <ControlledTextField
          name="proof.abhaNumber"
          control={control}
          label="Abha Number (optional)"
          placeholder="0000 0000 000000"
          initiallyReadonly
          fullWidth
          slotProps={{
            input: {
              inputProps: { maxLength: 14 },
            },
          }}
          onKeyDown={(e) => handleKeyDown(e, 'proof.abhaNumber')}
        />
      </div>
      <div className="w-[85%] flex gap-base">
        <ControlledImageUploader
          name="proof.url"
          control={control}
          label="ID Proof"
          accept=".png, .jpg, .jpeg, .pdf"
          maxSizeInMB={5}
          indicationLabel="(The file size must be less than 5MB.)"
          showError={false}
          onKeyDown={(e) => handleKeyDown(e, 'proof.url')}
        />
      </div>
    </div>
  );
};

export default memo(Verification);
