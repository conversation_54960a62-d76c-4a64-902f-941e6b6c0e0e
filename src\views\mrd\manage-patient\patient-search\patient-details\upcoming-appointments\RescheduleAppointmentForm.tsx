import { FC, memo, useCallback } from 'react';

import { useForm, FormProvider } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { Grid2 as Grid, Typography } from '@mui/material';
import dayjs from 'dayjs';
import * as yup from 'yup';

import { useBookConsultationStore } from '@/store/mrd/manage-patient/book-consultation';
import { useMrdPatientSearch } from '@/store/mrd/manage-patient/patient-search';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledTimePicker from '@/components/controlled-inputs/ControlledTimePicker';

import AppButton from '@/core/components/app-button';
import { convertToTimeObject } from '@/core/components/app-time-picker/utils';
import { Consultation } from '@/types/mrd/manage-patient/consultation';

type RescheduleFormValues = {
  date: string;
  time: string;
};

type Props = {
  appointment: Consultation;
  onSave: (date: string, time: string) => void;
  onCancel: () => void;
  isLoading: boolean;
};

const rescheduleSchema = yup.object().shape({
  date: yup.string().required('Date is required'),
  time: yup
    .string()
    .required('Time is required')
    .test('not-in-past-today', 'Time cannot be in the past', function (value) {
      const { date } = this.parent as { date?: string };
      if (!date || !value) return true;
      const isToday = dayjs(date).isSame(dayjs(), 'day');
      if (!isToday) return true;
      const now = new Date();
      const currentMinutes = now.getHours() * 60 + now.getMinutes();
      const [timePart, period] = value.split(' ');
      const [h, m] = timePart.split(':');
      let hour = parseInt(h, 10) % 12;
      if (period === 'PM') hour += 12;
      const minutes = hour * 60 + parseInt(m, 10);
      return minutes >= currentMinutes;
    }),
});

const RescheduleAppointmentForm: FC<Props> = ({
  appointment,
  onSave,
  onCancel,
  isLoading,
}) => {
  const { doctors } = useBookConsultationStore();
  const { patient } = useMrdPatientSearch();

  const doctor = doctors.find((doc) => doc.id === appointment.doctorId);

  const methods = useForm<RescheduleFormValues>({
    resolver: yupResolver(rescheduleSchema),
    defaultValues: {
      date: appointment.date,
      time: appointment.time,
    },
  });

  const { handleSubmit, control, watch } = methods;

  const selectedDate = watch('date');
  const selectedTime = watch('time');
  const isToday = selectedDate
    ? dayjs(selectedDate).isSame(dayjs(), 'day')
    : false;
  const now = new Date();
  const currentHour24 = now.getHours();
  const currentMinute = now.getMinutes();
  const currentPeriod = currentHour24 >= 12 ? 'PM' : 'AM';
  const selected = convertToTimeObject(selectedTime);
  const to24Hour = (hour12: number, period: string) => {
    const base = hour12 % 12;
    return period === 'PM' ? base + 12 : base;
  };

  const onSubmit = useCallback(
    (data: RescheduleFormValues) => {
      onSave(data.date, data.time);
    },
    [onSave]
  );

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2} rowSpacing={4} className="p-base">
          <Grid size={6}>
            <Typography variant="body2" color="textSecondary">
              Patient Name
            </Typography>
            <Typography variant="body1" className="font-semibold">
              {patient?.name || 'N/A'}
            </Typography>
          </Grid>
          <Grid size={6}>
            <Typography variant="body2" color="textSecondary">
              Doctor
            </Typography>
            <Typography variant="body1" className="font-semibold">
              {doctor?.name || 'N/A'}
            </Typography>
          </Grid>

          <Grid size={6}>
            <ControlledDatePicker
              label="Select Date"
              name="date"
              control={control}
              required
              minDate={dayjs()}
            />
          </Grid>
          <Grid size={6}>
            <ControlledTimePicker
              label="Select Time"
              name="time"
              control={control}
              required
              slotProps={
                isToday
                  ? {
                      periodSelect: {
                        isOptionDisabled: (opt) =>
                          currentPeriod === 'PM' && opt.value === 'AM',
                      },
                      hourSelect: {
                        isOptionDisabled: (opt) => {
                          if (!selected?.period) return false;
                          const hour12 = parseInt(opt.value, 10);
                          const optionHour24 = to24Hour(
                            hour12,
                            selected.period
                          );
                          if (selected.period !== currentPeriod) {
                            return (
                              currentPeriod === 'PM' && selected.period === 'AM'
                            );
                          }
                          return optionHour24 < currentHour24;
                        },
                      },
                      minuteSelect: {
                        isOptionDisabled: (opt) => {
                          if (!selected?.hour || !selected?.period)
                            return false;
                          const hour12 = parseInt(selected.hour, 10);
                          const h24 = to24Hour(hour12, selected.period);
                          if (selected.period !== currentPeriod) {
                            return (
                              currentPeriod === 'PM' && selected.period === 'AM'
                            );
                          }
                          if (h24 < currentHour24) return true;
                          if (h24 > currentHour24) return false;
                          const minute = parseInt(opt.value, 10);
                          return minute < currentMinute;
                        },
                      },
                    }
                  : undefined
              }
            />
          </Grid>
        </Grid>
        <div className="flex justify-end gap-base p-base">
          <AppButton
            variant="outlined"
            onClick={onCancel}
            disabled={isLoading}
            fullWidth
          >
            Cancel
          </AppButton>
          <AppButton type="submit" loading={isLoading} fullWidth>
            Save
          </AppButton>
        </div>
      </form>
    </FormProvider>
  );
};

export default memo(RescheduleAppointmentForm);
