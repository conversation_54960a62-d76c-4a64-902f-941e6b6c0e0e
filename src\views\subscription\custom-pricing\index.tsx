'use client';

import { useState, useEffect } from 'react';

import { useForm } from 'react-hook-form';

import { useRouter } from 'next/navigation';

import { useSubscriptionStore } from '@/store/subscription';

import { renderHTMLContent } from '@/utils/renderHTMLContent';
import {
  getSubscriptionUserData,
  clearSubscriptionUserData,
} from '@/utils/subscription';
import {
  emailPattern,
  mobileNumberPattern,
  restrictMobile,
} from '@/utils/validation';

import { AlertIcon } from '@/assets/svg/AlertIcon';

import ControlledTextArea from '@/components/controlled-inputs/ControlledTextArea';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import AppModal from '@/core/components/app-modal';
import { SuccessIcon } from '@/core/components/status-modal/modal-icons';

import BackArrow from '../components/BackArrow';
import PricingListHeader from '../components/PricingListHeader';
import SubscriptionLoading from '../components/SubscriptionLoading';

interface QuoteFormData {
  name: string;
  email: string;
  phone: string;
  organisation: string;
  designation: string;
  requirements: string;
}

const CustomPricingView = () => {
  const router = useRouter();
  const [_userData, setUserData] = useState<any>(null);
  const [_countdown, setCountdown] = useState(5);

  const {
    organizationPlan,
    isLoadingOrganizationPlan,
    fetchOrganizationPlan,
    submitQuote,
    isSubmittingQuote,
    showQuoteModal,
    showSuccessModal,
    showFailureModal,
    setShowQuoteModal,
    setShowSuccessModal,
    setShowFailureModal,
  } = useSubscriptionStore();

  const methods = useForm<QuoteFormData>({
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      organisation: '',
      designation: '',
      requirements: '',
    },
    mode: 'onTouched',
  });

  const { control, handleSubmit, reset, setValue } = methods;

  useEffect(() => {
    // Fetch organization plan data
    fetchOrganizationPlan();

    // Clear any existing success/failure modals when component mounts
    setShowSuccessModal(false);
    setShowFailureModal(false);

    // Get user data and auto-fill form
    const userData = getSubscriptionUserData();
    setUserData(userData);

    // Auto-fill name and email from signup data
    if (userData?.name) {
      setValue('name', userData.name);
    }
    if (userData?.email) {
      setValue('email', userData.email);
    }
  }, [
    router,
    fetchOrganizationPlan,
    setValue,
    setShowSuccessModal,
    setShowFailureModal,
  ]);

  const handleGetQuote = () => {
    setShowQuoteModal(true);
  };

  const handleModalClose = () => {
    setShowQuoteModal(false);
    reset();
  };

  const startCountdown = () => {
    setCountdown(5);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Close the success modal first
          setShowSuccessModal(false);
          setShowFailureModal(false);
          clearSubscriptionUserData();
          router.push('/subscription/signup');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const onSubmit = async (data: QuoteFormData) => {
    const success = await submitQuote({
      name: data.name,
      email: data.email,
      phoneNumber: data.phone,
      organizationName: data.organisation,
      designation: data.designation,
      requirements: data.requirements,
    });

    if (success) {
      reset();
      startCountdown();
    } else {
      startCountdown();
    }
  };

  // Show loading state
  if (isLoadingOrganizationPlan) {
    return <SubscriptionLoading />;
  }

  return (
    <div
      className="min-h-screen flex justify-center p-3 max-h-screen relative"
      style={{
        background: 'linear-gradient(to bottom, #E6F6FF, #B4E5FE)',
      }}
    >
      <div className="w-full h-full">
        <div className="mb-6 absolute left-0 top-0 p-4">
          <BackArrow />
        </div>

        <div className="pt-2">
          <div className="text-center mb-6">
            <PricingListHeader />
          </div>
        </div>

        <div className="bg-white min-h-[calc(100vh-8rem)] rounded-lg shadow-lg px-12 py-8 mx-8">
          <div className="mb-3">
            <div className="flex items-center justify-between">
              <div></div>
              <button
                onClick={handleGetQuote}
                disabled={isSubmittingQuote}
                style={{
                  backgroundColor: '#0496E1',
                  fontSize: '16px',
                  fontWeight: 400,
                  borderRadius: '16px',
                }}
                className="px-15 py-2 hover:opacity-90 disabled:bg-gray-400 disabled:cursor-not-allowed text-white transition-opacity duration-200"
              >
                {isSubmittingQuote ? 'Processing...' : 'Get Quote'}
              </button>
            </div>
          </div>

          <div>
            <div className="space-y-6 max-h-[60vh] overflow-y-auto">
              {/* Render HTML description from API */}
              {organizationPlan?.description && (
                <div className="prose prose-sm max-w-none">
                  {renderHTMLContent(organizationPlan.description)}
                </div>
              )}

              {/* Fallback content if no API data */}
              {!organizationPlan?.description && (
                <div>
                  <h3
                    style={{
                      fontSize: '24px',
                      fontWeight: 600,
                      color: '#012436',
                    }}
                    className="mb-4"
                  >
                    Customise according to your needs
                  </h3>
                  <h4
                    style={{
                      fontSize: '20px',
                      fontWeight: 600,
                      color: '#012436',
                    }}
                    className="mb-4"
                  >
                    Get a Quote
                  </h4>
                  <p
                    style={{
                      fontSize: '16px',
                      fontWeight: 400,
                      color: '#001926',
                      lineHeight: '24px',
                    }}
                    className="mb-6"
                  >
                    Our healthcare platform simplifies patient care &mdash; from
                    registration to consultation, prescription, and billing etc.
                    Choose modules that fit your organization&apos;s needs and
                    scale easily as you grow.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quote Request Modal */}
      <AppModal
        open={showQuoteModal}
        onClose={handleModalClose}
        title="Submit Response"
        classes={{
          root: 'w-[35vw] h-[95vh] min-h-[95vh] max-h-[95vh] flex flex-col',
          body: 'flex-1 overflow-auto',
        }}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col h-full"
        >
          <div className="bg-white rounded-lg px-4 flex-1 overflow-y-auto">
            <div className="space-y-1">
              <ControlledTextField
                name="name"
                control={control}
                label="Name"
                placeholder="Enter"
                fullWidth
                required
                rules={{ required: 'Required' }}
              />

              <ControlledTextField
                name="email"
                control={control}
                label="Email ID"
                type="email"
                placeholder="Enter"
                fullWidth
                required
                rules={{
                  required: 'Required',
                  pattern: emailPattern(),
                }}
              />

              <ControlledTextField
                name="phone"
                control={control}
                label="Phone Number"
                type="tel"
                placeholder="Enter"
                fullWidth
                required
                onKeyDown={restrictMobile(10)}
                rules={{
                  required: 'Required',
                  pattern: mobileNumberPattern(),
                }}
              />

              <ControlledTextField
                name="organisation"
                control={control}
                label="Organisation Name"
                placeholder="Enter"
                fullWidth
                required
                rules={{ required: 'Required' }}
              />

              <ControlledTextField
                name="designation"
                control={control}
                label="Designation"
                placeholder="Enter"
                fullWidth
                required
                rules={{ required: 'Required' }}
              />

              <ControlledTextArea
                name="requirements"
                control={control}
                label="Requirements"
                placeholder="Enter your requirements here..."
                rows={3}
                maxRows={6}
              />
            </div>
          </div>

          {/* Fixed Submit Button */}
          <div className="bg-white border-t border-gray-200 px-4 py-3 sticky bottom-0">
            <button
              type="submit"
              disabled={isSubmittingQuote}
              style={{
                backgroundColor: '#012436',
                color: 'white',
                width: '100%',
                padding: '12px',
                borderRadius: '8px',
                border: 'none',
                fontSize: '16px',
                fontWeight: 500,
                cursor: isSubmittingQuote ? 'not-allowed' : 'pointer',
                opacity: isSubmittingQuote ? 0.7 : 1,
              }}
            >
              {isSubmittingQuote ? 'Submitting...' : 'Submit'}
            </button>
          </div>
        </form>
      </AppModal>

      {/* Success Modal */}
      <AppModal
        open={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        classes={{
          root: 'w-[350px] !rounded-3xl relative',
          modal: '!rounded-3xl',
          header: 'hidden',
        }}
      >
        <div className="px-6 pb-4 pt-0 text-center">
          <div className="flex justify-center mb-4">
            <div className="scale-75">
              <SuccessIcon />
            </div>
          </div>
          <p className="text-base text-[#001926] mb-2">
            Response Submitted
            <br />
            Successfully!
            <br />
            <span className="text-sm text-gray-600">
              Our team will contact you shortly.
            </span>
          </p>
        </div>
      </AppModal>

      {/* Failure Modal */}
      <AppModal
        open={showFailureModal}
        onClose={() => setShowFailureModal(false)}
        classes={{
          root: 'w-[350px] !rounded-3xl relative',
          modal: '!rounded-3xl',
          header: 'hidden',
        }}
      >
        <div className="px-6 pb-4 pt-0 text-center">
          <div className="flex justify-center mb-4">
            <div className="scale-75">
              <AlertIcon />
            </div>
          </div>
          <p className="text-base text-[#001926] mb-2">
            Couldn&apos;t submit your response.
            <br />
            Please try again later.
          </p>
        </div>
      </AppModal>
    </div>
  );
};

export default CustomPricingView;
