import { FC, memo, useState } from 'react';

import { Controller } from 'react-hook-form';

import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  TableCellProps,
} from '@mui/material';

import { cn } from '@/lib/utils';

import AppIcon from '@/core/components/app-icon';
import {
  StyledTableContainerV2,
  StyledTableHeadV2,
} from '@/core/components/table-v2/styled-components';
import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

// Helper function to check if a field value is empty or unanswered
const isFieldEmpty = (value: any, field?: any): boolean => {
  if (value === null || value === undefined || value === '') {
    return true;
  }
  if (Array.isArray(value) && value.length === 0) {
    return true;
  }
  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return true;
  }

  // Check for "No information found" or similar ambient listening fallback values
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase().trim();
    if (
      lowerValue.includes('no information found') ||
      lowerValue.includes('not found') ||
      lowerValue.includes('no data') ||
      lowerValue.includes('unknown')
    ) {
      return true;
    }

    // If field has options, check if the value is not in the valid options
    if (field?.options && Array.isArray(field.options)) {
      const validOptions = field.options.map((opt: string) =>
        opt.toLowerCase().trim()
      );
      if (!validOptions.includes(lowerValue)) {
        return true;
      }
    }
  }

  return false;
};

// Helper function to get highlight styles for unanswered questions in ambient forms
const getHighlightStyles = (isAmbientForm: boolean, isEmpty: boolean) => {
  if (isAmbientForm && isEmpty) {
    return {
      color: '#FF742A',
    };
  }
  return {};
};

interface FrequencyTableFieldProps extends Partial<FieldComponentProps> {
  sections: FieldGroup[];
  control: any;
  readonly?: boolean;
  expandedSections?: string[];
  onExpandAll?: () => void;
  onCollapseAll?: () => void;
  onSectionToggle?: (sectionId: string) => void;
  isExpanded?: boolean;
  isAmbientForm?: boolean;
}

const Headers = {
  FOOD_ITEM: 'Food Item',
  DAILY: 'Daily',
  WEEKLY: 'Weekly (1,2,3,4,5)',
  MONTHLY: 'Monthly (1,2)',
  RARELY: 'Rarely',
  NEVER: 'Never',
} as const;

type Headers = (typeof Headers)[keyof typeof Headers];

const { DAILY, FOOD_ITEM, MONTHLY, NEVER, RARELY, WEEKLY } = Headers;

const commonStyling = { padding: '6px 12px', fontSize: '12px' };

const cellStyling: Record<string, TableCellProps> = {
  [FOOD_ITEM]: { sx: { ...commonStyling, minWidth: 120, maxWidth: 120 } },
  [DAILY]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
  [WEEKLY]: { sx: { ...commonStyling, minWidth: 160, maxWidth: 160 } },
  [MONTHLY]: { sx: { ...commonStyling, minWidth: 120, maxWidth: 120 } },
  [RARELY]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
  [NEVER]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
};

const FrequencyTableField: FC<FrequencyTableFieldProps> = ({
  sections,
  control,
  readonly,
  expandedSections: propExpandedSections,
  onSectionToggle,
  isExpanded = false,
  isAmbientForm,
}) => {
  const [internalExpandedSections, setInternalExpandedSections] = useState<
    string[]
  >(['cereals_grains']);

  const expandedSections = propExpandedSections ?? internalExpandedSections;

  const handleSectionToggle = (sectionId: string) => {
    if (onSectionToggle) {
      onSectionToggle(sectionId);
    } else {
      setInternalExpandedSections((prev) =>
        prev.includes(sectionId)
          ? prev.filter((id) => id !== sectionId)
          : [...prev, sectionId]
      );
    }
  };

  const firstField = sections[0]?.fields?.[0] as any;
  const frequencyColumns = firstField?.columns || [];

  const columnHeaders = [
    { key: 'food_item', label: 'Food Item' },
    ...frequencyColumns.map((col: any) => ({
      key: col.header,
      label: col.header,
      options: col.option || [],
    })),
  ];

  return (
    <div className={`w-full ${readonly ? 'readonly-form' : ''}`}>
      <style>
        {readonly &&
          `
          .readonly-form .MuiRadio-root.Mui-checked span svg[data-testid="RadioButtonCheckedIcon"] path,
          .readonly-form .MuiRadio-root.Mui-disabled.Mui-checked span svg[data-testid="RadioButtonCheckedIcon"] path {
            fill: #000000 !important;
            stroke: none !important;
            color: #000000 !important;
          }
          .readonly-form .MuiRadio-root.Mui-checked span svg[data-testid="RadioButtonCheckedIcon"],
          .readonly-form .MuiRadio-root.Mui-disabled.Mui-checked span svg[data-testid="RadioButtonCheckedIcon"] {
            filter: brightness(0) contrast(1.5) !important;
            color: #000000 !important;
          }
          .readonly-form .MuiRadio-root.Mui-checked,
          .readonly-form .MuiRadio-root.Mui-disabled.Mui-checked {
            color: #000000 !important;
          }
          .readonly-form .MuiRadio-root.Mui-checked .MuiSvgIcon-root,
          .readonly-form .MuiRadio-root.Mui-disabled.Mui-checked .MuiSvgIcon-root {
            filter: brightness(0) contrast(1.5) !important;
            color: #000000 !important;
          }
        `}
      </style>
      <StyledTableContainerV2
        sx={{
          ...(readonly && {
            '& .MuiTableHead-root .MuiTableCell-root': {
              backgroundColor: '#64707D',
            },
          }),
        }}
      >
        <Table size="small" stickyHeader>
          <StyledTableHeadV2>
            <TableRow>
              {columnHeaders.map((header) => (
                <TableCell
                  key={header.key}
                  align="center"
                  {...(cellStyling?.[header?.label] || {})}
                >
                  {header.label}
                </TableCell>
              ))}
            </TableRow>
          </StyledTableHeadV2>
        </Table>

        {sections.map((section, sectionIndex) => (
          <Accordion
            key={section.id}
            expanded={expandedSections.includes(section.id)}
            onChange={() => handleSectionToggle(section.id)}
            sx={{
              boxShadow: 'none',
              borderLeft: '1px solid #e5e7eb',
              borderRight: '1px solid #e5e7eb',
              borderBottom: '1px solid #e5e7eb',
              borderTop: 'none',
              backgroundColor: 'white',
              '&:before': { display: 'none' },
              '&.Mui-expanded': {
                margin: 0,
                borderLeft: '1px solid #e5e7eb',
                borderRight: '1px solid #e5e7eb',
                borderBottom: '1px solid #e5e7eb',
              },
            }}
            classes={{ heading: 'bg-transparent !h-12 !flex items-center' }}
          >
            <AccordionSummary
              expandIcon={
                <AppIcon icon="icon-park-outline:down" className="text-black" />
              }
              classes={{
                content: '',
                expanded: cn(
                  '!min-h-10 !h-10 !m-0 flex items-center ',
                  isExpanded ? '!bg-[#C2CDD6]' : '!bg-white'
                ),
                root: `min-h-10 !bg-white !m-0`,
                expandIconWrapper: 'min-h-10 flex items-center',
              }}
              sx={{
                '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
                  transform: 'rotate(180deg)',
                },
              }}
            >
              <Typography variant="subtitle1" fontWeight="medium">
                {section.title}
              </Typography>
            </AccordionSummary>

            <AccordionDetails classes={{ root: '!p-0' }}>
              <Table size="small">
                <TableBody>
                  {section.fields?.map((field, fieldIndex) => (
                    <TableRow key={field.id}>
                      {/* Food Item Column */}
                      <TableCell
                        {...cellStyling?.[FOOD_ITEM]}
                        align="left"
                        sx={{
                          ...(cellStyling?.[FOOD_ITEM]?.sx ?? {}),
                          p: '!0px',
                          backgroundColor: isExpanded
                            ? fieldIndex % 2 === 0
                              ? '#DAE1E7'
                              : 'white'
                            : fieldIndex % 2 === 0
                              ? '#E6F6FF'
                              : 'white',
                        }}
                      >
                        <div className="flex justify-start text-left">
                          <Controller
                            name={`questions.0.sections.${sectionIndex}.fields.${fieldIndex}.value`}
                            control={control}
                            render={({ field: controllerField }) => {
                              const currentValue =
                                controllerField.value ??
                                (field as any).value ??
                                '';
                              const isEmpty = isFieldEmpty(currentValue, field);
                              return (
                                <span
                                  style={{
                                    color:
                                      isAmbientForm && isEmpty
                                        ? '#FF742A'
                                        : 'inherit',
                                  }}
                                >
                                  {field.label}
                                </span>
                              );
                            }}
                          />
                        </div>
                      </TableCell>

                      {frequencyColumns.map((column: any, colIndex: number) => (
                        <TableCell
                          key={`${field.id}_${column.header}_${colIndex}`}
                          id={`${field.id}_${column.header}_${colIndex}`}
                          align="center"
                          {...cellStyling?.[column?.header]}
                          sx={{
                            ...(cellStyling?.[column?.header]?.sx ?? {}),
                            p: '!0px',
                            backgroundColor: isExpanded
                              ? fieldIndex % 2 === 0
                                ? '#DAE1E7'
                                : 'white'
                              : fieldIndex % 2 === 0
                                ? '#E6F6FF'
                                : 'white',
                          }}
                        >
                          <div className="flex gap-1 justify-center">
                            {column.option?.map((option: string) => (
                              <Controller
                                key={`${field.id}_${option}`}
                                name={`questions.0.sections.${sectionIndex}.fields.${fieldIndex}.value`}
                                control={control}
                                render={({ field: controllerField }) => {
                                  const isChecked =
                                    controllerField.value === option;

                                  return (
                                    <Radio
                                      value={option}
                                      checked={isChecked}
                                      size="small"
                                      onChange={(e) =>
                                        !readonly &&
                                        controllerField.onChange(e.target.value)
                                      }
                                      disableRipple={readonly}
                                      disableTouchRipple={readonly}
                                      disabled={readonly}
                                      sx={{
                                        ...(readonly && {
                                          padding: '0px',
                                          '& .MuiSvgIcon-root': {
                                            width: '16px',
                                            height: '16px',
                                            '& circle, & path': {
                                              strokeWidth: '1px',
                                            },
                                          },
                                          '&:hover': {
                                            backgroundColor: 'transparent',
                                          },
                                          ...(isChecked && {
                                            color: '#000000',
                                            '& .MuiSvgIcon-root': {
                                              color: '#000000',
                                              '& circle': {
                                                fill: 'none',
                                              },
                                              '& path': {
                                                fill: '#000000',
                                              },
                                            },
                                          }),
                                        }),
                                      }}
                                    />
                                  );
                                }}
                              />
                            ))}
                          </div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </AccordionDetails>
          </Accordion>
        ))}
      </StyledTableContainerV2>
    </div>
  );
};

export default memo(FrequencyTableField);
