'use client';

import { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import Loading from '@/lib/common/loading';

import { useUserStore } from '@/store/userStore';

import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

export default function MrdPage() {
  const router = useRouter();
  const { permissions = [] } = useUserStore();

  useEffect(() => {
    // Check if user has MRD dashboard permission
    const hasMrdDashboardView = permissions.includes(
      PERMISSION_KEYS.MRD_DASHBOARD_VIEW
    );

    if (hasMrdDashboardView) {
      router.replace(routes.MRD_DASHBOARD);
    } else {
      router.replace(routes.MRD_MANAGE_PATIENTS);
    }
  }, [router, permissions]);

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-gray-500">
        <Loading />
      </div>
    </div>
  );
}
