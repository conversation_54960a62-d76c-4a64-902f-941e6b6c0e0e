import React from 'react';

interface Props {
  title: string;
  description?: string;
  colSpan?: string;
  minHeight?: string;
  embedUrl?: string;
  hideRedashFooter?: boolean;
}

const ChartPlaceholder: React.FC<Props> = ({
  title,
  description = 'Chart will be embedded here',
  colSpan = 'col-span-1',
  minHeight = '300px',
  embedUrl,
  hideRedashFooter = false,
}) => {
  return (
    <div
      className={`${colSpan} bg-white-100 rounded-lg border border-[#DAE1E7]  relative flex flex-col`}
      style={{ borderRadius: '8px', minHeight }}
    >
      {embedUrl ? (
        <div className="flex-1 relative overflow-hidden">
          <iframe
            src={embedUrl}
            className="w-full h-full rounded border-0"
            title={title}
            allowFullScreen
            style={{
              minHeight: minHeight,
              border: 'none',
              width: '100%',
              height: '100%',
            }}
          />
          {hideRedashFooter && (
            <div
              className="absolute bottom-0 left-0 right-0 bg-[#f7f8f9] z-50 pointer-events-none"
              style={{ height: '100px' }}
            />
          )}
        </div>
      ) : (
        <div className="text-center text-gray-500 flex items-center justify-center flex-1">
          <div>
            <h3 className="text-lg font-medium mb-2">{title}</h3>
            <p className="text-sm">{description}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChartPlaceholder;
