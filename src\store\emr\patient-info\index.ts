import { toast } from 'sonner';
import { create } from 'zustand';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import { getPatient, getPatientVitals } from '@/query/patient';

import { PatientInfo, PatientInfoVitals } from '@/types/emr/patient-info';

type PatientInfoState = {
  patient: PatientInfo | null;
  vitals: PatientInfoVitals[];
  isSearching: boolean;
  isError: boolean;
  isLoadingVitals: boolean;
};

type PatientInfoAction = {
  setVitals: (vitals: PatientInfoVitals) => void;
  setPatient: (patient: PatientInfo | null) => void;
  selectPatient: (id: string) => Promise<PatientInfo | null>;
  getPatientVitals: (id: string | undefined) => Promise<PatientInfoVitals[]>;
};

type PatientInfoStore = PatientInfoState & PatientInfoAction;

const initialState: PatientInfoState = {
  patient: useCurrentPatientStore.getState().patient,
  vitals: [],
  isSearching: false,
  isError: false,
  isLoadingVitals: false,
};

export const usePatientInfoStore = create<PatientInfoStore>((set) => ({
  ...initialState,
  setPatient: (patient) => set({ patient }),
  setVitals: (vitals) =>
    set((state) => ({ vitals: [...state.vitals, vitals] })),

  selectPatient: async (id) => {
    try {
      set({ isError: false, isSearching: true, isLoadingVitals: true });
      const response = await getPatient(id);

      if (!response.data) {
        toast.error('Patient not found');
        set({ isSearching: false, isLoadingVitals: false });
        return null;
      }

      try {
        const vitalsResponse = await getPatientVitals(id);
        set({
          patient: response.data,
          vitals: vitalsResponse.data?.reverse() || [],
          isSearching: false,
          isLoadingVitals: false,
        });
      } catch (vitalsError) {
        console.error('Error fetching vitals:', vitalsError);

        set({
          patient: response.data,
          vitals: [],
          isSearching: false,
          isLoadingVitals: false,
        });
        toast.error('Patient loaded but vitals could not be fetched');
      }

      return response.data;
    } catch (err) {
      set({ isError: true, isSearching: false, isLoadingVitals: false });

      console.error(err);

      toast.error('There was an issue getting the selected patient');
    }
  },
  getPatientVitals: async (id) => {
    try {
      set({ isLoadingVitals: true });

      if (!id) {
        set({ isLoadingVitals: false });
        return [];
      }
      const { data } = await getPatientVitals(id);
      set({ vitals: data?.reverse(), isLoadingVitals: false });
      return data;
    } catch (err) {
      console.error(err);
      set({ isLoadingVitals: false });
      toast.error('Error fetching patient data');
    }
  },
}));
