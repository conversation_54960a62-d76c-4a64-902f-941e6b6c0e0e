'use client';

import { memo, useCallback, useEffect, useState } from 'react';

import { usePathname, useRouter } from 'next/navigation';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { getModuleSwitchUrl } from '@/utils/switch-module';

import { routes } from '@/constants/routes';

import { logout } from '@/core/lib/auth/services';

import MenuButton from './MenuButton';
import Menu from './MenuOption';

const UserMenu = () => {
  const { push } = useRouter();
  const pathname = usePathname();

  const { fetchDoctorProfileByEmail, doctorProfile } = useDoctorStore();
  const {
    name,
    setUser,
    setUserData,
    data: userData,
    fetchUser,
    permissions = [],
    fetchUserPermissions,
  } = useUserStore();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const open = Boolean(anchorEl);

  const isEMR = pathname.includes('emr');

  const handleClick = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleSwitchModule = useCallback(async () => {
    try {
      const freshPermissions = await fetchUserPermissions();
      const switchUrl = getModuleSwitchUrl(pathname, freshPermissions);
      push(switchUrl);
      handleClose();
    } catch (error) {
      console.error('Error switching modules:', error);

      push(getModuleSwitchUrl(pathname, permissions));
      handleClose();
    }
  }, [push, pathname, permissions, handleClose, fetchUserPermissions]);

  const handleLogout = useCallback(() => {
    logout();
    setUser('');
    setUserData(null);
    push(routes.LOGIN);
  }, [push, setUser, setUserData]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  useEffect(() => {
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData.email);
    }
  }, [userData?.email, fetchDoctorProfileByEmail]);

  return (
    <>
      <MenuButton
        open={open}
        handleClick={handleClick}
        name={name}
        userData={userData}
        doctorProfile={doctorProfile}
      />
      <Menu
        open={open}
        anchorEl={anchorEl}
        handleClose={handleClose}
        handleSwitchModule={handleSwitchModule}
        handleLogout={handleLogout}
        isEMR={isEMR}
      />
    </>
  );
};

export default memo(UserMenu);
