import {
  Faro,
  getWebInstrumentations,
  initializeFaro,
  LogLevel,
} from '@grafana/faro-web-sdk';
import { TracingInstrumentation } from '@grafana/faro-web-tracing';

let faro: Faro | null = null;

export const initializeObservability = (): void => {
  // Only run in browser
  if (typeof window === 'undefined') {
    return;
  }

  // Prevent multiple initializations
  if (faro) {
    return;
  }

  // Get environment variables (will be set in Vercel)
  const faroUrl = process.env.NEXT_PUBLIC_GRAFANA_FARO_URL;
  const appName = process.env.NEXT_PUBLIC_APP_NAME || 'arca-emr';
  const environment = process.env.NEXT_PUBLIC_APP_ENV || 'production';
  const appVersion = process.env.NEXT_PUBLIC_APP_VERSION || '0.1.0';

  if (!faroUrl) {
    console.log(
      'Grafana Faro URL not configured. Observability will be disabled.'
    );
    return;
  }

  try {
    faro = initializeFaro({
      url: faroUrl,
      app: {
        name: 'arca-emr',
        version: '0.1.0',
        environment: 'production',
      },
      instrumentations: [
        // Enable default web instrumentations (console, errors, fetch, xhr, web vitals)
        ...getWebInstrumentations(),

        // Enable tracing with default settings
        new TracingInstrumentation(),
      ],
    });

    // Add user context when available
    if (faro.metas.value.user?.email) {
      faro.api.setUser({
        email: faro.metas.value.user.email,
        // Add other user properties as needed
      });
    }

    // Log initialization
    faro.api.pushLog(['Faro initialized'], {
      context: { module: 'observability' },
      level: LogLevel.INFO,
    });
  } catch (error) {
    console.error('Failed to initialize Faro:', error);
  }
};

// Helper function to get Faro instance
export const getFaro = (): Faro | null => {
  return faro;
};

// Example usage of custom events
export const trackEvent = (
  name: string,
  properties: Record<string, string> = {}
): void => {
  if (!faro) return;

  // Convert all property values to strings to ensure type safety
  const stringProperties: Record<string, string> = {};
  Object.entries(properties).forEach(([key, value]) => {
    stringProperties[key] = String(value);
  });

  faro.api.pushEvent(name, stringProperties);
};

import { trace, SpanStatusCode } from '@opentelemetry/api';

// Example of manual tracing
export const withTrace = async <T>(
  name: string,
  callback: () => Promise<T>,
  attributes?: Record<string, string>
): Promise<T> => {
  if (!faro) {
    return callback();
  }

  // Get the active tracer
  const tracer = trace.getTracer('faro-tracer');

  // Start a new span
  return tracer.startActiveSpan(name, async (span) => {
    try {
      // Add custom attributes to the span if provided
      if (attributes) {
        Object.entries(attributes).forEach(([key, value]) => {
          span.setAttribute(key, value);
        });
      }

      const result = await callback();
      span.setStatus({ code: SpanStatusCode.OK });
      return result;
    } catch (error) {
      // Record the error on the span
      span.recordException(error as Error);
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: (error as Error).message,
      });
      throw error;
    } finally {
      // Always end the span when done
      span.end();
    }
  });
};

// Export a type-safe Faro instance
export type { Faro };

export default initializeObservability;
