import { FC, memo } from 'react';

import { IoMdClipboard } from 'react-icons/io';

import RenderFields from '@/views/emr/lifestyle/shared/render-fields';

import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

type Props = {
  formFields: FieldGroup[];
  readonly?: boolean;
  showHeading?: boolean;
  isAmbientForm?: boolean;
};

const KnowledgeForm: FC<Props> = ({ formFields, readonly, isAmbientForm }) => {
  if (!formFields || formFields.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No knowledge data available
      </div>
    );
  }

  return (
    <div
      className={`space-y-6 p-4 ${readonly ? 'pointer-events-none readonly-form' : ''}`}
    >
      <div className="flex items-center space-x-2 mb-6  border-gray-200 pb-2">
        <IoMdClipboard />
        <h4 className="text-lg font-semibold">
          Nutrition Knowledge Questionnaire
        </h4>
      </div>
      {formFields?.map((section, index) => (
        <div key={section.id || index} className="space-y-6">
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <RenderFields
              fields={section?.fields}
              namePrefix={`questions.${index}.fields`}
              readonly={readonly}
              groupClassName="space-y-0"
              isAmbientForm={isAmbientForm}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(KnowledgeForm);
