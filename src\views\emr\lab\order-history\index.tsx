import React, { useCallback, useEffect, useMemo, useState } from 'react';

import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
dayjs.extend(isBetween);

import { usePaymentPermissions } from '@/hooks/usePaymentPermissions';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useOrderHistoryFilterStore } from '@/store/emr/lab/order-history-filter';
import { useTestStore } from '@/store/emr/lab/reports-store';
import { usePaymentStore } from '@/store/payments';
import { useUserStore } from '@/store/userStore';

import { updateLabTest } from '@/query/emr/lab';

import { filterBy } from '@/utils/filter-by-util';

import PaymentFailureModal from '@/views/mrd/payment/PaymentFailureModal';

import Loading from '@/app/loading';
import AppModal from '@/core/components/app-modal';
import { TestResultItem, LabTest, OrderHistoryItem } from '@/types/emr/lab';

import LabTestPaymentConfirmationModal from '../new-test/LabTestPaymentConfirmationModal';
import LabTestPaymentSuccessModal from '../new-test/LabTestPaymentSuccessModal';

import OrderHistoryPrintContent from './components/OrderHistoryPrintContent';
import OrderHistoryTable from './components/OrderHistoryTable';
import PayButton from './components/PayButton';
import PreviewButton from './components/PreviewButton';

const OrderHistory: React.FC = () => {
  const {
    department,
    searchText,
    dateFilter,
    customStartDate,
    customEndDate,
    sortField,
    sortOrder,
    onSort,
  } = useOrderHistoryFilterStore();

  const patient = useCurrentPatientStore((state) => state.patient);
  const { testResult, isLoading, fetchPatientTestResult } = useTestStore();
  const { data: userData } = useUserStore();
  const { isPaymentEnabled } = usePaymentPermissions();

  const { createOrder, openRazorpayPayment, resetPaymentState } =
    usePaymentStore();

  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showPaymentConfirmationModal, setShowPaymentConfirmationModal] =
    useState(false);
  const [showPaymentSuccessModal, setShowPaymentSuccessModal] = useState(false);
  const [showFailureModal, setShowFailureModal] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const fetchOrderHistory = useCallback(() => {
    if (!patient) return;

    fetchPatientTestResult({
      patientId: patient.id,
      sortField,
      sortOrder,
      dateFilter: dateFilter || filterBy.ALL,
      customStartDate: customStartDate
        ? dayjs(customStartDate).format('YYYY-MM-DD')
        : undefined,
      customEndDate: customEndDate
        ? dayjs(customEndDate).format('YYYY-MM-DD')
        : undefined,
      department: department
        ? department === 'ALL'
          ? 'ALL'
          : department
        : 'ALL',
      searchText,
    });
  }, [
    fetchPatientTestResult,
    patient,
    sortField,
    sortOrder,
    dateFilter,
    customStartDate,
    customEndDate,
    department,
    searchText,
  ]);

  useEffect(() => {
    fetchOrderHistory();
  }, [fetchOrderHistory]);

  const orders = useMemo<OrderHistoryItem[]>(() => {
    if (!testResult) return [];
    return testResult.flatMap((result: TestResultItem) =>
      Object.values(result.labTests).flatMap((testArray) =>
        testArray.map((test: LabTest) => ({
          id: `${result.id}~${test.testName}`,
          testId: test.testId,
          date: dayjs(result.created_on).format('YYYY-MM-DD'),
          department: test.department,
          testName: test.testName,
          amount: String(test.cost),
          status: test.status,
        }))
      )
    );
  }, [testResult]);

  const handleSelectItem = (id: string) => {
    setSelectedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedItems(orders.map((item) => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectGroup = (_groupId: string, testIds: string[]) => {
    const isGroupSelected = testIds.every((id) => selectedItems.includes(id));

    if (isGroupSelected) {
      setSelectedItems((prev) => prev.filter((id) => !testIds.includes(id)));
    } else {
      const uniqueItems = Array.from(new Set([...selectedItems, ...testIds]));
      setSelectedItems(uniqueItems);
    }
  };

  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const handlePayClick = () => {
    setShowPaymentConfirmationModal(true);
  };

  const handlePaymentConfirmationClose = () => {
    setShowPaymentConfirmationModal(false);
  };

  const handlePaymentSuccessClose = useCallback(() => {
    setShowPaymentSuccessModal(false);
    resetPaymentState();

    fetchOrderHistory();
  }, [resetPaymentState, fetchOrderHistory]);

  const handlePaymentFailureClose = () => {
    setShowFailureModal(false);
    resetPaymentState();
  };

  const totalCost = useMemo(() => {
    return orders
      .filter(
        (item: OrderHistoryItem) =>
          selectedItems.includes(item.id) && item.status !== 'Paid'
      )
      .reduce((sum, item) => sum + parseFloat(item.amount || '0'), 0)
      .toFixed(2);
  }, [orders, selectedItems]);

  const selectedOrders = useMemo(() => {
    return orders.filter((item: OrderHistoryItem) =>
      selectedItems.includes(item.id)
    );
  }, [orders, selectedItems]);

  const allSelectedArePaid = useMemo(() => {
    if (selectedItems.length === 0) return false;
    return selectedOrders.every((order) => order.status === 'Paid');
  }, [selectedOrders, selectedItems.length]);

  const handleProceedToPay = useCallback(async () => {
    if (!patient || !userData?.organizationId || selectedItems.length === 0) {
      return;
    }

    try {
      setIsProcessingPayment(true);

      const paymentData = {
        amount: parseFloat(totalCost),
        currency: 'INR',
        paymentType: 'lab_test',
        organizationId: userData.organizationId,
        patientId: patient.id,
        description: 'Lab Test Payment',
        metadata: { selectedItems },
      };

      const order = await createOrder(paymentData);

      if (!order) {
        throw new Error('Failed to create payment order');
      }

      // Extract paymentId from order creation response
      const orderPaymentId = order.data.paymentId;

      setIsProcessingPayment(false);
      setShowPaymentConfirmationModal(false);

      await openRazorpayPayment(
        order,
        patient.name || 'Patient',
        async (razorpayResponse) => {
          setIsProcessingPayment(false);

          try {
            const today = new Date().toISOString().split('T')[0];

            const formattedLabTests = selectedOrders
              .filter((order) => order.status !== 'Paid')
              .map((order) => ({
                testName: order.testName,
                qty: '1',
                instructions: '',
                cost: order.amount,
                toBeDoneBy: '',
                date: today,
                testId: String(order.testId),
                results: null,
                reference: null,
                status: 'Paid',
              }));

            const actualId = selectedOrders[0]?.id?.split('~')[0] || '';

            const payload = {
              id: actualId,
              patientId: patient?.id,
              labTests: formattedLabTests,
              // Use paymentId from order creation response
              paymentId: orderPaymentId,
            };

            await updateLabTest(payload);

            setSelectedItems([]);
            fetchOrderHistory();

            setShowPaymentSuccessModal(true);
          } catch (error) {
            console.error(' Error updating lab test status:', error);
            setShowFailureModal(true);
          }
        },
        (error) => {
          console.error('Payment error:', error);

          setIsProcessingPayment(false);
          setShowFailureModal(true);
        }
      );
    } catch (error) {
      console.error('Error in payment process:', error);
      setIsProcessingPayment(false);
      setShowPaymentConfirmationModal(false);
      setShowFailureModal(true);
    }
  }, [
    patient,
    userData?.organizationId,
    selectedItems,
    totalCost,
    createOrder,
    openRazorpayPayment,
    selectedOrders,
    fetchOrderHistory,
  ]);

  if (isLoading) {
    return (
      <div className="flex h-full items-start justify-center ">
        <Loading />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-grow overflow-y-auto p-4">
        <OrderHistoryTable
          items={orders}
          onSort={onSort}
          sortField={sortField}
          sortOrder={sortOrder}
          selectedItems={selectedItems}
          onSelectItem={handleSelectItem}
          onSelectAll={handleSelectAll}
          onSelectGroup={handleSelectGroup}
        />
      </div>
      <div className="flex-shrink-0 bg-white p-4 border-t flex justify-between items-center">
        <div>
          <span className=" text-sm border px-10 py-3 rounded-md font-archivo">
            Total Cost: ₹{totalCost}
          </span>
        </div>
        <div className="flex items-center gap-4">
          <AppModal
            open={showPrintModal}
            onClose={() => setShowPrintModal(false)}
            title="Print Preview "
            titleClass="font-normal"
            classes={{
              root: 'w-[60vw] h-[90vh]',
              body: 'p-0 h-[80vh]',
              backdrop: '!opacity-20',
            }}
          >
            <OrderHistoryPrintContent
              selectedOrders={selectedOrders}
              onClose={() => setShowPrintModal(false)}
            />
          </AppModal>
          <PreviewButton
            onClick={handlePrint}
            disabled={selectedItems.length === 0}
          />
          {isPaymentEnabled('labTestEnabled') && (
            <PayButton
              onClick={handlePayClick}
              disabled={selectedItems.length === 0 || allSelectedArePaid}
            />
          )}
        </div>
      </div>

      <LabTestPaymentConfirmationModal
        open={showPaymentConfirmationModal}
        onClose={handlePaymentConfirmationClose}
        onOrderAndPrint={() => {}}
        onProceedToPay={handleProceedToPay}
        loading={isProcessingPayment}
        totalCost={`₹${totalCost}`}
        hideOrderAndPrint={true}
        title="Payment Confirmation"
      />

      <LabTestPaymentSuccessModal
        open={showPaymentSuccessModal}
        onClose={handlePaymentSuccessClose}
      />

      <PaymentFailureModal
        open={showFailureModal}
        onClose={handlePaymentFailureClose}
        onRetry={() => setShowPaymentConfirmationModal(true)}
        errorMessage="Payment failed. Please try again."
        showCloseButton={false}
      />
    </div>
  );
};

export default OrderHistory;
