'use client';

import React, { FC, useCallback, useEffect, useRef, useState } from 'react';

import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { BiX } from 'react-icons/bi';
import { TbPlayerPause, TbPlayerPlay } from 'react-icons/tb';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';
import { getSpeechToken } from '@/query/speech';

import {
  throttledUpdateLastActivity,
  setRecordingActive,
} from '@/utils/session';

import SineWaves from '@/helpers/sine-waves/sine-waves';

import {
  recordingStates,
  RecordingState,
  waves,
  languageOptions,
  CurrentModal,
  currentModal,
} from '@/constants/ambient-listening';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import PrimaryButton from '@/core/components/primary-button';

interface RecordConsultationProps {
  engine: speechsdk.ConversationTranscriber | null;
  onRecordEnd: (transcript: string, duration: number) => void;
  handleChooseLanguage: (newLanguage: string) => void;
  selectedLanguage: string;
  currentMode: CurrentModal;
  setCurrentMode: (mode: CurrentModal) => void;
  onCancel?: () => void;
  isLanguageSelection?: boolean;
}

const { IDLE, PAUSED, RECORDING } = recordingStates;
const { LANGUAGE_SELECTION, RECORD_CONSULTATION } = currentModal;

const PERIODIC_ACTION_INTERVAL = 5 * 60 * 1000;
const ROLLING_RESTART_INTERVAL = 29 * 60 * 1000;

const RecordConsultation: FC<RecordConsultationProps> = ({
  engine,
  onRecordEnd,
  handleChooseLanguage,
  selectedLanguage,
  currentMode,
  setCurrentMode,
  onCancel,
  isLanguageSelection = false,
}) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const rollingRestartRef = useRef<NodeJS.Timeout | null>(null);
  const recognizerRef = useRef<speechsdk.ConversationTranscriber | null>(null);
  const audioConfigRef = useRef<speechsdk.AudioConfig | null>(null);

  const [recordingState, setLocalRecordingState] =
    useState<RecordingState>(IDLE);
  const [wavesConfig, setWavesConfig] = useState({
    speed: 0,
    wavesWidth: '0',
  });

  const [currentOffset, setCurrentOffset] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [recordingStartTime, setRecordingStartTime] = useState<number | null>(
    null
  );
  const [recordingPeriods, setRecordingPeriods] = useState<
    Array<{ start: number; end: number | null }>
  >([]);
  const [currentRecordingDuration, setCurrentRecordingDuration] = useState(0);

  const [transcriptHistory, setTranscriptHistory] = useState(['']);
  const [transcript, setTranscript] = useState('');
  const [tEvent, setTvent] = useState(null);

  const transcriptEl = useRef(null);
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isListeningRef = useRef<boolean>(false);
  const selectedLanguageRef = useRef<string>(selectedLanguage);
  const isRestartingRef = useRef<boolean>(false);
  const reinitRef = useRef<(language: string) => void>(() => {});

  const wavesEl = useRef(null);

  // **ENHANCED: Function to properly dispose audio resources with better error handling**
  const disposeAudioResources = useCallback(async () => {
    try {
      const activeRecognizer = recognizerRef.current || engine;

      // First, ensure transcription is fully stopped
      if (activeRecognizer && isListeningRef.current) {
        await new Promise<void>((resolve) => {
          const timeout = setTimeout(() => {
            console.warn('Stop transcribing timeout, forcing cleanup');
            resolve();
          }, 2000); // 2 second timeout

          activeRecognizer.stopTranscribingAsync(
            () => {
              clearTimeout(timeout);
              resolve();
            },
            () => {
              clearTimeout(timeout);
              resolve();
            }
          );
        });
        // Give it a moment to fully stop
        await new Promise((resolve) => setTimeout(resolve, 200));
      }

      // Close the recognizer
      if (activeRecognizer) {
        try {
          activeRecognizer.close();
        } catch (error) {
          console.warn('Error closing recognizer:', error);
        }
      }

      // Close the audio config to release microphone
      if (audioConfigRef.current) {
        try {
          audioConfigRef.current.close();
          audioConfigRef.current = null;
        } catch (error) {
          console.warn('Error closing audio config:', error);
        }
      }

      // **REMOVED: Camera access - not needed for audio recording**
      // This was causing unnecessary resource usage

      recognizerRef.current = null;
      isListeningRef.current = false;

      // Clear any pending timeouts
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
        silenceTimeoutRef.current = null;
      }

      // Clear rolling restart timer
      clearRollingRestartTimer();
    } catch (error) {
      console.error('Error disposing audio resources:', error);
    }
  }, [engine]);

  const handlePeriodicAction = useCallback(() => {
    getDoctorProfile();
  }, []);

  const handleTranscribing = useCallback(
    (s: any, e: any) => {
      try {
        throttledUpdateLastActivity();
        if (e && e.privResult && e.privResult.privJson) {
          const event = JSON.parse(e.privResult.privJson);

          // Use requestAnimationFrame for smooth scrolling to prevent UI blocking
          if (transcriptEl.current) {
            requestAnimationFrame(() => {
              if (transcriptEl.current) {
                (transcriptEl.current as HTMLDivElement).scrollTop = (
                  transcriptEl.current as HTMLDivElement
                ).scrollHeight;
              }
            });
          }

          setTvent(() => event);

          setIsSpeaking(true);
          if (silenceTimeoutRef.current) {
            clearTimeout(silenceTimeoutRef.current);
          }

          silenceTimeoutRef.current = setTimeout(() => {
            setIsSpeaking(false);
          }, 600);
        }
      } catch (error) {
        console.error('Error in handleTranscribing:', error);
        // Continue execution even if there's an error
      }
    },
    [transcriptEl, throttledUpdateLastActivity]
  );

  // **FIXED: Properly clean up resources when canceling**
  const _handleCancelRecord = async () => {
    // Stop listening immediately
    setIsListening(false);
    isListeningRef.current = false;

    const activeRecognizer = recognizerRef.current || engine;
    if (activeRecognizer) {
      await new Promise<void>((resolve) => {
        activeRecognizer.stopTranscribingAsync(
          () => resolve(),
          () => resolve()
        );
      });
    }

    // Wait a bit before disposing
    await new Promise((resolve) => setTimeout(resolve, 200));
    await disposeAudioResources();

    setLocalRecordingState(IDLE);
    setIsSpeaking(false);
    setRecordingStartTime(null);
    setRecordingPeriods([]);
    setCurrentRecordingDuration(0);
    setTranscript('');
    setTranscriptHistory(['']);

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (rollingRestartRef.current) {
      clearTimeout(rollingRestartRef.current);
      rollingRestartRef.current = null;
    }
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }
    onCancel?.();
  };

  const clearRollingRestartTimer = () => {
    if (rollingRestartRef.current) {
      clearTimeout(rollingRestartRef.current);
      rollingRestartRef.current = null;
    }
  };

  function scheduleRollingRestart() {
    clearRollingRestartTimer();
    rollingRestartRef.current = setTimeout(() => {
      if (isListeningRef.current) {
        reinitializeRecognizer(selectedLanguageRef.current);
      }
    }, ROLLING_RESTART_INTERVAL);
  }

  // **FIXED: Properly dispose old recognizer before creating new one**
  async function reinitializeRecognizer(language: string) {
    try {
      if (isRestartingRef.current) return;
      isRestartingRef.current = true;

      const current = recognizerRef.current || engine;
      if (current) {
        await new Promise<void>((resolve) => {
          current.stopTranscribingAsync(
            () => {
              // **NEW: Close old recognizer properly**
              current.close();
              resolve();
            },
            () => {
              current.close();
              resolve();
            }
          );
        });
      }

      // **NEW: Close old audio config**
      if (audioConfigRef.current) {
        audioConfigRef.current.close();
      }

      const token = await getSpeechToken();
      if (!token) throw new Error('Failed to get speech token');

      const speechConfig = speechsdk.SpeechConfig.fromAuthorizationToken(
        token,
        'eastus'
      );
      speechConfig.speechRecognitionLanguage = language;

      // **NEW: Store audio config reference**
      audioConfigRef.current =
        speechsdk.AudioConfig.fromDefaultMicrophoneInput();
      const newRecognizer = new speechsdk.ConversationTranscriber(
        speechConfig,
        audioConfigRef.current
      );

      recognizerRef.current = newRecognizer;
      attachHandlers(newRecognizer);

      if (isListeningRef.current) {
        newRecognizer.startTranscribingAsync(
          () => {
            scheduleRollingRestart();
            isRestartingRef.current = false;
          },
          (error) => {
            console.error('Error starting new recognizer:', error);
            isRestartingRef.current = false;
            setTimeout(() => {
              if (isListeningRef.current) reinitializeRecognizer(language);
            }, 3000);
          }
        );
      } else {
        isRestartingRef.current = false;
      }
    } catch (e) {
      console.error('Failed to reinitialize recognizer:', e);
      isRestartingRef.current = false;
      setTimeout(() => {
        if (isListeningRef.current) reinitializeRecognizer(language);
      }, 3000);
    }
  }

  useEffect(() => {
    reinitRef.current = (lang: string) => {
      reinitializeRecognizer(lang);
    };
  }, [reinitializeRecognizer]);

  const attachHandlers = React.useCallback(
    (rec: speechsdk.ConversationTranscriber | null) => {
      if (!rec) return;
      rec.transcribing = handleTranscribing;
      rec.sessionStopped = () => {
        if (isListeningRef.current && !isRestartingRef.current) {
          reinitRef.current(selectedLanguageRef.current);
        }
      };
      rec.canceled = (_s: any, e: any) => {
        console.warn('Transcription canceled', e?.errorDetails || '');
        if (isListeningRef.current && !isRestartingRef.current) {
          reinitRef.current(selectedLanguageRef.current);
        }
      };
    },
    [handleTranscribing]
  );

  useEffect(() => {
    if (engine) {
      recognizerRef.current = engine;
      attachHandlers(engine);
    }

    // **ENHANCED: Comprehensive cleanup on unmount**
    return () => {
      // Reset recording state on unmount
      setRecordingActive(false);
      setTranscript('');
      setTranscriptHistory(['']);
      setRecordingStartTime(null);
      setRecordingPeriods([]);
      setCurrentRecordingDuration(0);

      // Stop listening immediately
      isListeningRef.current = false;

      const cleanup = async () => {
        try {
          const activeRecognizer = recognizerRef.current || engine;
          if (activeRecognizer) {
            await new Promise<void>((resolve) => {
              const timeout = setTimeout(() => {
                console.warn('Cleanup timeout, forcing resolution');
                resolve();
              }, 3000); // 3 second timeout for cleanup

              activeRecognizer.stopTranscribingAsync(
                () => {
                  clearTimeout(timeout);
                  resolve();
                },
                () => {
                  clearTimeout(timeout);
                  resolve();
                }
              );
            });
            await new Promise((resolve) => setTimeout(resolve, 300));
          }
          await disposeAudioResources();
        } catch (error) {
          console.error('Error during cleanup:', error);
        }
      };

      cleanup();

      // Clear all intervals and timeouts
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      clearRollingRestartTimer();
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
        silenceTimeoutRef.current = null;
      }

      // Cleanup sine waves
      if (sineWavesRef.current) {
        sineWavesRef.current.running = false;
        sineWavesRef.current = null;
      }
    };
  }, [engine, handleTranscribing, attachHandlers, disposeAudioResources]);

  useEffect(() => {
    isListeningRef.current = isListening;
  }, [isListening]);

  useEffect(() => {
    selectedLanguageRef.current = selectedLanguage;
  }, [selectedLanguage]);

  // Optimized transcript updates with throttling to prevent UI blocking
  useEffect(() => {
    if (tEvent) {
      const { Type, Offset, Text } = tEvent;
      if (Type === 'ConversationTranscription') {
        // Use requestAnimationFrame to prevent blocking the main thread
        requestAnimationFrame(() => {
          setTranscript(() => {
            if (Offset !== currentOffset) {
              const newHist = [...transcriptHistory, transcript];
              setTranscriptHistory(newHist);
              setCurrentOffset(Offset);
            }
            return Text;
          });
        });
      }
    }
  }, [tEvent]);

  const toggleRecord = () => {
    const activeRecognizer = recognizerRef.current || engine;
    if (!activeRecognizer) {
      console.error('Cannot toggle recording: Speech engine not initialized');
      return;
    }

    if (isListening) {
      // Stop recording
      setIsListening(false);
      clearRollingRestartTimer();
      setRecordingActive(false);

      setRecordingPeriods((prev) => {
        const newPeriods = [...prev];
        if (
          newPeriods.length > 0 &&
          newPeriods[newPeriods.length - 1].end === null
        ) {
          newPeriods[newPeriods.length - 1].end = Date.now();
        }
        return newPeriods;
      });

      activeRecognizer.stopTranscribingAsync(
        () => {
          setLocalRecordingState(PAUSED);
        },
        (error) => {
          console.error('Error stopping transcription:', error);
          setLocalRecordingState(PAUSED);
        }
      );
    } else {
      // Start recording
      setIsListening(true);
      setRecordingActive(true);
      setRecordingPeriods((prev) => [
        ...prev,
        { start: Date.now(), end: null },
      ]);
      activeRecognizer.startTranscribingAsync(
        () => {
          setLocalRecordingState(RECORDING);
          scheduleRollingRestart();
        },
        (error) => {
          console.error('Error starting transcription:', error);
          setIsListening(false);
          setRecordingActive(false);
          setLocalRecordingState(IDLE);
        }
      );
    }
  };

  // Optimized duration calculation with reduced frequency
  useEffect(() => {
    if (recordingPeriods.length > 0) {
      // Reduce frequency from 100ms to 500ms for better performance
      const interval = setInterval(() => {
        const now = Date.now();
        const totalDuration = recordingPeriods.reduce((total, period) => {
          const end = period.end || now;
          return total + (end - period.start);
        }, 0);
        setCurrentRecordingDuration(totalDuration / 1000);
      }, 500); // Reduced from 100ms to 500ms

      return () => clearInterval(interval);
    } else {
      setCurrentRecordingDuration(0);
    }
  }, [recordingPeriods]);

  // Optimized sine waves with performance improvements
  const sineWavesRef = useRef<any>(null);

  useEffect(() => {
    if (wavesEl.current != null && typeof window !== 'undefined') {
      // Dispose previous instance to prevent memory leaks
      if (sineWavesRef.current) {
        sineWavesRef.current.running = false;
        sineWavesRef.current = null;
      }

      const singleGreenWave = [
        {
          timeModifier: 1.2,
          lineWidth: 1.5,
          amplitude: isSpeaking ? 35 : 0,
          wavelength: 70,
          strokeStyle: '#1FC6A6',
        },
      ];

      // Create new instance with optimized settings
      sineWavesRef.current = new SineWaves({
        el: wavesEl.current,
        speed: wavesConfig.speed,
        ease: 'SineInOut',
        // ease: 'linear', // Use linear easing for better performance
        wavesWidth: wavesConfig.wavesWidth,
        waves: currentMode !== LANGUAGE_SELECTION ? singleGreenWave : waves,
        resizeEvent: function () {
          if (currentMode !== LANGUAGE_SELECTION) {
            var gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
            gradient.addColorStop(0, '#1FC6A6');
            gradient.addColorStop(1, '#1FC6A6');
            var index = -1;
            var length = this.waves.length;
            while (++index < length) {
              this.waves[index].strokeStyle = gradient;
            }
          } else {
            var gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
            gradient.addColorStop(0, 'rgba(25, 255, 255, 0)');
            gradient.addColorStop(0.5, 'rgba(100, 100, 255, 1)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 1)');
            var index = -1;
            var length = this.waves.length;
            while (++index < length) {
              this.waves[index].strokeStyle = gradient;
            }
          }
        },
      });
    }

    // Cleanup on unmount
    return () => {
      if (sineWavesRef.current) {
        sineWavesRef.current.running = false;
        sineWavesRef.current = null;
      }
    };
  }, [wavesConfig.speed, wavesConfig.wavesWidth, currentMode, isSpeaking]);

  const handleStartRecord = () => {
    if (!engine) {
      console.error('Speech engine not initialized');
      return;
    }

    setCurrentMode(RECORD_CONSULTATION);
    setLocalRecordingState(RECORDING);
    toggleRecord();
    setWavesConfig({
      speed: 8,
      wavesWidth: '75%',
    });
    intervalRef.current = setInterval(
      handlePeriodicAction,
      PERIODIC_ACTION_INTERVAL
    );
  };

  const handlePauseRecord = () => {
    setLocalRecordingState(PAUSED);
    toggleRecord();
    setWavesConfig({
      speed: 0,
      wavesWidth: '0%',
    });
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    clearRollingRestartTimer();
  };

  // **FIXED: Proper cleanup when ending recording**
  const handleEndRecording = async () => {
    // Update recording periods
    setRecordingPeriods((prev) => {
      const newPeriods = [...prev];
      if (
        newPeriods.length > 0 &&
        newPeriods[newPeriods.length - 1].end === null
      ) {
        newPeriods[newPeriods.length - 1].end = Date.now();
      }
      return newPeriods;
    });

    const finalTranscript = [...transcriptHistory, transcript].join(' ');
    const duration = currentRecordingDuration;

    // Stop listening immediately
    setIsListening(false);
    isListeningRef.current = false;

    // Stop transcription first
    const activeRecognizer = recognizerRef.current || engine;
    if (activeRecognizer) {
      await new Promise<void>((resolve) => {
        activeRecognizer.stopTranscribingAsync(
          () => resolve(),
          () => resolve()
        );
      });
    }

    // Wait before disposing resources
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Dispose all resources
    await disposeAudioResources();

    // Clear all timers
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    clearRollingRestartTimer();

    // Reset state
    setLocalRecordingState(IDLE);
    setIsSpeaking(false);

    // Call the callback
    onRecordEnd(finalTranscript, duration);
  };

  return (
    <div className="w-full min-h-full flex flex-col justify-between flex-1">
      <div>
        {recordingState === IDLE && currentMode === LANGUAGE_SELECTION && (
          <div className="h-full  ">
            <div className="w-full max-w-xl py-2 px-3  flex flex-col gap-2">
              <span className="mb-2 font-normal text-[12px]">
                To get started, please select your preferred language from the
                options below. This will help us provide you with the best
                possible experience.
              </span>
              <RadioGroup
                value={selectedLanguage}
                onValueChange={handleChooseLanguage}
                className="grid grid-cols-3 gap-4"
              >
                {languageOptions.map((language) => (
                  <label
                    key={language.value}
                    className="flex items-center gap-2 cursor-pointer text-[16px] font-medium"
                  >
                    <RadioGroupItem value={language.value} />
                    {language.label}
                  </label>
                ))}
              </RadioGroup>
            </div>
          </div>
        )}
        {currentMode !== LANGUAGE_SELECTION && (
          <div className="flex flex-col items-center justify-center bg-[#E6EBF0] py-2 px-2 rounded-md mb-4">
            <>
              <div className="flex items-center justify-center w-full mb-2">
                <span className="text-xs text-center text-[#222] font-medium">
                  Ambient Recording Active...
                </span>
                <span className="text-xs text-[#222] font-medium">
                  {Math.floor(currentRecordingDuration / 60)}:
                  {(currentRecordingDuration % 60).toFixed(0).padStart(2, '0')}
                </span>
              </div>
              <canvas ref={wavesEl} className="w-full h-20" />
            </>
          </div>
        )}
        {currentMode === RECORD_CONSULTATION && (
          <div
            className="h-[40vh] flex flex-col gap-2 overflow-y-auto  px-3"
            ref={transcriptEl}
          >
            <span className="text-[18px] font-medium font-['Inter'] pb-2 block">
              Live Transcript:
            </span>
            <div className="gap-3">
              {[...transcriptHistory, transcript].map(
                (x, i) => x && <p key={i}>{x}</p>
              )}
            </div>
          </div>
        )}
      </div>
      <div
        className={
          isLanguageSelection
            ? ' border-t border-[#DAE1E7] py-4 px-5 flex items-center justify-center'
            : ' border-t border-[#DAE1E7] py-3 px-5 flex items-center justify-between'
        }
      >
        <div
          className={isLanguageSelection ? 'hidden' : 'flex items-center gap-5'}
        >
          {(recordingState === RECORDING || recordingState === PAUSED) && (
            <>
              <PrimaryButton
                variant="contained"
                className="bg-[#012436] text-[16px] font-normal w-[180px] h-9 rounded-lg"
                onClick={handleEndRecording}
              >
                End recording
                <BiX className="text-xl" />
              </PrimaryButton>
            </>
          )}
        </div>
        {recordingState !== RECORDING && isLanguageSelection && (
          <PrimaryButton
            variant="contained"
            className={`text-[16px] font-normal px-7 h-9 rounded-lg ${
              !engine ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#012436]'
            }`}
            onClick={handleStartRecord}
            disabled={!engine}
          >
            {!engine ? 'Initializing...' : 'Start recording'}
          </PrimaryButton>
        )}
        {recordingState === RECORDING && !isLanguageSelection && (
          <PrimaryButton
            variant="outlined"
            className="text-[16px] font-normal w-[180px] border-[#012436] text-[#012436] h-9  rounded-lg"
            onClick={handlePauseRecord}
          >
            Pause recording
            <TbPlayerPause className="text-lg" />
          </PrimaryButton>
        )}
        {recordingState !== RECORDING && !isLanguageSelection && (
          <PrimaryButton
            variant="contained"
            className="bg-[#012436] text-[16px] font-normal  h-9  rounded-lg"
            onClick={handleStartRecord}
          >
            Resume recording
            <TbPlayerPlay className="text-lg" />
          </PrimaryButton>
        )}
      </div>
    </div>
  );
};

export default RecordConsultation;
