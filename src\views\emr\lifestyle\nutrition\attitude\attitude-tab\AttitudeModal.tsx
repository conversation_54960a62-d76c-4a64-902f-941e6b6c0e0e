import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { nutritionAttitudeStore } from '@/store/emr/lifestyle/nutrition/attitude/attitude-store';

import { detectAmbientInObject } from '@/utils/ambient-detection';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import LifestyleModalWrapper from '@/views/emr/lifestyle/shared/LifestyleModalWrapper';
import { BaseLifestyleModalProps } from '@/views/emr/lifestyle/shared/types';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AttitudeForm from './AttitudeForm';

const AttitudeModal: React.FC<
  BaseLifestyleModalProps & {
    onSaveRef?: MutableRefObject<(() => void) | null>;
    initialValues?: any;
  }
> = ({
  patientData,
  mode = LifestyleMode.CREATE,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
  initialValues,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
  } = nutritionAttitudeStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { patient } = useCurrentPatientStore();

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? initialValues ?? questions,
    mode: 'onChange',
  });

  const { handleSubmit, reset } = methods;

  const formFields = useMemo(() => {
    if (patientData?.questions?.length) return patientData.questions;
    if (!questions?.questions?.length) return [];
    return questions.questions;
  }, [questions, patientData]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      try {
        if (data?.id) {
          const updateData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
          };
          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
            // Include ambient listening data if available from initialValues
            ...(initialValues?.conversation && {
              conversation: initialValues.conversation,
            }),
            ...(initialValues?.recordingDuration && {
              recordingDuration: initialValues.recordingDuration,
            }),
          };
          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error('Error submitting nutrition attitude:', error);
      }
    },
    [
      profile,
      updateLifestyleData,
      createLifestyleData,
      setModalOpen,
      onAfterSubmit,
      initialValues, // Add initialValues to dependencies
    ]
  );

  const handleSaveClick = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = handleSaveClick;
    }
    return () => {
      if (onSaveRef) {
        onSaveRef.current = null;
      }
    };
  }, [onSaveRef, handleSaveClick]);
  useEffect(() => {
    if (
      !patientData &&
      (!questions?.questions?.length || questions?.questions?.length === 0)
    ) {
      getLifestyleQuestions();
    }
  }, [getLifestyleQuestions, questions, patientData]);

  useEffect(() => {
    if (patientData) {
      reset(patientData);
    } else if (questions?.questions?.length) {
      reset(questions);
    }
  }, [patientData, questions, reset]);

  useEffect(() => {
    setCurrentMode(mode);
  }, [mode]);

  return (
    <FormProvider {...methods}>
      <LifestyleModalWrapper
        loading={questionLoading}
        onSubmit={handleSubmit(onSubmit)}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
        data={patientData || undefined}
        doctorName={profile?.general?.fullName}
        patientName={patient?.name}
      >
        <AttitudeForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          variant="modal"
          showHeading={true}
          isAmbientForm={detectAmbientInObject(initialValues || patientData)}
        />
      </LifestyleModalWrapper>
    </FormProvider>
  );
};

export default memo(AttitudeModal);
