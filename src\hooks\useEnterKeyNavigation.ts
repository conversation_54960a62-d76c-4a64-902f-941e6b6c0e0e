import { useCallback } from 'react';

export interface FieldNavigationConfig {
  fieldOrder: string[];
  isFieldDisabled?: (fieldName: string, index?: number) => boolean;
  shouldTriggerSave?: (fieldName: string) => boolean;
  onSave?: () => void;
}

/**
 * Custom hook for handling Enter key navigation between form fields
 * Based on the implementation from GeneralDetails.tsx
 */
export const useEnterKeyNavigation = (config: FieldNavigationConfig) => {
  const { fieldOrder, isFieldDisabled, shouldTriggerSave, onSave } = config;

  const getNextEditableField = useCallback(
    (currentField: string): string | null => {
      const currentIndex = fieldOrder.indexOf(currentField);

      for (let i = currentIndex + 1; i < fieldOrder.length; i++) {
        const fieldName = fieldOrder[i];
        if (!isFieldDisabled || !isFieldDisabled(fieldName)) {
          return fieldName;
        }
      }
      return null;
    },
    [fieldOrder, isFieldDisabled]
  );

  const handleKeyDown = useCallback(
    (
      e: React.KeyboardEvent,
      currentFieldName: string,
      options?: {
        shouldTriggerSave?: boolean;
        index?: number;
        customFieldName?: string;
      }
    ) => {
      if (e.key === 'Enter') {
        e.preventDefault();

        const fieldNameToUse = options?.customFieldName || currentFieldName;
        const shouldSave =
          options?.shouldTriggerSave ||
          (shouldTriggerSave && shouldTriggerSave(fieldNameToUse));

        if (shouldSave) {
          if (onSave) {
            onSave();
          } else {
            const form = e.currentTarget.closest('form');
            if (form) {
              const submitButton = form.querySelector(
                'button[type="submit"]'
              ) as HTMLButtonElement;
              if (submitButton) {
                submitButton.click();
              }
            }
          }
          return;
        }

        const nextField = getNextEditableField(fieldNameToUse);
        if (nextField) {
          const currentElement = e.currentTarget as HTMLElement;
          const currentRect = currentElement?.getBoundingClientRect();

          setTimeout(() => {
            let nextFieldElement: HTMLElement | null = null;

            const isVisibleAndFocusable = (el: HTMLElement) => {
              return (
                el &&
                el.offsetParent !== null &&
                !el.hasAttribute('disabled') &&
                el.offsetWidth > 0 &&
                el.offsetHeight > 0
              );
            };

            const byNameSelectors = [
              `input[name="${nextField}"]`,
              `textarea[name="${nextField}"]`,
            ];

            for (const selector of byNameSelectors) {
              const elements = document.querySelectorAll(selector);
              for (let i = 0; i < elements.length; i++) {
                const el = elements[i] as HTMLElement;
                if (isVisibleAndFocusable(el)) {
                  nextFieldElement = el;
                  break;
                }
              }
              if (nextFieldElement) break;
            }

            if (!nextFieldElement) {
              const allComboboxes =
                document.querySelectorAll('[role="combobox"]');

              const fieldParts = nextField.split('.');
              const lastPart = fieldParts[fieldParts.length - 1].toLowerCase();
              const possibleMatches = [
                lastPart,
                nextField.toLowerCase(),
                lastPart.replace(/([A-Z])/g, ' $1').trim(), // camelCase to spaces
              ];

              for (let i = 0; i < allComboboxes.length; i++) {
                const combobox = allComboboxes[i] as HTMLElement;
                const input = combobox.querySelector('input') as HTMLElement;

                if (input && isVisibleAndFocusable(input)) {
                  const container = input.closest(
                    '.MuiFormControl-root, [class*="FormControl"], div'
                  );
                  if (container) {
                    const label = container.querySelector('label');
                    const labelText = (label?.textContent || '')
                      .toLowerCase()
                      .trim();
                    const isMatch = possibleMatches.some((match) => {
                      return (
                        labelText.includes(match) || match.includes(labelText)
                      );
                    });

                    if (isMatch) {
                      if (currentRect) {
                        const targetRect = input.getBoundingClientRect();
                        if (targetRect.top >= currentRect.top - 5) {
                          nextFieldElement = input;
                          break;
                        }
                      } else {
                        nextFieldElement = input;
                        break;
                      }
                    }
                  }
                }
              }
            }

            if (!nextFieldElement && currentElement) {
              const allInputs = Array.from(
                document.querySelectorAll(
                  'input:not([type="hidden"]):not([type="button"]):not([type="submit"]), textarea'
                )
              ) as HTMLElement[];

              const allComboboxInputs = Array.from(
                document.querySelectorAll('[role="combobox"] input')
              ) as HTMLElement[];
              const allFocusable = [...allInputs, ...allComboboxInputs].filter(
                (el, index, self) => self.indexOf(el) === index
              );

              let foundCurrent = false;

              for (let i = 0; i < allFocusable.length; i++) {
                const el = allFocusable[i];
                if (el === currentElement) {
                  foundCurrent = true;
                  continue;
                }
                if (foundCurrent && isVisibleAndFocusable(el)) {
                  nextFieldElement = el;
                  break;
                }
              }

              if (!foundCurrent && currentRect) {
                for (const el of allFocusable) {
                  if (isVisibleAndFocusable(el)) {
                    const elRect = el.getBoundingClientRect();
                    if (
                      elRect.top > currentRect.top ||
                      (Math.abs(elRect.top - currentRect.top) < 10 &&
                        elRect.left > currentRect.left)
                    ) {
                      nextFieldElement = el;
                      break;
                    }
                  }
                }
              }
            }
            if (!nextFieldElement && currentElement && currentRect) {
              const regularInputs = Array.from(
                document.querySelectorAll(
                  'input:not([type="hidden"]):not([type="button"]):not([type="submit"]), textarea'
                )
              ) as HTMLElement[];

              const selectInputs = Array.from(
                document.querySelectorAll('[role="combobox"] input')
              ) as HTMLElement[];
              const allInputs = [...regularInputs, ...selectInputs].filter(
                (el, index, self) => self.indexOf(el) === index
              );

              let closestElement: HTMLElement | null = null;
              let closestDistance = Infinity;

              for (const input of allInputs) {
                if (!isVisibleAndFocusable(input)) continue;
                if (input === currentElement) continue;

                const inputRect = input.getBoundingClientRect();

                const isToRight =
                  Math.abs(inputRect.top - currentRect.top) < 10 &&
                  inputRect.left > currentRect.right;
                const isBelow = inputRect.top > currentRect.bottom - 10;

                if (isToRight || isBelow) {
                  const distance =
                    Math.abs(inputRect.top - currentRect.top) +
                    Math.abs(inputRect.left - currentRect.left);

                  if (distance < closestDistance) {
                    closestDistance = distance;
                    closestElement = input;
                  }
                }
              }

              if (closestElement) {
                nextFieldElement = closestElement;
              }
            }

            if (nextFieldElement) {
              nextFieldElement.focus();

              if (
                nextFieldElement.tagName === 'INPUT' ||
                nextFieldElement.tagName === 'TEXTAREA'
              ) {
                const inputEl = nextFieldElement as
                  | HTMLInputElement
                  | HTMLTextAreaElement;

                if (!inputEl.readOnly) {
                  setTimeout(() => {
                    inputEl.select();
                  }, 10);
                }
              }
            } else {
              console.warn(`Could not find next field: ${nextField}`);
            }
          }, 50);
        }
      }
    },
    [getNextEditableField, shouldTriggerSave, onSave]
  );

  return {
    handleKeyDown,
    getNextEditableField,
  };
};

/**
 * Utility function to create field navigation for array-based forms (like emergency contacts, qualifications, etc.)
 */
export const createArrayFieldNavigation = (
  baseFields: string[],
  index: number,
  prefix: string
): string[] => {
  return baseFields.map((field) => `${prefix}.${index}.${field}`);
};

/**
 * Utility function to extract base field name from array field name
 * e.g., "emergencyContacts.0.name" -> "name"
 */
export const getBaseFieldName = (fieldName: string): string => {
  const parts = fieldName.split('.');
  return parts[parts.length - 1];
};
