// instrumentation.ts
// Main instrumentation entry point for Next.js

export async function register() {
  // Only run on server side
  if (typeof window !== 'undefined') {
    return;
  }

  // Skip telemetry initialization in Edge runtime
  // Edge runtime doesn't support Node.js-specific packages
  if (process.env.NEXT_RUNTIME === 'edge') {
    console.log('⚪ Skipping telemetry initialization in Edge runtime');
    return;
  }

  try {
    // Import and run the server-only telemetry initialization
    const { initializeTelemetry } = await import('./instrumentation.server');
    await initializeTelemetry();
  } catch (error) {
    console.error('❌ Failed to load telemetry initialization:', error);
    // Don't throw - we don't want telemetry issues to break the app
  }
}
