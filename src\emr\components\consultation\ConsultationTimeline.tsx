import React, { useState, useRef, useEffect, useCallback } from 'react';

import { useForm } from 'react-hook-form';

import dayjs from 'dayjs';

import { useConsultationStore } from '@/store/consultation-store';
import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { GetPatientHistoryRes } from '@/query/patient';

import { FINALIZED } from '@/utils/constants/consultation';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { capitalizeFirstLetter } from '@/utils/string';

import PrintPreview from '@/views/emr/consultation/shared/Print/PrintPreview';

import SummaryForm from '@/emr/components/consultation/summary-form';

import { DateRange } from '@/core/components/date-range-picker/types';

import FinalizeModal from '../lifestyle/lifestyle-forms/shared/FinalizeModal';
import TimelineTitle from '../lifestyle/lifestyle-forms/shared/TimelineTitle';

import Accordion from './Accordion';
import ConsultationExpanded from './ConsultationExpanded';
import TimeLine from './TimeLine';

type Props = {
  isFetching?: boolean;
  patientHistory: GetPatientHistoryRes;
  fetchPatientHistory: () => Promise<void>;
  dateRange: DateRange;
  onSelectDateRange: (range: DateRange) => void;
};

const ConsultationTimeline = ({
  patientHistory,
  isFetching,
  fetchPatientHistory,
  dateRange,
  onSelectDateRange,
}: Props) => {
  const form = useForm();

  const { finaliseRecord, updating } = useConsultationStore();
  const { patient } = useCurrentPatientStore();
  const { data: userData } = useUserStore();
  const { doctorProfile } = useDoctorStore();

  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);
  const [selectedHistory, setSelectedHistory] = useState<
    GetPatientHistoryRes[0] | null
  >(null);
  const [dataToFinalize, setDataToFinalize] = useState<
    GetPatientHistoryRes[0] | null
  >(null);
  const [selectedDateRange, setSelectedDateRange] =
    useState<DateRange>(dateRange);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [printData, setPrintData] = useState<{
    open: boolean;
    consultation: GetPatientHistoryRes[0] | null;
    doctorName: string;
  }>({
    open: false,
    consultation: null,
    doctorName: '',
  });

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  const toggleAccordion = (index: number) => {
    setExpandedIndex((prev) => (prev === index ? null : index));
  };

  const onFinalise = useCallback((data: GetPatientHistoryRes[0]) => {
    setDataToFinalize(data);
  }, []);

  const handlePrint = useCallback(
    (consultation: GetPatientHistoryRes[0] | null) => {
      const doctorName =
        consultation?.summary?.owner?.id === userData?.id &&
        doctorProfile?.general?.fullName
          ? doctorProfile.general.fullName
          : consultation?.summary?.owner?.name || '---';

      setPrintData({
        open: true,
        consultation,
        doctorName,
      });
    },
    [userData?.id, doctorProfile?.general?.fullName]
  );

  const closePrintPreview = useCallback(() => {
    setPrintData((prev) => ({ ...prev, open: false }));
  }, []);

  useEffect(() => {
    if (expandedIndex !== null && scrollContainerRef.current) {
      const scrollContainer = scrollContainerRef.current;
      const targetAccordion = scrollContainer.querySelector(
        `.accordion-item-${expandedIndex}`
      );

      const expandedAccordionOffset = expandedIndex * 50;

      if (targetAccordion instanceof HTMLElement) {
        scrollContainer.scrollTo({
          top: expandedAccordionOffset,
          behavior: 'smooth',
        });
      }
    }
  }, [expandedIndex]);

  useEffect(() => {
    const hasBothDates = selectedDateRange?.from && selectedDateRange?.to;
    const isCleared = !selectedDateRange?.from && !selectedDateRange?.to;

    const shouldFetch = (hasBothDates || isCleared) && !isCalendarOpen;
    if (shouldFetch) {
      onSelectDateRange(selectedDateRange);
    }
  }, [onSelectDateRange, isCalendarOpen, selectedDateRange]);

  const onFinaliseRecord = useCallback(async () => {
    if (!dataToFinalize) return;

    await finaliseRecord(dataToFinalize);
    setDataToFinalize(null);

    if (patient?.id) {
      await fetchPatientHistory();
    }
  }, [dataToFinalize, finaliseRecord, patient?.id, fetchPatientHistory]);

  return (
    <div className="mx-auto h-full px-1  overflow-y-hidden">
      <TimelineTitle
        title="Consultation Timeline"
        className="text-lg mb-1"
        dateRange={selectedDateRange}
        onDateChange={setSelectedDateRange}
        setIsCalendarOpen={setIsCalendarOpen}
        isCalendarOpen={isCalendarOpen}
        maxDate={dayjs().toDate()}
      />
      <div
        ref={scrollContainerRef}
        className="overflow-y-auto h-[calc(100%-3rem)] w-full rounded-thin-scrollbar pr-1"
      >
        <TimeLine
          loading={isFetching}
          items={patientHistory?.map((history, i) => ({
            content: (
              <div
                key={`${i}-patient-history`}
                className={`accordion-item-${i}`}
              >
                <Accordion
                  open={expandedIndex === i}
                  onToggle={() => toggleAccordion(i)}
                  date={formatDate(
                    history?.updated_on,
                    DateFormats.DATE_DD_MM_YYYY_SLASH
                  )}
                  duration={history?.summary?.recordingDuration}
                  onFinalise={() => onFinalise(history)}
                  showFinaliseButton={history?.status !== FINALIZED}
                  finalized={history?.status === FINALIZED}
                  designation={capitalizeFirstLetter(
                    history.summary?.owner?.id === userData?.id &&
                      doctorProfile?.general?.designation
                      ? doctorProfile?.general?.designation
                      : history.summary?.owner?.role || '---'
                  )}
                  doctorName={
                    history.summary?.owner?.id === userData?.id &&
                    doctorProfile?.general?.fullName
                      ? doctorProfile?.general?.fullName
                      : history.summary?.owner?.name || '---'
                  }
                  department={
                    history.summary?.owner?.id === userData?.id &&
                    doctorProfile?.general?.department
                      ? doctorProfile?.general?.department
                      : history.summary?.owner?.department || '---'
                  }
                  onExpand={() => setSelectedHistory(history)}
                  onPrint={() => handlePrint(history)}
                  isDownloadPDF={true}
                  fileName="somename.pdf"
                >
                  <SummaryForm
                    key={history?.id || 'default'}
                    className="px-2"
                    editable={false}
                    form={form}
                    data={history}
                  />
                </Accordion>
              </div>
            ),
          }))}
        />
      </div>
      <FinalizeModal
        open={!!dataToFinalize}
        onClose={() => setDataToFinalize(null)}
        onFinalize={onFinaliseRecord}
        loading={updating}
      />
      <ConsultationExpanded
        isOpen={!!selectedHistory}
        onClose={() => {
          setSelectedHistory(null);
        }}
        fetchPatientHistory={fetchPatientHistory}
        selectedHistory={selectedHistory}
        showFinaliseButton={selectedHistory?.status !== FINALIZED}
        onFinalise={(data) => {
          setSelectedHistory(null);
          setDataToFinalize(data);
        }}
      />
      {printData.consultation && patient && (
        <PrintPreview
          open={printData.open}
          onClose={closePrintPreview}
          consultations={[printData.consultation]}
          patient={patient}
          doctorName={printData.doctorName}
        />
      )}
    </div>
  );
};

export default ConsultationTimeline;
