import React, { memo, useCallback, useEffect } from 'react';

import { useFormContext, useWatch } from 'react-hook-form';

import { useEnterKeyNavigation } from '@/hooks/useEnterKeyNavigation';

import { useManagePatientStore } from '@/store/mrd/manage-patient/manage';

import { states } from '@/utils/constants/master';
import {
  formatToAlphaNumeric,
  formatToAlphaNumericToUpperCase,
  formatToNumber,
  formatToNumberWithDot,
} from '@/utils/format-value';

import { countryOptions } from '@/constants/mrd/manage-patient/country';
import {
  genderOptions,
  maritalStatusOptions,
} from '@/constants/mrd/manage-patient/select-options';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import AppTitle from '@/core/components/app-title';
import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const GeneralDetails = () => {
  const {
    control,
    setValue,
    clearErrors,
    trigger,
    formState: { errors },
  } = useFormContext<PatientDetails>();

  const { setCurrentTab } = useManagePatientStore();

  const name = useWatch({ control, name: 'name' });
  const dob = useWatch({ control, name: 'dob' });
  const sex = useWatch({ control, name: 'sex' });

  useEffect(() => {
    if (name && errors.name) {
      clearErrors('name');
    }
  }, [name, errors.name, clearErrors]);

  useEffect(() => {
    if (dob && errors.dob) {
      clearErrors('dob');
    }
  }, [dob, errors.dob, clearErrors]);

  useEffect(() => {
    if (sex && errors.sex) {
      clearErrors('sex');
    }
  }, [sex, errors.sex, clearErrors]);

  useEffect(() => {
    if (dob) {
      const calculateAge = (dob: string) => {
        const birthDate = new Date(dob);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && today.getDate() < birthDate.getDate())
        ) {
          age--;
        }
        return age;
      };

      const age = calculateAge(dob);
      setValue('age', age.toString());
    }
  }, [dob, setValue]);

  const fieldOrder = [
    'name',
    'dob',
    'sex',
    'age',
    'height',
    'weight',
    'maritalStatus',
    'cmcId',
    'address.houseName',
    'address.pin',
    'address.street',
    'address.city',
    'address.district',
    'address.state',
    'address.country',
    'contact.phone',
    'contact.email',
  ];

  const handleNavigateToNextTab = useCallback(async () => {
    const isValid = await trigger(undefined, { shouldFocus: true });

    if (isValid) {
      setCurrentTab(1);
    }
  }, [trigger, setCurrentTab]);

  const shouldTriggerSave = useCallback((fieldName: string) => {
    return fieldName === 'contact.email';
  }, []);

  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder,
    shouldTriggerSave,
    onSave: handleNavigateToNextTab,
  });

  return (
    <div className="flex flex-col gap-base w-full max-h-full h-full overflow-y-auto">
      <div className="w-full flex flex-col gap-base border-b-2 py-base">
        <div className="flex gap-base w-[60%]">
          <div className="w-1/2">
            <ControlledTextField
              name="name"
              control={control}
              label="Patient Name"
              placeholder="Enter Patient Name"
              fullWidth
              required
              initiallyReadonly
              formatValue={formatToAlphaNumericToUpperCase}
              onKeyDown={(e) => handleKeyDown(e, 'name')}
            />
          </div>
          <div className="w-1/4">
            <ControlledDatePicker
              name="dob"
              control={control}
              label="Date of Birth"
              initiallyReadonly
              disableFuture={true}
              onKeyDown={(e) => handleKeyDown(e, 'dob')}
            />
          </div>
          <div className="w-1/4">
            <ControlledSelectField
              name="sex"
              control={control}
              label="Gender"
              options={genderOptions}
              placeholder="Select"
              required
              initiallyReadonly
              onKeyDown={(e) => handleKeyDown(e, 'sex')}
            />
          </div>
        </div>
        <div className="flex gap-base ">
          <div className="flex-1 min-w-[120px]">
            <ControlledTextField
              name="age"
              control={control}
              label="Age"
              required
              placeholder="Enter"
              formatValue={formatToNumber}
              onKeyDown={(e) => handleKeyDown(e, 'age')}
            />
          </div>
          <div className="flex-1  min-w-[130px]">
            <ControlledTextField
              name="height"
              control={control}
              label="Height"
              required
              placeholder="Height in cm"
              formatValue={formatToNumber}
              onKeyDown={(e) => handleKeyDown(e, 'height')}
            />
          </div>
          <div className="flex-1  min-w-[120px]">
            <ControlledTextField
              name="weight"
              control={control}
              required
              label="Weight"
              placeholder="Weight in kg"
              formatValue={formatToNumberWithDot(1)}
              onKeyDown={(e) => handleKeyDown(e, 'weight')}
            />
          </div>
          <div className="flex-[1.3]  min-w-[130px]">
            <ControlledSelectField
              name="maritalStatus"
              control={control}
              label="Marital Status"
              options={maritalStatusOptions}
              placeholder="Select"
              onKeyDown={(e) => handleKeyDown(e, 'maritalStatus')}
            />
          </div>
          <ControlledTextField
            name="cmcId"
            control={control}
            label="Hospital ID"
            placeholder="Enter Hospital ID"
            initiallyReadonly
            formatValue={formatToAlphaNumericToUpperCase}
            slotProps={{
              input: {
                inputProps: { maxLength: 9 },
              },
            }}
            onKeyDown={(e) => handleKeyDown(e, 'cmcId')}
          />
        </div>
      </div>
      <div className="w-[60%] flex flex-col gap-base py-base">
        <AppTitle variant="subtitle1">Patient Address</AppTitle>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.houseName"
            control={control}
            label="House Name"
            placeholder="House No/Name"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
            onKeyDown={(e) => handleKeyDown(e, 'address.houseName')}
          />
          <ControlledTextField
            name="address.pin"
            control={control}
            label="Pin"
            placeholder="682030"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            slotProps={{
              input: {
                inputProps: { maxLength: 6 },
              },
            }}
            onKeyDown={(e) => handleKeyDown(e, 'address.pin')}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.street"
            control={control}
            label="Street"
            placeholder="Street"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
            onKeyDown={(e) => handleKeyDown(e, 'address.street')}
          />
          <ControlledTextField
            name="address.city"
            control={control}
            label="City"
            placeholder="City"
            required
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
            onKeyDown={(e) => handleKeyDown(e, 'address.city')}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.district"
            control={control}
            label="District"
            placeholder="District"
            initiallyReadonly
            onKeyDown={(e) => handleKeyDown(e, 'address.district')}
          />
          <ControlledSelectField
            name="address.state"
            control={control}
            label="State"
            options={states}
            placeholder="State"
            initiallyReadonly
            onKeyDown={(e) => handleKeyDown(e, 'address.state')}
          />
        </div>
        <div className="flex gap-base w-1/2">
          <ControlledSelectField
            name="address.country"
            control={control}
            label="Country"
            options={countryOptions}
            placeholder="Country"
            initiallyReadonly
            onKeyDown={(e) => handleKeyDown(e, 'address.country')}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="contact.phone"
            control={control}
            label="Mobile (Personal)"
            placeholder="Phone"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            required
            slotProps={{
              input: {
                inputProps: { maxLength: 10 },
              },
            }}
            rules={{
              validate: (value: any) => {
                if (value?.includes('*')) return true;

                if (!value) return 'Mobile number is required';
                if (value.length !== 10)
                  return 'Enter a valid 10 digit mobile number';
                return true;
              },
            }}
            onKeyDown={(e) => handleKeyDown(e, 'contact.phone')}
          />

          <ControlledTextField
            name="contact.email"
            control={control}
            label="Email"
            placeholder="Email"
            fullWidth
            initiallyReadonly
            onKeyDown={(e) => handleKeyDown(e, 'contact.email')}
          />
        </div>
      </div>
    </div>
  );
};

export default memo(GeneralDetails);
