import React, { useState, useMemo } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import Accordion from './Accordion';
import LifestyleTranscriptView from './LifestyleTranscriptView';

interface LifestyleAccordionWrapperProps {
  data: QuestionnaireResponse;
  children: React.ReactNode;
  designation?: string;
  doctorName?: string;
  department?: string;
  stepper?: string[];
  date?: string;
  open: boolean;
  onToggle?: () => void;
  onFinalise?: () => void;
  onExpand?: () => void;
  onPrint?: () => void;
  finalised?: boolean;
  expand?: boolean;
  width?: string;
  className?: string;
  isKnowledge?: boolean;
  isAttitude?: boolean;
}

const LifestyleAccordionWrapper: React.FC<LifestyleAccordionWrapperProps> = ({
  data,
  children,
  designation,
  doctorName,
  department,
  stepper = [],
  date,
  open,
  onToggle,
  onFinalise,
  onExpand,
  onPrint,
  finalised = true,
  expand,
  width,
  className = '',
  isKnowledge = false,
  isAttitude = false,
}) => {
  const { patient } = useCurrentPatientStore();
  const { doctorProfile } = useDoctorStore();

  const [showTranscriptView, setShowTranscriptView] = useState(false);

  const handleSwitchToTranscript = () => {
    setShowTranscriptView(true);
  };

  const handleGoBackFromTranscript = () => {
    setShowTranscriptView(false);
  };

  const accordionContent = useMemo(() => {
    if (showTranscriptView && data.conversation) {
      return (
        <LifestyleTranscriptView
          conversation={data.conversation}
          doctorName={
            doctorProfile?.general?.fullName || doctorName || 'Doctor'
          }
          patientName={patient?.name || 'Patient'}
          date={data.created_on || date}
          duration={data.recordingDuration}
        />
      );
    }
    return children;
  }, [
    showTranscriptView,
    data.conversation,
    data.created_on,
    data.recordingDuration,
    doctorProfile?.general?.fullName,
    doctorName,
    patient?.name,
    date,
    children,
  ]);

  return (
    <Accordion
      designation={designation}
      doctorName={doctorName}
      department={department}
      stepper={stepper}
      date={date}
      open={open}
      onToggle={onToggle}
      onFinalise={onFinalise}
      onExpand={onExpand}
      onPrint={onPrint}
      finalised={finalised}
      expand={expand}
      width={width}
      className={className}
      isKnowledge={isKnowledge}
      isAttitude={isAttitude}
      conversation={data.conversation}
      showTranscriptView={showTranscriptView}
      onSwitchToTranscript={handleSwitchToTranscript}
      onGoBackFromTranscript={handleGoBackFromTranscript}
    >
      {accordionContent}
    </Accordion>
  );
};

export default LifestyleAccordionWrapper;
