import { api, publicApi } from '@/core/lib/interceptor';

export interface CreatePaymentOrderRequest {
  amount: number;
  currency: string;
  patientId: string;
  paymentType: string;
  organizationId: string;
  description: string;
  metadata?: Record<string, any>;
}

export interface CreateSubscriptionPaymentOrderRequest {
  amount: number;
  currency: string;
  paymentType: string;
  subscriberEmail: string;
  organizationId: string;
  description: string;
  metadata: {
    planId: string;
    subscriptionType: string;
    contactEmail: string;
    billingType: string;
  };
}

export interface CreatePaymentOrderResponse {
  success: boolean;
  data: {
    orderId: string;
    paymentId: string;
    keyId: string;
    amount: number;
    currency: string;
    status: string;
    description: string;
  };
}

export interface PaymentStatus {
  orderId: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentId?: string;
  razorpayPaymentId?: string;
  amount: number;
  currency: string;
  completedAt?: string;
}

export const createPaymentOrder = async (
  data: CreatePaymentOrderRequest
): Promise<CreatePaymentOrderResponse> => {
  const response = await api.post<CreatePaymentOrderResponse>(
    'payment/v0.1/payments/create-order',
    data
  );
  return response.data;
};

export const createSubscriptionPaymentOrder = async (
  data: CreateSubscriptionPaymentOrderRequest
): Promise<CreatePaymentOrderResponse> => {
  const response = await publicApi.post<CreatePaymentOrderResponse>(
    'payment/v0.1/payments/create-order',
    data
  );
  return response.data;
};

export const getPaymentStatus = async (
  orderId: string
): Promise<PaymentStatus> => {
  const response = await api.get<PaymentStatus>(
    `/payments/status?orderId=${orderId}`
  );
  return response.data;
};

export const verifyPayment = async (data: {
  orderId: string;
  paymentId: string;
  signature: string;
}): Promise<{ verified: boolean; status: string }> => {
  const response = await api.post<{ verified: boolean; status: string }>(
    '/payments/verify',
    data
  );
  return response.data;
};
