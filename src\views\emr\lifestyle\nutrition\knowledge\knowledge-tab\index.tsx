import React, { memo, useEffect, useMemo, useState, useCallback } from 'react';

import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { nutritionKnowledgeStore } from '@/store/emr/lifestyle/nutrition/knowledge/knowledge-store';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import LifestyleAccordionWrapper from '@/views/emr/lifestyle/shared/LifestyleAccordionWrapper';

import TimeLine from '@/emr/components/consultation/TimeLine';
import FinalizeModal from '@/emr/components/lifestyle/lifestyle-forms/shared/FinalizeModal';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import NutritionKnowledgeEditModal from './NutritionKnowledgeEditModal';
import NutritionKnowledgeTimelineForm from './NutritionKnowledgeTimelineForm';

const KnowledgeTab = () => {
  const { setSource, modalOpen } = lifestyleStore();
  const { patientData, loading, getPatientData, finalizeRecord, finalizing } =
    nutritionKnowledgeStore();
  const { fromDate, toDate } = useLifestyleFilterStore();

  const [openedAccordion, setOpenedAccordion] = useState<number | null>(null);
  const [recordToFinalize, setRecordToFinalize] =
    useState<QuestionnaireResponse | null>(null);
  const [editRecord, setEditRecord] = useState<QuestionnaireResponse | null>(
    null
  );

  const handleFinalize = useCallback(
    async (id: string) => {
      await finalizeRecord(id);
      setRecordToFinalize(null);

      if (editRecord?.id === id) {
        setEditRecord({
          ...editRecord,
          status: LifestyleRecordStatus.FINALIZED,
        });
      }
    },
    [finalizeRecord, editRecord]
  );

  useEffect(() => {
    setSource(LifestyleSources.NUTRITION_KNOWLEDGE);
  }, [setSource]);

  useEffect(() => {
    getPatientData(fromDate, toDate);
  }, [getPatientData, fromDate, toDate]);

  const timelineItems = useMemo(() => {
    return patientData.map((data, index) => ({
      content: (
        <LifestyleAccordionWrapper
          key={`${data.id}-${data.created_on}`}
          data={data}
          date={data.created_on}
          open={openedAccordion === index}
          onToggle={() => {
            setOpenedAccordion(openedAccordion === index ? null : index);
          }}
          onFinalise={() => setRecordToFinalize(data)}
          finalised={data.status === LifestyleRecordStatus.FINALIZED}
          doctorName={data?.doctor?.name}
          designation={data?.doctor?.designation}
          department={data?.doctor?.department}
          className="bg-white rounded-lg shadow-sm"
          onExpand={() => setEditRecord(data)}
          stepper={['Knowledge']}
          isKnowledge={true}
        >
          <NutritionKnowledgeTimelineForm data={data} />
        </LifestyleAccordionWrapper>
      ),
    }));
  }, [patientData, openedAccordion]);

  return (
    <div className="h-full flex flex-col p-1 overflow-y-auto">
      <TimeLine items={timelineItems} loading={loading} />

      <FinalizeModal
        open={!!recordToFinalize}
        onClose={() => setRecordToFinalize(null)}
        onFinalize={() =>
          recordToFinalize?.id && handleFinalize(recordToFinalize.id)
        }
        loading={finalizing}
      />
      <NutritionKnowledgeEditModal
        open={!!editRecord}
        onClose={() => setEditRecord(null)}
        formFields={editRecord}
        onFinalize={(record) => {
          if (record?.id) {
            handleFinalize(record.id);
          }
        }}
      />
    </div>
  );
};

export default memo(KnowledgeTab);
