import React from 'react';

import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';

interface ActivityPieChartProps {
  data: Array<{
    name: string;
    value: number;
    percentage: number;
    color: string;
    count?: number;
    duration?: number;
  }>;
  title: string;

  unit: string;
}

const RADIAN = Math.PI / 180;

const renderCustomizedLabelWithLeaderLine = ({
  cx,
  cy,
  midAngle,

  outerRadius,
  name,
  percentage,
  value,
  duration,
  unit,
}: any) => {
  // Skip rendering if percentage is too small
  if (percentage < 2) return null;

  // Leader line points - straight line from pie edge
  const lineStartRadius = outerRadius + 2;
  const lineEndRadius = outerRadius + 35;
  const lineStartX = cx + lineStartRadius * Math.cos(-midAngle * RADIAN);
  const lineStartY = cy + lineStartRadius * Math.sin(-midAngle * RADIAN);
  const lineEndX = cx + lineEndRadius * Math.cos(-midAngle * RADIAN);
  const lineEndY = cy + lineEndRadius * Math.sin(-midAngle * RADIAN);

  // Determine text anchor based on position
  const isRightSide = lineEndX > cx;
  const textAnchor = isRightSide ? 'start' : 'end';

  // Adjust label position to avoid cutoff
  const labelOffsetX = isRightSide ? 5 : -5;
  const labelX = lineEndX + labelOffsetX;

  return (
    <g>
      {/* Straight leader line from pie edge */}
      <line
        x1={lineStartX}
        y1={lineStartY}
        x2={lineEndX}
        y2={lineEndY}
        stroke="#999"
        strokeWidth={1}
      />

      {/* Activity name - positioned above the line end */}
      <text
        x={labelX}
        y={lineEndY - 12}
        fill="#333"
        textAnchor={textAnchor}
        dominantBaseline="middle"
        style={{
          fontSize: '11px',
          fontWeight: '600',
        }}
      >
        {name}
      </text>

      {/* Percentage - positioned below the line end */}
      <text
        x={labelX}
        y={lineEndY + 4}
        fill="#666"
        textAnchor={textAnchor}
        dominantBaseline="middle"
        style={{
          fontSize: '9px',
        }}
      >
        {percentage}%
      </text>

      {/* Duration - positioned below percentage */}
      <text
        x={labelX}
        y={lineEndY + 15}
        fill="#666"
        textAnchor={textAnchor}
        dominantBaseline="middle"
        style={{
          fontSize: '9px',
        }}
      >
        {duration || value} {unit}
      </text>
    </g>
  );
};

const CustomTooltip = ({ active, payload, unit }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-3 border border-gray-200 rounded shadow-lg text-sm">
        <p className="font-semibold text-gray-800 mb-1">{data.name}</p>
        <p className="text-gray-600">
          <span className="font-medium">{data.percentage}%</span>
        </p>
        <p className="text-gray-600">
          Duration: {data.duration || data.value} {unit}
        </p>
      </div>
    );
  }
  return null;
};

const ActivityPieChart: React.FC<ActivityPieChartProps> = ({
  data = [],
  title = '',

  unit = '',
}) => {
  // Define legend items based on chart type
  const getLegendItems = () => {
    if (title.toLowerCase().includes('intensity')) {
      return [
        { name: 'Intense', color: '#BA324F' },
        { name: 'Moderate', color: '#FFAA5A' },
        { name: 'Mild', color: '#AB92BF' },
      ];
    } else {
      return [
        { name: 'Flexibility', color: '#0B3948' },
        { name: 'Strength', color: '#A8C256' },
        { name: 'Balance', color: '#1B998B' },
        { name: 'Aerobics', color: '#BA324F' },
      ];
    }
  };

  const legendItems = getLegendItems();
  const chartTitle = title || 'Distribution';

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm mt-4">
      <div
        className={`flex flex-col  border-b border-gray-200 mb-4 pt-1 ${title.toLowerCase().includes('intensity') ? ' pb-5' : 'pb-1'}`}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h3 className="text-base font-medium text-gray-900">{chartTitle}</h3>

          {title.toLowerCase().includes('activity') ? (
            <div className="grid grid-cols-2 gap-x-6 gap-y-2">
              {legendItems.map((item, index) => (
                <div key={index} className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-1.5 flex-shrink-0"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-xs text-gray-600 whitespace-nowrap">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-wrap items-center gap-4">
              {legendItems.map((item, index) => (
                <div key={index} className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-1.5 flex-shrink-0"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-xs text-gray-600 whitespace-nowrap">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="w-full max-w-[500px] h-[300px] relative mx-auto">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={(props) =>
                  renderCustomizedLabelWithLeaderLine({
                    ...props,
                    unit,
                  })
                }
                outerRadius={110}
                innerRadius={60}
                dataKey="percentage"
                nameKey="name"
                animationBegin={0}
                animationDuration={1000}
                animationEasing="ease-out"
              >
                {data.map((entry, index) => {
                  let color = entry.color;
                  if (title.toLowerCase().includes('intensity') && entry.name) {
                    const intensityColors: Record<string, string> = {
                      intense: '#BA324F',
                      moderate: '#FFAA5A',
                      mild: '#AB92BF',
                    };
                    color =
                      intensityColors[entry.name.toLowerCase()] || entry.color;
                  }
                  return <Cell key={`cell-${index}`} fill={color} />;
                })}
              </Pie>
              <Tooltip content={<CustomTooltip unit={unit} />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ActivityPieChart;
