import { FC, useMemo } from 'react';

import AppLayout from '@core/layout/shared/AppLayout';

import { useUserStore } from '@/store/userStore';

import colors from '@/utils/colors';

import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

import mrdNavigation from '@/core/configs/mrd/navigation-desktop';

const DesktopLayout: FC<{ children: React.ReactNode }> = ({ children }) => {
  const { permissions = [] } = useUserStore();

  const filteredNavigation = useMemo(() => {
    return mrdNavigation.filter((navItem) => {
      if (navItem.path === routes.MRD_DASHBOARD) {
        return permissions.includes(PERMISSION_KEYS.MRD_DASHBOARD_VIEW);
      }

      return true;
    });
  }, [permissions]);

  return (
    <AppLayout
      navItem={filteredNavigation}
      highlightColor={colors.sidebar.pink}
    >
      {children}
    </AppLayout>
  );
};

export default DesktopLayout;
