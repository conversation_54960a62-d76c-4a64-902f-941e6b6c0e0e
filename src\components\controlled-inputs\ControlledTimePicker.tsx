import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import AppTimePicker from '@/core/components/app-time-picker';
import { AppTimePickerProps } from '@/core/components/app-time-picker/types';

export type TimeValue = {
  hour: string;
  minute: string;
  period: string;
};

type Props<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  slotProps?: AppTimePickerProps['slotProps'];
};

const ControlledTimePicker = <T extends FieldValues>({
  name,
  control,
  label,
  disabled = false,
  ...rest
}: Props<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <AppTimePicker
          value={field.value}
          onChange={field.onChange}
          disabled={disabled}
          label={label}
          helperText={fieldState.error?.message}
          error={!!fieldState.error?.message}
          slotProps={rest.slotProps}
          {...rest}
        />
      )}
    />
  );
};

export default ControlledTimePicker;
