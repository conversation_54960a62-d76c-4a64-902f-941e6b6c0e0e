'use client';

import React, { useState, forwardRef, useEffect } from 'react';

import { InputAdornment, IconButton, TextFieldProps } from '@mui/material';

import PenIcon from '@/assets/svg/PenIcon';

import InputLabel from '../input-label';

import { StyledTextField } from './styed-component';

type OmittedTextFieldProps = Omit<
  TextFieldProps<'outlined'>,
  'variant' | 'onChange'
>;

export type AppTextFieldProps = OmittedTextFieldProps & {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  fullWidth?: boolean;
  size?: 'small' | 'medium';
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  label?: string;
  multiline?: boolean;
  rows?: number;
  maxRows?: number;
  className?: string;
  sx?: object;
  initiallyReadonly?: boolean;
  editIconClassName?: string;
  onEditToggle?: (isEditing: boolean) => void;
  isReadOnly?: boolean;
  isRequired?: boolean;
  formatValue?: (value: string) => string;
  endIcon?: React.ReactNode;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
};

const AppTextField = forwardRef<HTMLDivElement, AppTextFieldProps>(
  (
    {
      value = '',
      onChange,
      placeholder = '',
      fullWidth = false,
      size = 'small',
      disabled = false,
      error = false,
      helperText,
      label,
      multiline = false,
      rows,
      maxRows,
      className,
      sx,
      initiallyReadonly = false,
      editIconClassName = '',
      onEditToggle,
      isReadOnly: textFieldReadOnly = false,
      required,
      formatValue = (value) => value,
      endIcon,
      onKeyDown,
      ...rest
    },
    ref
  ) => {
    const [isEditing, setIsEditing] = useState(!initiallyReadonly || !value);
    const [internalValue, setInternalValue] = useState(value);

    const handleEditToggle = () => {
      const newEditingState = !isEditing;
      setIsEditing(newEditingState);
      onEditToggle?.(newEditingState);
    };

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      const formattedValue = formatValue(newValue);
      setInternalValue(formattedValue);
      onChange?.(formattedValue);
    };

    const shouldShowEditIcon = initiallyReadonly && value && !isEditing;
    const isReadOnly = initiallyReadonly && value && !isEditing;

    useEffect(() => {
      setInternalValue(value);
    }, [value]);

    return (
      <div className="w-full">
        <InputLabel
          label={label}
          required={required}
          className="mb-0 md:mb-0 text-sm md:text-sm"
        />
        <StyledTextField
          ref={ref}
          value={internalValue}
          onChange={handleChange}
          placeholder={placeholder}
          fullWidth={fullWidth}
          size={size}
          disabled={disabled}
          required={required}
          error={error}
          helperText={helperText}
          multiline={multiline}
          rows={rows}
          maxRows={maxRows}
          className={className}
          sx={sx}
          slotProps={{
            input: {
              readOnly: Boolean(isReadOnly || textFieldReadOnly),
              // Prevent browser tooltip by not setting required on the input element
              required: false,
              onKeyDown,
              endAdornment: endIcon ? (
                <InputAdornment position="end">{endIcon}</InputAdornment>
              ) : shouldShowEditIcon ? (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleEditToggle}
                    edge="end"
                    aria-label="edit field"
                    className={editIconClassName}
                  >
                    <PenIcon className="w-4.5 h-4.5" />
                  </IconButton>
                </InputAdornment>
              ) : undefined,
              sx: {
                cursor: isReadOnly || textFieldReadOnly ? 'default' : 'text',
                ...((isReadOnly || textFieldReadOnly) && {
                  '& input': {
                    cursor: 'default',
                  },
                }),
              },
            },
          }}
          {...rest}
        />
      </div>
    );
  }
);

AppTextField.displayName = 'AppTextField';

export default AppTextField;
