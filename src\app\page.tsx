'use client';

import { useState, useEffect } from 'react';

import { useIsAuthenticated } from '@azure/msal-react';

import { useRouter } from 'next/navigation';

import { login, getActiveAccount } from '@core/lib/auth/services';

import Loading from '@/lib/common/loading';

import { useUserStore } from '@/store/userStore';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';

import { LOCAL_STORAGE_KEYS } from '@/constants/local-storage';
import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

import { AuthLayout } from '@/core/layout/auth/AuthLayout';

// Helper function to check if user has any of the required permissions
const hasAnyPermission = (
  userPermissions: string[],
  requiredPermissions: string[]
): boolean => {
  return requiredPermissions.some((permission) =>
    userPermissions.includes(permission)
  );
};

const HomePage = () => {
  const router = useRouter();
  const isAuthenticated = useIsAuthenticated();
  const [isLoading, setIsLoading] = useState(false);

  const setPermissions = useUserStore((state) => state.setPermissions);

  async function handleLogin() {
    setIsLoading(true);
    try {
      await login();

      // Fetch and handle permissions
      const permissions = await fetchUserPermissions();

      const handleUserPermissions = async (permissions: string[]) => {
        setPermissions(permissions);
      };
      await handleUserPermissions(permissions);

      const hasMrdAccess = hasAnyPermission(permissions, ['mrd.access']);
      const hasEmrAccess = hasAnyPermission(permissions, ['emr.access']);

      if (hasMrdAccess && hasEmrAccess) {
        router.push(routes.SELECT_APP);
      } else if (hasMrdAccess) {
        const hasMrdDashboardView = permissions.includes(
          PERMISSION_KEYS.MRD_DASHBOARD_VIEW
        );
        if (hasMrdDashboardView) {
          router.push(routes.MRD_DASHBOARD);
        } else {
          router.push(routes.MRD_MANAGE_PATIENTS);
        }
      } else if (hasEmrAccess) {
        const hasEmrDashboardView = permissions.includes(
          PERMISSION_KEYS.EMR_DASHBOARD_VIEW
        );
        if (hasEmrDashboardView) {
          router.push(routes.EMR_DASHBOARD);
        } else {
          router.push(routes.EMR_PATIENT_INFO);
        }
      } else {
        console.warn('User has no access to either MRD or EMR');
      }
    } catch (error) {
      console.error('Login failed:', error);
    } finally {
      setIsLoading(false);
    }
  }

  async function fetchUserPermissions(): Promise<string[]> {
    try {
      const account = getActiveAccount();
      if (!account) {
        console.error('No active account found');
        throw new Error('No active account found');
      }

      const response = await getDoctorProfile();

      // Extract permissions from the response
      let permissions: string[] = [];

      // Handle the response format from getDoctorProfile
      if (response?.data && response.data.length > 0) {
        const userData = response.data[0];
        // Check for permissions in both the root and permissions property for backward compatibility
        if (userData.permissionKeys) {
          permissions = Array.isArray(userData.permissionKeys)
            ? userData.permissionKeys
            : [userData.permissionKeys];
        } else if (userData.permissions) {
          permissions = Array.isArray(userData.permissions)
            ? userData.permissions
            : [userData.permissions];
        }
      }

      // Update user store with permissions
      setPermissions(permissions);

      // Also return the permissions for immediate use
      return permissions;
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      return [];
    }
  }

  async function handleUserPermissions(permissions: string[]) {
    try {
      // Check for redirectTo in localStorage and redirect if present
      const redirectTo =
        typeof window !== 'undefined'
          ? localStorage.getItem(LOCAL_STORAGE_KEYS.REDIRECT_TO)
          : null;
      if (redirectTo) {
        localStorage.removeItem(LOCAL_STORAGE_KEYS.REDIRECT_TO);
        await router.replace(redirectTo);
        return;
      }
      // Check for selectedApp in localStorage and redirect if present
      const selectedApp =
        typeof window !== 'undefined'
          ? localStorage.getItem(LOCAL_STORAGE_KEYS.SELECTED_APP)
          : null;
      if (selectedApp) {
        localStorage.removeItem(LOCAL_STORAGE_KEYS.SELECTED_APP);
        await router.replace(selectedApp);
        return;
      }
      // Ensure we have a valid array of permissions
      if (!Array.isArray(permissions)) {
        console.error('Permissions is not an array:', permissions);
        permissions = [];
      }

      // Log all permissions for debugging

      // Check for access permissions (case sensitive)
      const hasMRDAccess = permissions.includes(PERMISSION_KEYS.MRD_ACCESS);
      const hasEMRAccess = permissions.includes(PERMISSION_KEYS.EMR_ACCESS);

      // If user has both permissions, redirect to select app page
      if (hasMRDAccess && hasEMRAccess) {
        await router.replace(routes.SELECT_APP);
        return;
      }
      // If user has only MRD access
      if (hasMRDAccess) {
        const hasMrdDashboardView = permissions.includes(
          PERMISSION_KEYS.MRD_DASHBOARD_VIEW
        );
        if (hasMrdDashboardView) {
          await router.replace(routes.MRD_DASHBOARD);
        } else {
          await router.replace(routes.MRD_MANAGE_PATIENTS);
        }
        return;
      }

      if (hasEMRAccess) {
        const hasEmrDashboardView = permissions.includes(
          PERMISSION_KEYS.EMR_DASHBOARD_VIEW
        );
        if (hasEmrDashboardView) {
          await router.replace(routes.EMR_DASHBOARD);
        } else {
          await router.replace(routes.EMR_PATIENT_INFO);
        }
        return;
      }

      // If we get here, no valid permissions were found
      console.error(
        'User has no valid access permissions. Available permissions:',
        permissions
      );
      await router.replace(routes.UNAUTHORIZED);
    } catch (error) {
      console.error('Error handling permissions:', error);
      router.replace(routes.UNAUTHORIZED);
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      const initializeAuth = async () => {
        const permissions = await fetchUserPermissions();
        handleUserPermissions(permissions);
      };
      initializeAuth();
    }
  }, [isAuthenticated]);

  if (isLoading || isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loading />
      </div>
    );
  }

  // Show login page by default
  return (
    <AuthLayout title="Welcome to ArcaAI" subtitle="Sign in to continue">
      <button
        onClick={handleLogin}
        className="w-full flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loading />
            <span className="ml-2">Signing in...</span>
          </>
        ) : (
          'Sign in'
        )}
      </button>
    </AuthLayout>
  );
};

export default HomePage;
