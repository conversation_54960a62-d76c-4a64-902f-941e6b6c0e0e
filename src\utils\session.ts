import { throttle } from 'lodash';

import API_CONFIG from '@/core/configs/api';
import { logout } from '@/core/lib/auth/services';

const IDLE_THRESHOLD = 1000 * 60 * 45; // 45 minutes
let debounceTimeout: NodeJS.Timeout | null = null;
let isRecordingActive = false;

const handleIdle = () => {
  if (!isRecordingActive) {
    logout();
  }
};

export const setRecordingActive = (isActive: boolean) => {
  isRecordingActive = isActive;
  if (isActive) {
    // Reset the timeout when recording starts
    updateLastActivity();
  }
};

export const isRecordingInProgress = (): boolean => {
  return isRecordingActive;
};

export const updateLastActivity = () => {
  if (API_CONFIG.NODE_ENV === 'development') return;

  // Don't update activity if recording is in progress
  if (isRecordingActive) return;

  if (debounceTimeout) {
    clearTimeout(debounceTimeout);
  }
  debounceTimeout = setTimeout(() => {
    handleIdle();
  }, IDLE_THRESHOLD);
};

export const throttledUpdateLastActivity = throttle(updateLastActivity, 10000, {
  trailing: false,
});
