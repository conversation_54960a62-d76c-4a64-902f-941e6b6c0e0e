import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { getPatientById as getPatientByIdMRD } from '@/query/mrd/manage-patient/manage';

import { PatientI } from '../types/index';

type currentPatientStoreType = {
  patient: PatientI | null;
  isPatientFromQueue: boolean;
  isActivePatient: boolean;
  setPatient: (_data: PatientI | null) => Promise<void>;
  setIsPatientFromQueue: (isFromQueue: boolean) => void;
  setIsActivePatient: (isActive: boolean) => void;
  updateIsActivePatient: (params: {
    currentAppointmentPatientId?: string | null;
    searchPatientId?: string | null;
    patientId?: string | null;
    isPatientFromQueue?: boolean;
  }) => void;
  clear: () => void;
};

export const useCurrentPatientStore = create<currentPatientStoreType>()(
  persist(
    (set, get) => ({
      patient: null,
      isPatientFromQueue: false,
      isActivePatient: false,

      setPatient: async (data) => {
        if (data?.id && !data.cmcId) {
          try {
            const latest = await getPatientByIdMRD(data.id);
            if (latest) {
              const mapped: PatientI = {
                id: latest.id || '',
                name: latest.name || '',
                sex: latest.sex || '',
                age: String(latest.age ?? ''),
                maritalStatus: latest.maritalStatus || '',
                dob:
                  typeof latest.dob === 'string'
                    ? latest.dob
                    : latest.dob instanceof Date
                      ? latest.dob.toISOString()
                      : '',
                height:
                  latest.height !== undefined && latest.height !== null
                    ? String(latest.height)
                    : '',
                weight:
                  latest.weight !== undefined && latest.weight !== null
                    ? String(latest.weight)
                    : '',
                address: latest.address || '',
                aadhar: latest.aadhar || '',
                abha: latest.abha || '',
                contact: {
                  phone: latest.contact?.phone || '',
                  email: latest.contact?.email || '',
                },
                insurance: {
                  provider: latest.insurance?.provider || '',
                  id: latest.insurance?.id || '',
                },
                last_consultation_date: latest.last_consultation_date || null,
                cmcId: (latest as any)?.cmcId || undefined,
                vitals: [],
                created_on: '',
                updated_on: '',
                _rid: '',
                _self: '',
                _etag: '',
                _attachments: '',
                _ts: 0,
              };

              set({ patient: mapped });
              return;
            }
          } catch (error) {
            console.error('Error fetching complete patient data:', error);
          }
        }

        set({ patient: data });
      },

      setIsPatientFromQueue: (isFromQueue) => {
        set({ isPatientFromQueue: isFromQueue });
        // Recalculate isActivePatient when isPatientFromQueue changes

        if (isFromQueue) {
          set({ isActivePatient: false });
        }
      },

      setIsActivePatient: (isActive) => {
        set({ isActivePatient: isActive });
      },

      updateIsActivePatient: (params) => {
        const {
          currentAppointmentPatientId,
          searchPatientId,
          patientId,
          isPatientFromQueue = get().isPatientFromQueue,
        } = params;

        // Update isPatientFromQueue if provided
        if (params.isPatientFromQueue !== undefined) {
          set({ isPatientFromQueue: params.isPatientFromQueue });
        }

        // Calculate isActivePatient
        if (isPatientFromQueue) {
          set({ isActivePatient: false });
        } else {
          const isActive =
            currentAppointmentPatientId === searchPatientId ||
            (patientId || get().patient?.id) === searchPatientId;
          set({ isActivePatient: isActive });
        }
      },

      clear: () =>
        set({
          patient: null,
          isPatientFromQueue: false,
          isActivePatient: false,
        }),
    }),
    {
      name: 'current-patient-store',
    }
  )
);
