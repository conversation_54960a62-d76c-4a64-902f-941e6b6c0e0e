import {
  ButtonHTMLAttributes,
  DetailedHTMLProps,
  FC,
  useEffect,
  useState,
  useRef,
} from 'react';

import { MdDragIndicator } from 'react-icons/md';

import { formatAddress } from '@/utils/mrd/manage-patient/format-address';

import {
  AppointmentStatus,
  PatientStatus,
  PatientQueueLabStatus,
} from '@/constants/mrd/manage-patient/consultation';

import Avatar from '@/views/mrd/queue/queue-card/avatar';

type QueueCardPatient = {
  name: string;
  sex: string;
  address: string;
  age: number;
  id: string;
  dob: string;
};

export interface QueueCardProps {
  appointmentId?: string;
  queueId: string;
  patientId: string;
  patient: QueueCardPatient;
  queuePosition: number;
  status: AppointmentStatus;
  patientStatus?: PatientStatus;
  time: string;
  isSelected?: boolean;
  isPatientFromQueue?: boolean;
  onClickMove?: (data: QueueCardProps) => void;
  onClickMarkAsConsulted?: (data: QueueCardProps) => void;
  onClickCancel?: (data: QueueCardProps) => void;
  onMoveDown?: (item?: QueueCardProps) => Promise<void>;
  onSelect?: (e: React.MouseEvent) => void;
  onClick?: () => void;
  labTestStatus?: PatientQueueLabStatus;
}

const QueueCard: FC<QueueCardProps> = (props) => {
  const {
    patient,
    status,
    patientStatus,
    queuePosition,
    isSelected = false,
    isPatientFromQueue = false,
    onClickMove = () => {},
    onClickMarkAsConsulted = () => {},
    onMoveDown = () => {},
    onSelect = () => {},
  } = props;

  const cardRef = useRef<HTMLDivElement>(null);

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger selection if clicking on buttons or links
    const target = e.target as HTMLElement;
    if (
      !(target instanceof HTMLButtonElement) &&
      !(target instanceof HTMLAnchorElement) &&
      !(target instanceof SVGElement) &&
      !(target.parentElement instanceof HTMLButtonElement) &&
      !(target.parentElement instanceof HTMLAnchorElement)
    ) {
      onSelect?.(e);
    }
  };

  const [localStatus, setLocalStatus] = useState<'active' | 'inQueue'>(
    'active'
  );

  const handleClickMove = () => {
    onClickMove(props);
  };

  const handleClickMarkAsConsulted = () => {
    onClickMarkAsConsulted(props);
  };

  useEffect(() => {
    if (status === AppointmentStatus.Consultation) {
      setLocalStatus('active');
    } else {
      setLocalStatus('inQueue');
    }
  }, [status]);

  const Separator = ({ className = '' }) => {
    return (
      <span
        className={`inline-block w-[4px] h-[4px] rounded-full bg-[#A5B2BD] ${className}`}
      ></span>
    );
  };

  // Only show blue border when explicitly selected
  const borderClass =
    isSelected && isPatientFromQueue
      ? 'border border-[#53BDF5]'
      : 'border border-[#DAE1E7]';

  return (
    <div
      ref={cardRef}
      onClick={handleCardClick}
      className={`py-1 w-full px-1 flex items-center rounded-[5px] bg-white ${borderClass} grid grid-cols-12 cursor-pointer `}
    >
      <div className="col-span-6 flex items-center gap-1 py-1 w-full">
        <div className="w-4"></div>
        <div className="flex items-center gap-2 ">
          <MdDragIndicator className="cursor-grab text-lg text-[#323F49]" />
          <Avatar
            name={patient?.name}
            status={status}
            patientStatus={patientStatus}
            queuePosition={queuePosition}
          />
        </div>
        <div className="flex flex-col text-[#001926] w-[calc(100%-3.5rem)] 2xl:w-[calc(100%-4rem)]">
          <span className="text-sm 2xl:text-base capitalize font-medium -tracking-[2.2%] w-full  text-[14px]">
            {patient?.name}
          </span>
          <div className="flex items-center gap-1 whitespace-nowrap w-full text-[#64707D]">
            <span className="font-light text-xs -tracking-[2.2%] w-fit">
              {patient?.age} yrs
            </span>
            <Separator className="h-4" />
            <span className="font-light text-xs -tracking-[2.2%] w-fit">
              {patient?.sex && typeof patient?.sex === 'string'
                ? patient?.sex.charAt(0).toUpperCase()
                : ''}
            </span>
            <Separator className="h-4" />
            <span className="font-light text-xs -tracking-[2.2%] w-full truncate">
              {formatAddress(patient?.address)}
            </span>
          </div>
        </div>
      </div>

      <div
        className={`flex px-1 2xl:px-2 gap-1 col-span-6 overflow-hidden ${
          localStatus === 'active' ? 'border-l border-[#C2CDD6]' : ''
        }`}
      >
        {localStatus === 'active' ? (
          <>
            <OutLinedButton onClick={() => onMoveDown(props)}>
              Move to queue
            </OutLinedButton>
            <OutLinedButton onClick={handleClickMarkAsConsulted}>
              Mark as Consulted
            </OutLinedButton>
          </>
        ) : localStatus === 'inQueue' ? (
          <>
            {/* Empty placeholder to maintain layout alignment */}
            <div className="flex-1" />
            <OutLinedButton onClick={handleClickMove}>
              Consult Now
            </OutLinedButton>
          </>
        ) : null}
      </div>

      <div className="col-span-12 w-full border-t border-[#ECECEC]" />
      <div className="col-span-12 px-2 pt-2 pb-2">
        <div className="text-[13px] text-[#64707D] mb-1">Lab Reports</div>
        <div className="relative h-3 flex items-center w-full">
          {/* Slider bar */}
          <div className="absolute top-1/2 left-0 w-full h-1 bg-[#E9F4FA] rounded-full -translate-y-1/2" />

          {/* Colored progress bar */}
          {props.labTestStatus ===
            PatientQueueLabStatus.LAB_REPORT_AWAITING && (
            <div className="absolute top-1/2 left-0 w-1/2 h-1 bg-[#0B4D6D] rounded-full -translate-y-1/2" />
          )}
          {props.labTestStatus === PatientQueueLabStatus.LAB_REPORT_READY && (
            <div className="absolute top-1/2 left-0 w-full h-1 bg-[#0B4D6D] rounded-full -translate-y-1/2" />
          )}

          {/* Dots */}
          <span className="absolute -left-0.5 h-4 w-4 rounded-full bg-[#D3D8DE]" />
          <span
            className={
              'absolute left-1/2 -translate-x-1/2 h-4 w-4 rounded-full ' +
              (props.labTestStatus === PatientQueueLabStatus.LAB_REPORT_READY
                ? 'bg-[#FF9F2A]'
                : props.labTestStatus ===
                    PatientQueueLabStatus.LAB_REPORT_AWAITING
                  ? 'bg-[#FF9F2A]'
                  : 'bg-[#FBE5CC]')
            }
          />
          <span
            className={
              'absolute -right-0.5 h-4 w-4 rounded-full ' +
              (props.labTestStatus === PatientQueueLabStatus.LAB_REPORT_READY
                ? 'bg-[#06C6A7]'
                : 'bg-[#CCF5EE]')
            }
          />
        </div>
      </div>
    </div>
  );
};

// Export the component as default
export default QueueCard;

// Export the types
export type { QueueCardPatient };

const OutLinedButton: React.FC<
  DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>
> = ({ children, ...props }) => {
  return (
    <button
      {...props}
      type={props.type as 'submit' | 'reset' | 'button' | undefined}
      className="flex-1 rounded-full flex justify-evenly items-center whitespace-nowrap border border-black py-0.5 px-0.5 font-light -tracking-[2.2%] text-[10px] 2xl:text-[11px]"
    >
      {children}
    </button>
  );
};
