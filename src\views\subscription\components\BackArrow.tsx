'use client';

import { FaArrowLeft } from 'react-icons/fa';

import { useRouter } from 'next/navigation';

interface BackArrowProps {
  className?: string;
  onClick?: () => void;
}

const BackArrow: React.FC<BackArrowProps> = ({ className = '', onClick }) => {
  const router = useRouter();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      router.back();
    }
  };

  return (
    <div className="mb-6">
      <button
        onClick={handleClick}
        className={`flex items-center text-gray-600 hover:text-gray-800 transition-colors ${className}`}
      >
        <FaArrowLeft />
      </button>
    </div>
  );
};

export default BackArrow;
