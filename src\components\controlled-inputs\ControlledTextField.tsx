import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import AppTextField, {
  AppTextFieldProps,
} from '@/core/components/app-text-field';

type Props<T extends FieldValues> = AppTextFieldProps & {
  name: Path<T>;
  control: Control<T>;
  rules?: any;
};

const ControlledTextField = <T extends FieldValues>({
  name,
  control,
  rules,
  required,
  onKeyDown,
  ...props
}: Props<T>) => {
  // Determine if field is required based on rules
  const isRequired = required || (rules && rules.required);

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => (
        <AppTextField
          {...field}
          {...props}
          onKeyDown={onKeyDown}
          required={isRequired}
          error={!!fieldState.error?.message}
          helperText={fieldState.error?.message}
        />
      )}
    />
  );
};

export default ControlledTextField;
