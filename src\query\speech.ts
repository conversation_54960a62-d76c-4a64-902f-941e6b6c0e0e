import axios from 'axios';

import API_CONFIG from '@/core/configs/api';
import { api, arcaAxios } from '@/core/lib/interceptor';

const { SPEECH_ENGINE_SUBSCRIPTION_KEY } = API_CONFIG;

export async function getSpeechToken() {
  const headers = {
    headers: {
      'Ocp-Apim-Subscription-Key': SPEECH_ENGINE_SUBSCRIPTION_KEY,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };

  try {
    const tokenResponse = await axios.post(
      `https://eastus.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
      null,
      headers
    );
    return tokenResponse.data;
  } catch (err) {
    console.error(err);
  }
}

type SummarizeConversationApiRes = {
  conversation: [
    {
      speaker: 'doctor' | 'patient';
      message: string;
    },
  ];
  summary: {
    presentingcomplaint: string;
    historyofpresenting: string;
    pastmedicalhistory: string;
    pastsurgicalhistory: string;
    familyhistory: string;
    addictionhistory: string;
    diethistory: string;
    physicalactivityhistory: string;
    stresshistory: string;
    sleephistory: string;
    currentmedicationhistory: string;
    vitals?: {
      heartRate?: number;
      systolicPressure?: number;
      diastolicPressure?: number;
      respiratoryRate?: number;
      spO2?: number;
      temperature?:
        | number
        | {
            value: number;
            unit: 'Celsius' | 'Fahrenheit';
          };
    };
    anthropometry?: {
      height?: number | string;
      weight?: number | string;
      bmi?: number | string;
      waistCircumference?: number | string;
    };
    generalphysicalexamination?: {
      pallor?: boolean;
      icterus?: boolean;
      cyanosis?: boolean;
      clubbing?: boolean;
      pedalEnema?: boolean;
      lymphadenopathy?: boolean;
      notes?: string;
      pedalEnemaNotes?: string;
      lymphadenopathyNotes?: string;
    };
    heent?: string;
    systemicexamination?: {
      neurologicalExamination?: string;
      cardiovascularExamination?: string;
      respiratoryExamination?: string;
      abdomenExamination?: string;
      rheumatologicalExamination?: string;
    };
  };
};

export type Conversation = {
  speaker: 'doctor' | 'patient';
  message: string;
};

export type SummarizeConversationRes = {
  conversation: Conversation[];
  summary: {
    owner?: {
      id: string;
      role: string;
      name: string;
      email: string;
    };
    conversation?: Conversation[];
    // CamelCase properties
    presentingComplaints?: string;
    historyOfPresenting?: string;
    pastMedicalHistory?: string;
    pastSurgicalHistory?: string;
    familyHistory?: string;
    addictionHistory?: string;
    dietHistory?: string;
    physicalActivityHistory?: string;
    stressHistory?: string;
    sleepHistory?: string;
    currentMedicationHistory?: string;

    // Snake_case properties from API
    presentingcomplaint?: string;
    historyofpresenting?: string;
    pastmedicalhistory?: string;
    pastsurgicalhistory?: string;
    familyhistory?: string;
    addictionhistory?: string;
    diethistory?: string;
    physicalactivityhistory?: string;
    stresshistory?: string;
    sleephistory?: string;
    currentmedicationhistory?: string;
    recordingDuration?: number;
    vitals?: {
      heartRate?: number;
      systolicPressure?: number;
      diastolicPressure?: number;
      respiratoryRate?: number;
      spO2?: number;
      temperature?:
        | number
        | {
            value: number;
            unit: 'Celsius' | 'Fahrenheit';
          };
    };
    anthropometry?: {
      height?: number | string;
      weight?: number | string;
      bmi?: number | string;
      waistCircumference?: number | string;
    };
    generalPhysicalExamination?: {
      pallor?: boolean;
      icterus?: boolean;
      cyanosis?: boolean;
      clubbing?: boolean;
      pedalEnema?: boolean;
      lymphadenopathy?: boolean;
      notes?: string;
      pedalEnemaNotes?: string;
      lymphadenopathyNotes?: string;
    };
    heent?: string;
    systemicExamination?: {
      neurologicalExamination?: string;
      cardiovascularExamination?: string;
      respiratoryExamination?: string;
      abdomenExamination?: string;
      rheumatologicalExamination?: string;
    };
  };
};

export async function summarizeAmbientListening(
  source: string,
  transcript: string,
  userId?: string,
  patientId?: string
) {
  // Format the transcript with proper line breaks

  const { data } = await api.post<SummarizeConversationApiRes>(
    '/lifestyle/v0.1/lifestyle/ambient-listening',
    {
      source,
      transcript,
      ...(userId && { userId }),
      ...(patientId && { patientId }),
    }
  );

  return data;
}

export async function retryAmbientListening(
  userId: string,
  patientId: string,
  source: string
) {
  const { data } = await api.post<SummarizeConversationApiRes>(
    '/lifestyle/v0.1/retry/lifestyle/ambient-listening',
    {
      userId,
      patientId,
      source,
    }
  );

  return data;
}

export async function retryConsultationSummary(
  userId: string,
  patientId: string
) {
  const { data } = await api.post<SummarizeConversationApiRes>(
    '/consultation/v0.1/retry/summary',
    {
      userId,
      patientId,
    }
  );

  return data;
}

function processAnthropometry(
  anthropometry: SummarizeConversationApiRes['summary']['anthropometry']
) {
  if (!anthropometry) {
    return anthropometry;
  }

  let height = anthropometry.height;
  if (typeof height === 'string') {
    const heightValue = parseFloat(height);
    if (!isNaN(heightValue)) {
      const lowerHeight = height.toLowerCase();
      if (
        lowerHeight.includes('centimeter') ||
        lowerHeight.includes('centimetre') ||
        lowerHeight.includes('cm')
      ) {
        height = heightValue; // Already in cm
      } else if (
        lowerHeight.includes('meter') ||
        lowerHeight.includes('metre') ||
        lowerHeight.match(/\bm\b/)
      ) {
        height = heightValue * 100;
      } else {
        height = heightValue;
      }
    }
  }

  let weight = anthropometry.weight;
  if (typeof weight === 'string') {
    const weightValue = parseFloat(weight.replace(/,/g, ''));
    if (!isNaN(weightValue)) {
      if (weight.includes('kilogram') || weight.includes('kg')) {
        weight = weightValue;
      } else if (weight.includes('gram')) {
        weight = weightValue / 1000;
      }
    }
  }

  let waistCircumference = anthropometry.waistCircumference;
  if (typeof waistCircumference === 'string') {
    const waistCircumferenceValue = parseFloat(waistCircumference);
    if (!isNaN(waistCircumferenceValue)) {
      const lowerWaist = waistCircumference.toLowerCase();
      if (
        lowerWaist.includes('centimeter') ||
        lowerWaist.includes('centimetre') ||
        lowerWaist.includes('cm')
      ) {
        waistCircumference = waistCircumferenceValue;
      } else if (
        lowerWaist.includes('meter') ||
        lowerWaist.includes('metre') ||
        lowerWaist.match(/\bm\b/)
      ) {
        waistCircumference = waistCircumferenceValue * 100; // Convert meters to cm
      } else {
        waistCircumference = waistCircumferenceValue;
      }
    }
  }

  return {
    ...anthropometry,
    height: typeof height === 'number' ? height : anthropometry.height,
    weight: typeof weight === 'number' ? weight : anthropometry.weight,
    waistCircumference:
      typeof waistCircumference === 'number'
        ? waistCircumference
        : anthropometry.waistCircumference,
  };
}

function processVitals(
  vitals: SummarizeConversationApiRes['summary']['vitals']
) {
  if (!vitals) {
    return vitals;
  }

  let temperature = vitals.temperature;
  let processedTemperature: number | undefined = undefined;

  // Handle temperature conversion if it's an object with unit
  if (typeof temperature === 'object' && temperature !== null) {
    const { value, unit } = temperature;

    // Only process if value is a valid number and not 0
    if (value && !isNaN(value) && value !== 0) {
      if (unit === 'Celsius') {
        // Convert Celsius to Fahrenheit: F = (C * 9/5) + 32
        processedTemperature = (value * 9) / 5 + 32;
      } else if (unit === 'Fahrenheit') {
        // Keep Fahrenheit as is
        processedTemperature = value;
      }
    }
  } else if (typeof temperature === 'number' && temperature !== 0) {
    // If it's already a number and not 0, keep it
    processedTemperature = temperature;
  }

  return {
    ...vitals,
    temperature: processedTemperature,
  };
}

export async function summarizeConversation(
  transcript: string,
  userId?: string,
  patientId?: string
): Promise<SummarizeConversationRes> {
  const { data } = await arcaAxios.post<SummarizeConversationApiRes>(
    '/summary',
    {
      text: transcript,
      ...(userId && { userId }),
      ...(patientId && { patientId }),
    }
  );

  const res = {
    conversation: data.conversation,
    summary: {
      presentingComplaints: data.summary.presentingcomplaint,
      historyOfPresenting: data.summary.historyofpresenting,
      pastMedicalHistory: data.summary.pastmedicalhistory,
      pastSurgicalHistory: data.summary.pastsurgicalhistory,
      familyHistory: data.summary.familyhistory,
      addictionHistory: data.summary.addictionhistory,
      dietHistory: data.summary.diethistory,
      physicalActivityHistory: data.summary.physicalactivityhistory,
      stressHistory: data.summary.stresshistory,
      sleepHistory: data.summary.sleephistory,
      currentMedicationHistory: data.summary.currentmedicationhistory,
      vitals: processVitals(data.summary.vitals),
      anthropometry: processAnthropometry(data.summary.anthropometry),
      generalPhysicalExamination: data.summary.generalphysicalexamination,
      heent: data.summary.heent,
      systemicExamination: data.summary.systemicexamination,
    },
  };

  return res;
}
