import React, { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import { clearSubscriptionUserData } from '@/utils/subscription';

import AppModal from '@/core/components/app-modal';
import { SuccessIcon } from '@/core/components/status-modal/modal-icons';

interface PaymentSuccessModalProps {
  open: boolean;
  onClose: () => void;
  isTrial?: boolean;
  billingCycle?: 'monthly' | 'yearly';
  isFromProfile?: boolean;
}

const PaymentSuccessModal: React.FC<PaymentSuccessModalProps> = ({
  open,
  onClose,
  isTrial = false,
  billingCycle = 'monthly',
  isFromProfile = false,
}) => {
  const router = useRouter();

  useEffect(() => {
    if (open) {
      if (isFromProfile) {
        // If from profile, auto close after 5 seconds but don't redirect
        const timer = setTimeout(() => {
          handleCloseOnly();
        }, 5000);

        return () => clearTimeout(timer);
      } else {
        // If not from profile, redirect to signup after 5 seconds
        const timer = setTimeout(() => {
          handleCloseAndRedirect();
        }, 5000);

        return () => clearTimeout(timer);
      }
    }
  }, [open, isFromProfile]);

  const handleCloseOnly = () => {
    // Clear all subscription selection data
    clearSubscriptionUserData();

    // Just close modal - no redirection for profile users
    onClose();
  };

  const handleCloseAndRedirect = () => {
    // Clear all subscription selection data
    clearSubscriptionUserData();

    // Close modal
    onClose();

    // Only redirect to signup if not from profile
    if (!isFromProfile) {
      router.push('/subscription/signup');
    }
  };

  const renderContent = () => {
    if (isFromProfile) {
      // Profile flow messages
      if (isTrial) {
        return (
          <>
            <p className="text-base text-[#001926] mb-2">
              Your plan upgraded successfully!
            </p>
            <p className="text-sm text-gray-600 mt-2">
              Your trial has been activated successfully.
            </p>
          </>
        );
      } else {
        return (
          <>
            <p className="text-base text-[#001926] mb-2">
              Your plan upgraded successfully!
            </p>
            <p className="text-sm text-gray-600 mt-2">
              Your payment has been processed and plan upgraded.
            </p>
          </>
        );
      }
    } else {
      // Non-profile flow messages
      if (isTrial) {
        const daysText =
          billingCycle === 'yearly'
            ? '365 Days Remaining!'
            : '30 Days Remaining!';
        const message = `You are on Free trial Now!`;
        return (
          <>
            <p className="text-base text-[#001926] mb-2">{message}</p>
            <p className="text-base font-semibold" style={{ color: '#0496E1' }}>
              {daysText}
            </p>
            <p className="text-sm text-gray-600 mt-2">
              Please check your email to activate your account.
            </p>
          </>
        );
      } else {
        return (
          <>
            <p className="text-base text-[#001926] mb-2">
              Payment Processed
              <br />
              Successfully!
            </p>
            <p className="text-sm text-gray-600 mt-2">
              Please check your email to activate your account.
            </p>
          </>
        );
      }
    }
  };

  return (
    <AppModal
      open={open}
      onClose={handleCloseAndRedirect}
      classes={{
        root: 'w-[350px] !rounded-3xl relative',
        modal: '!rounded-3xl',
        header: 'hidden',
      }}
    >
      <div className="px-6 pb-4 pt-0 text-center">
        <div className="flex justify-center mb-4">
          <div className="scale-75">
            <SuccessIcon />
          </div>
        </div>
        {renderContent()}
      </div>
    </AppModal>
  );
};

export default PaymentSuccessModal;
