import { FC } from 'react';

import { BiSearch } from 'react-icons/bi';

import Loading from '@/lib/common/loading';
import { Pagination } from '@/lib/Pagination';
import QueueCard, { QueueCardProps } from '@/lib/QueueCard';

interface QueueListProps {
  queue: QueueCardProps[];
  isLoading?: boolean;
  onRearrange?: (queue: QueueCardProps[]) => void;
  onStartConsultation?: (item: QueueCardProps) => void;
  onCancelAppointment?: (item: QueueCardProps) => void;
  onMarkAsConsulted?: (item: QueueCardProps) => void;
  onSelectPatient?: (item: QueueCardProps) => void;
  hasConsultingNow?: boolean;
  selectedPatientId?: string | null;
  isPatientFromQueue?: boolean;
  searchQuery?: string;
  onSearch?: (e: React.ChangeEvent<HTMLInputElement>) => void;

  // Pagination props
  totalItems?: number;
  currentPage?: number;
  pageSize?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
}

const QueueList: FC<QueueListProps> = ({
  queue = [],
  isLoading = false,
  onStartConsultation = () => {},
  onCancelAppointment = () => {},
  onMarkAsConsulted = () => {},
  onSelectPatient = () => {},
  selectedPatientId,
  isPatientFromQueue = false,
  searchQuery = '',
  onSearch,
  hasConsultingNow,

  // Pagination props with defaults
  totalItems = 0,
  currentPage = 1,
  pageSize = 10,
  totalPages = 1,
  onPageChange = () => {},
  onPageSizeChange = () => {},
}) => {
  const getKey = (item: QueueCardProps): string => {
    return `${item.appointmentId}+${item.queueId}`;
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 flex flex-col gap-2 h-full">
        <div className="sticky top-0 bg-white z-10 pb-2">
          <h2
            className="text-base -tracking-[2.2%] font-medium text-[#637D92] mb-2 mt-2"
            style={{ fontWeight: 500, fontSize: '16px' }}
          >
            Patient Queue
          </h2>
          {onSearch && (
            <div className="relative -mx-1 px-1">
              <input
                type="text"
                placeholder="Search by name, patient id"
                className="w-full pl-3 pr-10 py-2 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-0 text-sm font-normal transition-colors duration-200"
                style={{ fontSize: '14px' }}
                value={searchQuery}
                onChange={onSearch}
              />
              <BiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          )}
        </div>

        {isLoading ? (
          <Loading />
        ) : (
          <div
            className={`overflow-y-auto transition-all duration-300 ${
              hasConsultingNow
                ? 'max-h-[calc(67vh-12.6rem)]' // shorter when consulting patient exists
                : 'max-h-[calc(67vh-9.2rem)]' // taller when no patient consulting
            }`}
          >
            <div className="flex flex-col gap-1">
              {queue.map((item, index) => (
                <div key={getKey(item)} className="w-full ">
                  <QueueCard
                    queueId={item.queueId}
                    patientId={item.patientId}
                    patient={item.patient}
                    queuePosition={item.queuePosition}
                    status={item.status}
                    patientStatus={item.patientStatus}
                    labTestStatus={item.labTestStatus}
                    time={item.time}
                    isSelected={item.queueId === selectedPatientId}
                    isPatientFromQueue={
                      isPatientFromQueue && item.queueId === selectedPatientId
                    }
                    onClickCancel={onCancelAppointment}
                    onClickMove={onStartConsultation}
                    onClickMarkAsConsulted={onMarkAsConsulted}
                    onSelect={(e) => {
                      e.stopPropagation();
                      onSelectPatient(item);
                    }}
                  />
                </div>
              ))}

              {!isLoading && !queue.length && (
                <div className="text-[#637D92] text-center py-4">
                  No patients for today!
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Fixed Pagination at Bottom */}
      {totalItems > 0 && (
        <div className="absolute bottom-0 left-0 right-0 px-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
          />
        </div>
      )}
    </div>
  );
};

export default QueueList;
