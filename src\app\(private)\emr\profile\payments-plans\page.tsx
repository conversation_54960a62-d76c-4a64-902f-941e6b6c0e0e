'use client';

import { Suspense } from 'react';

import useIsMobile from '@/hooks/use-mobile-layout';

import PaymentsAndPlans from '@/views/emr/doctor-profile/payments-plans';

export default function PaymentsPlansPage() {
  const isMobile = useIsMobile();

  return (
    <Suspense>
      <div className="col-span-7 w-full bg-white rounded-base shadow-base max-h-full">
        <div className="w-full grid grid-rows-14 bg-white px-5 pt-1.5 rounded-lg min-h-full overflow-hidden">
          <div
            className={`row-span-12 relative overflow-y-hidden overflow-x-hidden ${
              isMobile ? 'h-[calc(100vh-14rem)]' : 'h-[calc(100vh-5rem)]'
            }`}
          >
            <PaymentsAndPlans />
          </div>
        </div>
      </div>
    </Suspense>
  );
}
