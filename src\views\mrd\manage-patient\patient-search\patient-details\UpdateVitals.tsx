import React, { memo, useCallback, useEffect } from 'react';

import { useForm } from 'react-hook-form';

import { LuUpload } from 'react-icons/lu';

import { useMrdPatientSearch } from '@/store/mrd/manage-patient/patient-search';
import { useUpdateVitalStore } from '@/store/mrd/manage-patient/patient-search/update-vital';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { calculateBMI } from '@/utils/mrd/manage-patient/calculate-bmi';

import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import AppButton from '@/core/components/app-button';
import AppModal from '@/core/components/app-modal';
import { Vitals } from '@/types/mrd/manage-patient/vitals';

import KeyValuePair from '../../shared/KeyValuePair';

const UpdateVitals = () => {
  const { toggleModal, open, vital, updating, updateVitals } =
    useUpdateVitalStore();
  const patient = useMrdPatientSearch((state) => state.patient);

  const { control, reset, handleSubmit, watch, setValue } = useForm<Vitals>();

  const height = watch('height');
  const weight = watch('weight');

  const resetData = useCallback(() => {
    if (open && vital) {
      const { createdAt, ...rest } = vital;
      reset({ ...rest });
    }
  }, [reset, vital, open]);

  const onSubmit = useCallback(
    ({ vitalStatuses: _vs, ageGroup: _a, ...data }: Vitals) => {
      updateVitals(data);
    },
    [updateVitals]
  );

  useEffect(() => {
    resetData();
  }, [resetData]);

  useEffect(() => {
    if (height && weight) {
      const bmi = calculateBMI(height, weight);
      setValue('bmi', String(bmi));
    }
  }, [height, weight, setValue]);

  return (
    <div className="flex h-full p-1">
      <AppButton
        variant="outlined"
        startIcon={<LuUpload />}
        size="small"
        classes={{
          root: 'flex-col !items-center !justify-center !h-full !bg-green-200',
          startIcon: '!mr-0',
        }}
        onClick={toggleModal}
      >
        Update Vitals
      </AppButton>
      <AppModal title="Update Vitals" open={open} onClose={toggleModal}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="p-base flex flex-col gap-base"
        >
          <div className="flex justify-between items-center pb-base">
            <KeyValuePair label="Patient Name" value={patient?.name} />
            <KeyValuePair
              label="Patient ID"
              value={patient?.id}
              align="right"
            />
          </div>
          <div className="flex gap-base w-full">
            <ControlledTextField
              label="Height (cm)"
              name="height"
              control={control}
              initiallyReadonly
              fullWidth
            />
            <ControlledTextField
              label="Weight (kg)"
              name="weight"
              control={control}
              initiallyReadonly
              fullWidth
            />
          </div>
          <div className="flex gap-base w-full">
            <ControlledTextField
              label="BMI (kg/m2)"
              name="bmi"
              control={control}
              isReadOnly
              fullWidth
            />
            <ControlledTextField
              label="Pulse (per min)"
              name="pulse"
              control={control}
              initiallyReadonly
              fullWidth
            />
          </div>
          <div className="flex gap-base w-full">
            <div className="flex w-1/2">
              <ControlledTextField
                label="SBP(mm/Hg)"
                name="sbp"
                control={control}
                initiallyReadonly
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderEndEndRadius: '0px',
                    borderStartEndRadius: '0px',
                  },
                }}
              />
              <ControlledTextField
                label="DBP(mm/Hg)"
                name="dbp"
                control={control}
                initiallyReadonly
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderEndStartRadius: '0px',
                    borderStartStartRadius: '0px',
                    borderLeftStyle: 'none',
                  },
                }}
              />
            </div>
            <div className="w-1/2">
              <ControlledTextField
                label="RR (per min)"
                name="rr"
                control={control}
                initiallyReadonly
                fullWidth
              />
            </div>
          </div>
          <div className="flex gap-base">
            <ControlledTextField
              label="SpO2 (%)"
              name="spO2"
              control={control}
              initiallyReadonly
              fullWidth
            />
            <ControlledTextField
              label="Temperature (°F)"
              name="temperature"
              control={control}
              initiallyReadonly
              fullWidth
            />
          </div>
          <div className="flex justify-between mt-4">
            <div className="flex flex-col">
              {vital?.createdAt && (
                <>
                  <span className="text-sm text-gray-500">Date</span>
                  <span className="text-sm text-gray-500">
                    {formatDate(
                      vital?.createdAt,
                      DateFormats.DATE_DD_MM_YYYY_SLASH
                    )}
                  </span>
                </>
              )}
            </div>
            <AppButton sx={{ minWidth: 140 }} loading={updating} type="submit">
              Save
            </AppButton>
          </div>
        </form>
      </AppModal>
    </div>
  );
};

export default memo(UpdateVitals);
