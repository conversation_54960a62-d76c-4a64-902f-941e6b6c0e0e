import { ActionMeta, Props as ReactSelectProps } from 'react-select';

import { FormControlProps, InputLabelProps } from '@mui/material';

export type CommonSelectProps = {
  label?: string;
  inputLabelProps?: InputLabelProps;
  error?: boolean;
  helperText?: string;
  formControlProps?: FormControlProps;
  required?: boolean;
  color?: FormControlProps['color'];
  initiallyReadonly?: boolean;
  onKeyDown?: (e: React.KeyboardEvent) => void;
};

export type ReactSelectOmitted<T, F extends boolean = boolean> = Omit<
  ReactSelectProps<T, F>,
  'onChange' | 'isMulti'
>;

export type SingleSelectProps<T> = ReactSelectOmitted<T> &
  CommonSelectProps & {
    isMulti?: false;
    onChange?: (newValue: T, actionMeta?: ActionMeta<T>) => void;
  };

export type MultiSelectProps<T> = ReactSelectOmitted<T> &
  CommonSelectProps & {
    isMulti?: true;
    onChange?: (newValue: T[], actionMeta?: ActionMeta<T>) => void;
  };

export type AppSelectFieldProps<T> = MultiSelectProps<T> | SingleSelectProps<T>;
