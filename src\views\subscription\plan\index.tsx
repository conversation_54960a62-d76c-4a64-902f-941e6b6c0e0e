'use client';

import { useState, useEffect } from 'react';

import { useRouter } from 'next/navigation';

import { useSubscriptionStore } from '@/store/subscription';

import {
  getSubscriptionUserData,
  OrganizationPlan,
} from '@/utils/subscription';

import { routes } from '@/constants/routes';

import BackArrow from '../components/BackArrow';
import MonthlyYearlyToggle from '../components/MonthlyYearlyToggle';
import PricingListHeader from '../components/PricingListHeader';
import SubscriptionLoading from '../components/SubscriptionLoading';
import { Plan } from '../PlanCard';
import PlanGrid from '../PlanGrid';

const PlanSelectionView = () => {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);

  const {
    subscriptionPlans,
    organizationPlan,
    isLoadingPlans,
    isLoadingOrganizationPlan,
    fetchSubscriptionPlans,
    fetchOrganizationPlan,
    selectedPlan,
    setSelectedPlan,
    billingCycle,
    setBillingCycle,
    subscribe,
    isSubscribing,
  } = useSubscriptionStore();

  // Convert API plans to Plan format
  const convertApiPlansToPlans = (apiPlans: OrganizationPlan[]): Plan[] => {
    return apiPlans
      .filter((plan) => {
        // Filter plans based on billing cycle
        if (billingCycle === 'monthly') {
          return plan.validity === 'Monthly' || plan.validity === 'Both';
        } else {
          return plan.validity === 'Yearly' || plan.validity === 'Both';
        }
      })
      .map((plan) => {
        const hasAddOnFeatures = Object.values(plan.addOnFeatures).some(
          (features) => features.length > 0
        );

        const price =
          billingCycle === 'monthly'
            ? (plan.totalMonthlyBasicAmount ?? plan.monthlyTotal)
            : (plan.totalYearlyBasicAmount ?? plan.yearlyTotal);

        return {
          id: plan.id,
          name: plan.planName,
          subtitle:
            billingCycle === 'monthly' ? 'Billed Monthly' : 'Billed Annually',
          monthlyPrice: plan.totalMonthlyBasicAmount ?? plan.monthlyTotal,
          yearlyPrice: plan.totalYearlyBasicAmount ?? plan.yearlyTotal,
          // Keep the original features structure for hierarchical display
          features: [], // Will be populated by the new hierarchical structure
          buttonText: 'Buy Now',
          buttonColor: price === 0 ? '#0496E1' : '#0EA5E9',
          popular: false,
          hasAddOnFeatures,
          isOrganizationPlan: false,
          apiPlan: plan,
        };
      });
  };

  const convertOrganizationPlanToPlan = (orgPlan: OrganizationPlan): Plan => {
    const hasAddOnFeatures = Object.values(orgPlan.addOnFeatures).some(
      (features) => features.length > 0
    );

    return {
      id: orgPlan.id,
      name: orgPlan.planName,
      subtitle:
        billingCycle === 'monthly' ? 'Billed Monthly' : 'Billed Annually',
      monthlyPrice: 0, // Hide price for organization plan
      yearlyPrice: 0, // Hide price for organization plan
      features: [], // Empty features for organization plans (will show description instead)
      buttonText: 'Get Quote', // Always show Get Quote for organization plan
      buttonColor: '#0496E1',

      hasAddOnFeatures,
      isOrganizationPlan: true,
      apiPlan: orgPlan,
    };
  };

  // Convert regular subscription plans
  const regularPlans = subscriptionPlans
    ? convertApiPlansToPlans(subscriptionPlans.plans)
    : [];
  console.log('Regular plans:', regularPlans);
  // Sort regular plans by price (ascending order)
  const sortedRegularPlans = regularPlans.sort((a, b) => {
    const priceA = a.monthlyPrice > 0 ? a.monthlyPrice : a.yearlyPrice;
    const priceB = b.monthlyPrice > 0 ? b.monthlyPrice : b.yearlyPrice;
    return priceA - priceB;
  });

  // Convert organization plan if available
  const organizationPlanCard = organizationPlan
    ? convertOrganizationPlanToPlan(organizationPlan)
    : null;

  // Combine plans - regular plans first (sorted), then organization plan at the end
  const allPlans = organizationPlanCard
    ? [...sortedRegularPlans, organizationPlanCard]
    : sortedRegularPlans;

  useEffect(() => {
    // Fetch subscription plans
    fetchSubscriptionPlans(true);

    // Fetch organization plan
    fetchOrganizationPlan();

    // Get user data
    const userData = getSubscriptionUserData();
    if (!userData) {
      // Create default user data for testing
      const defaultUserData = {
        signupCompleted: true,
        establishment: 'hospital',
        role: 'admin',
      };
      setUserData(defaultUserData);
    } else {
      setUserData(userData);
    }
  }, [router, fetchSubscriptionPlans, fetchOrganizationPlan]);

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleSubscribe = async (planId: string) => {
    // Find the plan to check if it's free (cost 0, not organization plan)
    const plan = allPlans.find((p) => p.id === planId);

    // According to requirements:
    // - Free plans (cost 0, not organization) should use startTrial API
    // - Paid plans should use subscribeAfterPayment API
    // - The subscribeToPlan API should be completely removed

    if (
      plan &&
      !plan.isOrganizationPlan &&
      (plan.monthlyPrice === 0 || plan.yearlyPrice === 0)
    ) {
      // Free plans are handled by PlanCard component via SubscriptionDetailsModal
      // Don't call subscription API here to avoid duplicate calls
      return;
    }

    // For paid plans, the logic is handled in PlanCard component
    // No direct subscription call needed here as it's handled via modal flow
    return;
  };

  const handleGetQuote = () => {
    router.push(routes.SUBSCRIPTION_CUSTOM_PRICING);
  };

  const isLoading = isLoadingPlans || isLoadingOrganizationPlan;

  if (!userData || isLoading) {
    return <SubscriptionLoading />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 py-4 px-4 pb-16 relative">
      <div className="mb-6 absolute left-0 top-0 p-4">
        <BackArrow />
      </div>

      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-6">
          <PricingListHeader />

          {/* Monthly/Yearly Toggle */}
          <MonthlyYearlyToggle
            billingCycle={billingCycle}
            onBillingCycleChange={setBillingCycle}
            className="mb-8"
          />
        </div>

        <PlanGrid
          plans={allPlans}
          billingCycle={billingCycle}
          selectedPlan={selectedPlan}
          onPlanSelect={handlePlanSelect}
          onGetQuote={handleGetQuote}
          onSubscribe={handleSubscribe}
          isLoading={false}
        />
      </div>
    </div>
  );
};

export default PlanSelectionView;
