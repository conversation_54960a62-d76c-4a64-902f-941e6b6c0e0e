import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createLifestyleData,
  getLifestyleQuestions,
  getPatientLifestyle,
  updateLifestyleData,
} from '@/query/emr/lifestyle';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import {
  FieldGroup,
  Questionnaire,
  QuestionnaireResponse,
} from '@/types/emr/lifestyle/questionnaire';

import { useLifestyleFilterStore } from '../../filter-store';

type ExercisePatternState = {
  questions: Questionnaire;
  questionLoading: boolean;
  updating: boolean;
  patientData: QuestionnaireResponse[];
  loading: boolean;
  finalizing: boolean;
};

type ExercisePatternActions = {
  getLifestyleQuestions: () => Promise<void>;
  createLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  updateLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  finalizeRecord: (id: string) => Promise<void>;
  getPatientData: (
    fromDate?: string,
    toDate?: string,
    silent?: boolean
  ) => Promise<void>;
  refreshData: () => void;
};

export type ExercisePatternStore = ExercisePatternState &
  ExercisePatternActions;

const defaultQuestions = {
  source: LifestyleSources.PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS,
  questions: [],
};

const initialState: ExercisePatternState = {
  questions: defaultQuestions,
  questionLoading: false,
  updating: false,
  patientData: [],
  loading: false,
  finalizing: false,
};

export const exercisePatternStore = create<ExercisePatternStore>(
  (set, get) => ({
    ...initialState,
    getLifestyleQuestions: async () => {
      try {
        set({ questionLoading: true });

        const data = await getLifestyleQuestions(
          LifestyleSources.PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS
        );

        set({ questions: data });
      } catch (error) {
        console.error('Error fetching exercise pattern questions:', error);
        toast.error(
          'Failed to load exercise pattern questionnaire. Please try again.'
        );

        set({ questions: defaultQuestions });
      } finally {
        set({ questionLoading: false });
      }
    },
    createLifestyleData: async (data: Record<string, unknown>) => {
      try {
        set({ updating: true });
        await createLifestyleData({ ...data, source: defaultQuestions.source });
        toast.success('Exercise pattern created successfully');

        get().refreshData();
      } catch (error) {
        console.error(error);
        toast.error('Failed to create exercise pattern');
      } finally {
        set({ updating: false });
      }
    },
    updateLifestyleData: async (data: Record<string, unknown>) => {
      try {
        set({ updating: true });

        const {
          questions,
          created_on,
          updated_on,
          create_by,
          update_by,
          _attachments,
          _etag,
          _rid,
          _self,
          _ts,
          ...formData
        } = data;

        const updatePayload: Partial<QuestionnaireResponse> = {
          ...formData,
          source: defaultQuestions.source,
          questions: (questions || data.questions) as FieldGroup[],
        };

        await updateLifestyleData(updatePayload, data?.id as string);
        toast.success('Exercise pattern updated successfully');
      } catch (error) {
        console.error(' PATCH request failed:', error);
        toast.error('Failed to update exercise pattern');
      } finally {
        set({ updating: false });
        get().refreshData();
      }
    },
    finalizeRecord: async (id: string) => {
      try {
        set({ finalizing: true });
        await updateLifestyleData(
          {
            id,
            status: LifestyleRecordStatus.FINALIZED,
          },
          id
        );
        toast.success('Exercise pattern record finalized successfully');
        await get().refreshData();
      } catch (error) {
        console.error(error);
        toast.error('Failed to finalize exercise pattern record');
      } finally {
        set({ finalizing: false });
      }
    },
    getPatientData: async (fromDate, toDate, silent = false) => {
      try {
        set({ loading: !silent });
        const data = await getPatientLifestyle(
          LifestyleSources.PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS,
          fromDate,
          toDate
        );
        set({ patientData: data || [] });
      } catch (error) {
        console.error('Error fetching patient data:', error);
        set({ patientData: [] });
      } finally {
        set({ loading: false });
      }
    },

    refreshData: () => {
      const { getPatientData } = get();
      const { fromDate, toDate } = useLifestyleFilterStore.getState();
      getPatientData(fromDate, toDate, true);
    },
  })
);
