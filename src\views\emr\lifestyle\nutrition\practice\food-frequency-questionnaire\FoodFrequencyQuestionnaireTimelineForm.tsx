import { FC, memo, useEffect, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { Questionnaire } from '@/types/emr/lifestyle/questionnaire';

import FoodFrequencyQuestionnaireForm from './FoodFrequencyQuestionnaireForm';

type Props = {
  data: Questionnaire;
  isExpandView?: boolean;
  isAmbientForm?: boolean;
};

const FoodFrequencyQuestionnaireTimelineForm: FC<Props> = ({
  data,
  isExpandView,
  isAmbientForm,
}) => {
  const methods = useForm<Questionnaire>({
    defaultValues: data,
    mode: 'onChange',
  });

  const formFields = useMemo(() => {
    if (!data?.questions?.length) return [];
    return data.questions;
  }, [data]);

  // Detect if this is from ambient listening
  const isFromAmbientListening = useMemo(() => {
    return !!(data as any)?.conversation || !!(data as any)?.recordingDuration;
  }, [data]);

  useEffect(() => {
    if (data) {
      methods.reset(data);
    }
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <FoodFrequencyQuestionnaireForm
        formFields={formFields}
        readonly
        isExpandView={isExpandView}
        isAmbientForm={isAmbientForm ?? isFromAmbientListening}
      />
    </FormProvider>
  );
};

export default memo(FoodFrequencyQuestionnaireTimelineForm);
