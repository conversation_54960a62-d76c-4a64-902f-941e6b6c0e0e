import React from 'react';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDuration, formatDate } from '@/utils/dateUtils/dayUtils';

import { Conversation } from '@/types/emr/lifestyle/questionnaire';

export interface LifestyleTranscriptViewProps {
  conversation?: Conversation[];
  doctorName?: string;
  patientName?: string;
  date?: string;
  duration?: number;
}

export default function LifestyleTranscriptView({
  conversation,
  doctorName = 'Doctor',
  patientName = 'Patient',
  date,
  duration,
}: LifestyleTranscriptViewProps) {
  // Format the date properly, use current date if no date provided
  const displayDate = date
    ? formatDate(date, DateFormats.DATE_DD_MM_YYYY_SLASH)
    : formatDate(new Date(), DateFormats.DATE_DD_MM_YYYY_SLASH);

  if (!conversation || !conversation?.length) {
    return (
      <div className="w-full p-6 text-center text-gray-500">
        No transcript available for this record.
      </div>
    );
  }

  return (
    <div className="w-full space-y-6 bg-white p-2">
      {/* Consultation Details Section */}
      <div className="space-y-4 pb-6 border-b border-gray-200">
        <h2
          className="text-base font-medium"
          style={{ color: '#012436', fontSize: '16px', fontWeight: 500 }}
        >
          Consultation Details:
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex">
              <span
                className="font-medium min-w-[80px]"
                style={{ color: '#012436', fontSize: '14px', fontWeight: 500 }}
              >
                Doctor:
              </span>
              <span
                className="text-gray-700"
                style={{ fontSize: '14px', fontWeight: 400 }}
              >
                {doctorName}
              </span>
            </div>
            <div className="flex">
              <span
                className="font-medium min-w-[80px]"
                style={{ color: '#012436', fontSize: '14px', fontWeight: 500 }}
              >
                Patient:
              </span>
              <span
                className="text-gray-700"
                style={{ fontSize: '14px', fontWeight: 400 }}
              >
                {patientName}
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex">
              <span
                className="font-medium min-w-[80px]"
                style={{ color: '#012436', fontSize: '14px', fontWeight: 500 }}
              >
                Date:
              </span>
              <span
                className="text-gray-700"
                style={{ fontSize: '14px', fontWeight: 400 }}
              >
                {displayDate}
              </span>
            </div>
            {duration && (
              <div className="flex">
                <span
                  className="font-medium min-w-[80px]"
                  style={{
                    color: '#012436',
                    fontSize: '14px',
                    fontWeight: 500,
                  }}
                >
                  Duration:
                </span>
                <span
                  className="text-gray-700"
                  style={{ fontSize: '14px', fontWeight: 400 }}
                >
                  {formatDuration(duration)}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI Transcript Section */}
      <div className="space-y-4">
        <h3
          className="text-base font-medium"
          style={{ color: '#012436', fontSize: '16px', fontWeight: 500 }}
        >
          AI Transcript:
        </h3>

        {/* Conversation */}
        <div className="space-y-4">
          {conversation?.map((c, index) => (
            <div
              key={`${c.message?.slice(0, 10)}-${index}`}
              className="space-y-1"
            >
              <h4
                className="text-base font-medium"
                style={{
                  fontSize: '16px',
                  fontWeight: 500,
                  color: c.speaker === 'doctor' ? '#000000' : '#02537D',
                }}
              >
                {c.speaker === 'doctor' ? doctorName : patientName}:
              </h4>
              <p
                className="text-black leading-relaxed"
                style={{ fontSize: '12px' }}
              >
                {c.message}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
