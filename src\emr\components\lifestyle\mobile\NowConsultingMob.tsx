import React, { memo, useEffect, useMemo, useRef, useState } from 'react';

import { IoIosAdd } from 'react-icons/io';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { nutritionAttitudeStore } from '@/store/emr/lifestyle/nutrition/attitude/attitude-store';
import { nutritionKnowledgeStore } from '@/store/emr/lifestyle/nutrition/knowledge/knowledge-store';
import { dietaryRecallStore } from '@/store/emr/lifestyle/nutrition/practice/dietary-recall-store';
import { foodFrequencyQuestionnaireStore } from '@/store/emr/lifestyle/nutrition/practice/food-frequency-questionnaire-store';
import { foodIntakePatternStore } from '@/store/emr/lifestyle/nutrition/practice/food-intake-pattern-store';
import { attitudeStore } from '@/store/emr/lifestyle/physical-activity/attitude/attitude-store';
import { knowledgeStore } from '@/store/emr/lifestyle/physical-activity/knowledge/knowledge-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import { checkTodayEntryExists } from '@/utils/emr/lifestyle/daily-entry-validation';

import FluentFoodApple20Regular from '@/assets/svg/FluentFoodApple20Regular';

import {
  lifestyleModes,
  mobileViews,
  NutritionAndDietForm,
} from '@/constants/lifestyle';

import PatientCard from '../../patient-card';

import LifestyleNote from '../LifestyleNote';
import LifestyleTabs from '../LifestyleTabs';
import TabPanels from '../TabPanels';

import DetailsTimelineButtons from './shared/DetailsTimelineButtons';
import MobilePageTitle from './shared/MobilePageTitle';

const NowConsultingMob = () => {
  const { setCurrentTab, currentTab, setMobilePage, setFormMode, closeModal } =
    useLifestyleUtilStore();
  const { patient } = useCurrentPatientStore();
  const buttonContainerRef = useRef<HTMLDivElement>(null);

  const { source } = lifestyleStore();
  const { patientData: exerciseData } = exercisePatternStore();
  const { patientData: physicalAttitudeData } = attitudeStore();
  const { patientData: nutritionAttitudeData } = nutritionAttitudeStore();
  const { patientData: knowledgeData } = knowledgeStore();
  const { patientData: nutritionKnowledgeData } = nutritionKnowledgeStore();
  const { patientData: foodIntakeData } = foodIntakePatternStore();
  const { patientData: dietaryRecallData } = dietaryRecallStore();
  const { patientData: foodFrequencyData } = foodFrequencyQuestionnaireStore();

  const isTodayEntryExists = checkTodayEntryExists(source, {
    exerciseData,
    physicalAttitudeData,
    nutritionAttitudeData,
    knowledgeData,
    nutritionKnowledgeData,
    foodIntakeData,
    dietaryRecallData,
    foodFrequencyData,
  });

  const isButtonDisabled = !source || isTodayEntryExists;

  const [buttonContainerHeight, setButtonContainerHeight] = useState(0);

  const tabList = useMemo(
    () => [
      {
        label: 'Nutrition & Diet',
        icon: <FluentFoodApple20Regular color="inherit" />,
        content: (
          <TabPanels
            items={NutritionAndDietForm}
            activeForm={currentTab}
            onSelectTab={setCurrentTab}
          />
        ),
      },
      // TODO: Enable these when ready
      // {
      //   label: 'Physical Activity',
      //   icon: <RunningIcon />,
      //   content: <>Physical Activity</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Mental Health',
      //   icon: <BrainIcon />,
      //   content: <>Mental Health</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Sleep',
      //   icon: <SleepIcon />,
      //   content: <>Sleep</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Meditation',
      //   icon: <MeditationIcon />,
      //   content: <>Meditation</>,
      //   disabled: true,
      // },
    ],
    [currentTab, setCurrentTab]
  );

  useEffect(() => {
    if (buttonContainerRef.current) {
      setButtonContainerHeight(buttonContainerRef.current.clientHeight + 10);
    }
  }, []);

  return (
    <div className="flex flex-col h-full px-2">
      <MobilePageTitle title="Now Consulting" />
      <DetailsTimelineButtons />
      <div className="pb-4">
        <PatientCard
          name={patient?.name ?? '--'}
          dob={patient?.dob}
          sex={patient?.sex ?? '--'}
          address={patient?.address ?? '--'}
        />
      </div>
      <div className="flex flex-col h-[calc(100vh-22rem)] overflow-x-auto">
        <LifestyleTabs items={tabList} />
        <div
          className="flex-1"
          style={{ paddingBottom: `${buttonContainerHeight}px` }}
        >
          <div className="w-full pt-4">
            <LifestyleNote />
          </div>
        </div>
      </div>
      <div
        className="fixed bottom-18 left-0 right-0 py-2 px-3 shadow-lg z-10"
        ref={buttonContainerRef}
      >
        <div className="flex gap-2 items-stretch justify-between">
          <button
            disabled={isButtonDisabled}
            onClick={() => {
              setMobilePage(mobileViews.ADD_RECORD_MANUALLY);
              setFormMode?.(lifestyleModes.CREATE);
              closeModal();
            }}
            className="flex flex-1 gap-1 p-2 items-center text-[15px] justify-center font-semibold bg-primary text-white rounded-lg h-full focus:shadow-custom-xs disabled:bg-gray-300 disabled:opacity-90 disabled:cursor-not-allowed"
          >
            Add Record Manually <IoIosAdd className="text-lg" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default memo(NowConsultingMob);
