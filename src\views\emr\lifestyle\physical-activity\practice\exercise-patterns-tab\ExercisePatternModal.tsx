import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import { detectAmbientInObject } from '@/utils/ambient-detection';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import LifestyleModalWrapper from '@/views/emr/lifestyle/shared/LifestyleModalWrapper';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternForm from './ExercisePatternForm';

const hasFormData = (data: QuestionnaireResponse): boolean => {
  if (!data?.questions) return false;

  return data.questions.some((question) =>
    question.fields?.some((field) => {
      if (field.type === 'table' && field.value) {
        return (
          Array.isArray(field.value) &&
          field.value.length > 0 &&
          field.value.some((row) =>
            Object.values(row || {}).some(
              (value) => value !== null && value !== undefined && value !== ''
            )
          )
        );
      }

      return (
        field.value !== null && field.value !== undefined && field.value !== ''
      );
    })
  );
};

const getValidationMessage = (data: QuestionnaireResponse): string => {
  if (!data?.questions) return 'Please enter at least one record to save';

  const tableFields = data.questions.flatMap(
    (question) =>
      question.fields?.filter((field) => field.type === 'table') || []
  );

  if (tableFields.length === 0)
    return 'Please enter at least one record to save';

  const hasRows = tableFields.some(
    (field) => Array.isArray(field.value) && field.value.length > 0
  );

  if (!hasRows) {
    return 'Please enter at least one record to save';
  }

  return 'Please enter at least one record to save';
};

// Function to check if there are any completely empty rows
const hasEmptyRows = (data: QuestionnaireResponse): boolean => {
  if (!data?.questions) return false;

  return data.questions.some((question) =>
    question.fields?.some((field) => {
      if (field.type === 'table' && Array.isArray(field.value)) {
        return field.value.some((row) => {
          if (!row) return true; // null or undefined row is considered empty

          // Check if all values in the row are empty
          const values = Object.values(row);
          return (
            values.length > 0 &&
            values.every((value) => {
              if (Array.isArray(value)) {
                return value.length === 0;
              }
              return value === null || value === undefined || value === '';
            })
          );
        });
      }
      return false;
    })
  );
};

const ExercisePatternModal: React.FC<{
  patientData?: QuestionnaireResponse | null;
  mode?: LifestyleMode;
  onAfterSubmit?: () => void;
  hideSaveButton?: boolean;
  onSaveRef?: MutableRefObject<(() => void) | null>;
  initialValues?: any;
}> = ({
  patientData,
  mode = LifestyleMode.CREATE,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
  initialValues,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
    patientData: storePatientData,
  } = exercisePatternStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { patient } = useCurrentPatientStore();

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? initialValues ?? questions,
    mode: 'onChange',
  });

  const { watch, handleSubmit } = methods;

  const watchedData = watch();
  const isFormValid = useMemo(() => {
    if (currentMode === LifestyleMode.VIEW) {
      return true;
    }

    return hasFormData(watchedData);
  }, [watchedData, currentMode]);

  const formFields = useMemo(() => {
    if (!questions?.questions?.length) return [];

    return questions.questions;
  }, [questions]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      console.log('=== onSubmit called ===');
      console.log('Raw form data received:', data);

      // Check if this is ambient listening data
      const isAmbientData = detectAmbientInObject(initialValues || data);
      console.log('Is ambient data:', isAmbientData);
      console.log('Initial values:', initialValues);

      // Ensure data has questions structure - if not, use questions template
      const formData = data?.questions
        ? data
        : {
            ...data,
            questions: questions?.questions || [],
          };
      console.log('Form data after ensuring questions structure:', formData);

      // Check for empty rows first (skip for ambient data as it might be processed differently)
      if (
        !isAmbientData &&
        (currentMode === LifestyleMode.CREATE ||
          currentMode === LifestyleMode.EDIT) &&
        hasEmptyRows(formData)
      ) {
        toast.error("Empty row can't be saved", {
          style: {
            background: '#fef2f2',
            border: '1px solid #fecaca',
            color: '#dc2626',
            padding: '12px 16px',
            borderRadius: '6px',
          },
          closeButton: true,
          duration: 4000,
        });
        return;
      }

      // For ambient data, we'll validate after cleaning the data
      // For manual entry, validate before processing
      if (
        !isAmbientData &&
        (currentMode === LifestyleMode.CREATE ||
          currentMode === LifestyleMode.EDIT) &&
        !hasFormData(formData)
      ) {
        const validationMessage = getValidationMessage(formData);
        toast.error(validationMessage, {
          style: {
            background: '#fef2f2',
            border: '1px solid #fecaca',
            color: '#dc2626',
            padding: '12px 40px 12px 16px',
            borderRadius: '6px',
            position: 'relative',
          },
          closeButton: true,
          duration: 5000,
          className: 'custom-error-toast',
        });
        return;
      }

      try {
        const cleanedData = {
          ...formData,
          questions: formData.questions?.map((question) => ({
            ...question,
            fields: question.fields?.map((field) => {
              if (field.type === 'table' && Array.isArray(field.value)) {
                const filteredRows = field.value.filter((row) => {
                  if (!row) return false;

                  return Object.values(row).some((value) => {
                    if (Array.isArray(value)) {
                      return value.length > 0;
                    }
                    return (
                      value !== null && value !== undefined && value !== ''
                    );
                  });
                });
                return {
                  ...field,
                  value: filteredRows,
                };
              }
              return field;
            }),
          })),
        };

        console.log('Cleaned data:', cleanedData);

        // Validate data after cleaning - this applies to both ambient and manual data
        const hasValidData = cleanedData.questions?.some((question) =>
          question.fields?.some((field) => {
            if (field.type === 'table' && Array.isArray(field.value)) {
              console.log(
                'Table field found:',
                field.id,
                'with',
                field.value.length,
                'rows'
              );
              return field.value.length > 0;
            }
            return (
              field.value !== null &&
              field.value !== undefined &&
              field.value !== ''
            );
          })
        );

        console.log('Has valid data:', hasValidData);

        if (!hasValidData) {
          // For ambient data, provide a more helpful error message
          const errorMessage = isAmbientData
            ? 'No exercise pattern data found in the recording. Please add data manually or try recording again.'
            : 'Please enter at least one complete row before saving';
          toast.error(errorMessage, {
            style: {
              background: '#fef2f2',
              border: '1px solid #fecaca',
              color: '#dc2626',
              padding: '12px 16px',
              borderRadius: '6px',
            },
            duration: 4000,
          });
          return;
        }

        if (cleanedData?.id) {
          const updateData = {
            ...cleanedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
          };
          console.log('Updating exercise pattern:', updateData);
          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...cleanedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
            // Include ambient listening data if available from initialValues
            ...(initialValues?.conversation && {
              conversation: initialValues.conversation,
            }),
            ...(initialValues?.recordingDuration && {
              recordingDuration: initialValues.recordingDuration,
            }),
          };
          console.log('Creating exercise pattern:', createData);
          await createLifestyleData(createData);
        }
        console.log('Exercise pattern saved successfully, closing modal');
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error('Error submitting exercise pattern:', error);
        toast.error('Failed to save exercise pattern. Please try again.');
      }
    },
    [
      setModalOpen,
      updateLifestyleData,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.id,
      createLifestyleData,
      onAfterSubmit,
      currentMode,
      initialValues,
      questions, // Add questions to dependencies since we use it in formData fallback
    ]
  );

  const handleSaveClick = useCallback(() => {
    // Ensure form is ready before submitting
    if (!questions?.questions?.length) {
      console.warn('Questions not loaded yet, cannot save');
      toast.error('Form is not ready. Please wait a moment and try again.');
      return;
    }
    // Use handleSubmit which will validate and then call onSubmit
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit, questions]);

  // Set up the save ref when the component mounts/updates
  // Use a more stable approach that ensures the ref is always set and works correctly
  useEffect(() => {
    if (onSaveRef) {
      // Create a wrapper function that ensures the form is ready and properly submits
      onSaveRef.current = () => {
        try {
          console.log('Save ref called for exercise pattern');
          // Double-check that form is ready
          if (!questions?.questions?.length) {
            console.warn('Save ref called but questions not loaded');
            toast.error(
              'Form is not ready. Please wait a moment and try again.'
            );
            return;
          }

          // Get current form values for debugging
          const currentValues = methods.getValues();
          console.log('Current form values before submit:', currentValues);

          // Check if this is ambient data - if so, we might need to bypass validation
          const isAmbientData = detectAmbientInObject(
            initialValues || currentValues
          );

          // Validate and submit using handleSubmit which will call onSubmit with validated data
          // handleSubmit returns a function that validates and then calls onSubmit
          // If validation fails, it won't call onSubmit - we need to handle that
          const submitFn = handleSubmit(
            (validData) => {
              console.log(
                'Form validation passed, calling onSubmit with:',
                validData
              );
              onSubmit(validData);
            },
            (errors) => {
              console.error('Form validation failed:', errors);
              console.error(
                'Detailed validation errors:',
                JSON.stringify(errors, null, 2)
              );

              // For ambient data, if validation fails due to structure issues,
              // try to submit anyway with the current form values
              if (isAmbientData) {
                console.log(
                  'Ambient data detected, attempting to submit despite validation errors'
                );
                // Get the current form values and submit directly
                const formValues = methods.getValues();
                console.log('Submitting with current form values:', formValues);
                onSubmit(formValues as QuestionnaireResponse);
              } else {
                toast.error('Please fix the form errors before saving.');
              }
            }
          );
          submitFn();
        } catch (error) {
          console.error('Error in save ref function:', error);
          toast.error('An error occurred while saving. Please try again.');
        }
      };
    }
    return () => {
      if (onSaveRef) {
        onSaveRef.current = null;
      }
    };
  }, [onSaveRef, handleSubmit, onSubmit, questions, methods, initialValues]);

  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  useEffect(() => {
    if (patientData) {
      methods.reset(patientData);
    } else if (initialValues) {
      console.log('Initializing form with initialValues:', initialValues);
      // When initialValues is provided (from ambient listening), ensure it has the proper structure
      // Merge with questions template if available to ensure proper form structure
      if (questions?.questions && initialValues?.questions) {
        console.log('Merging ambient data with questions template');
        // Merge ambient data with questions template
        const mergedData = {
          ...initialValues,
          questions: questions.questions.map(
            (questionGroup: any, index: number) => {
              const ambientGroup = initialValues.questions?.[index];
              if (!ambientGroup) {
                console.log(
                  `No ambient group at index ${index}, using template`
                );
                return questionGroup;
              }

              console.log(`Merging question group ${index}:`, {
                questionGroup,
                ambientGroup,
              });

              return {
                ...questionGroup,
                fields: questionGroup.fields?.map(
                  (field: any, fieldIndex: number) => {
                    const ambientField = ambientGroup.fields?.[fieldIndex];
                    if (!ambientField) {
                      console.log(
                        `No ambient field at index ${fieldIndex} for field ${field.id}`
                      );
                      return field;
                    }

                    // If it's a table field, merge the values
                    if (field.type === 'table' && ambientField.value) {
                      console.log(
                        `Table field ${field.id} has value:`,
                        ambientField.value
                      );
                      return {
                        ...field,
                        value: ambientField.value,
                      };
                    }

                    // For other field types, merge the value
                    return {
                      ...field,
                      value:
                        ambientField.value !== undefined
                          ? ambientField.value
                          : field.value,
                    };
                  }
                ),
              };
            }
          ),
        };
        console.log('Merged data:', mergedData);

        // Also create form data structure for React Hook Form
        const formData: any = {};
        mergedData.questions?.forEach(
          (questionGroup: any, groupIndex: number) => {
            if (!formData.questions) formData.questions = [];
            if (!formData.questions[groupIndex])
              formData.questions[groupIndex] = { fields: [] };

            questionGroup.fields?.forEach((field: any, fieldIndex: number) => {
              if (field.value !== undefined) {
                if (!formData.questions[groupIndex].fields[fieldIndex]) {
                  formData.questions[groupIndex].fields[fieldIndex] = {};
                }
                formData.questions[groupIndex].fields[fieldIndex].value =
                  field.value;
              }
            });
          }
        );

        console.log('Form data structure:', formData);
        methods.reset({ ...mergedData, ...formData });
      } else {
        // If questions template isn't loaded yet, just reset with initialValues
        // The form will be reset again when questions load
        console.log(
          'Questions template not ready, resetting with initialValues only'
        );
        methods.reset(initialValues);
      }
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, initialValues, questions, methods]);

  return (
    <FormProvider {...methods}>
      <LifestyleModalWrapper
        loading={questionLoading}
        onSubmit={handleSaveClick}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
        isFormValid={isFormValid}
        data={patientData || undefined}
        doctorName={profile?.general?.fullName}
        patientName={patient?.name}
      >
        <ExercisePatternForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          showHeading={true}
          mode={currentMode}
          patientData={storePatientData}
          variant="modal"
          isAmbientForm={detectAmbientInObject(initialValues || patientData)}
        />
      </LifestyleModalWrapper>
    </FormProvider>
  );
};

export default memo(ExercisePatternModal);
