import debounce from 'lodash/debounce';

type SearchFunction<T> = (term: string, signal?: AbortSignal) => Promise<T[]>;

export function createDebouncedSearch<T>(
  searchFn: SearchFunction<T>,
  wait = 500,
  options?: {
    minLength?: number;
    onStart?: () => void;
    onComplete?: () => void;
    onError?: (error: unknown) => void;
    maxWait?: number;
  }
) {
  const {
    minLength = 1,
    onStart,
    onComplete,
    onError,
    maxWait = 1000,
  } = options || {};

  let pending: {
    resolve: (value: T[] | PromiseLike<T[]>) => void;
    reject: (reason?: unknown) => void;
  } | null = null;
  let lastArgs: { term: string; signal?: AbortSignal } | null = null;

  const run = async () => {
    const args = lastArgs;
    if (!args) return;

    const { term, signal } = args;
    const trimmed = term.trim();
    const current = pending;
    // Clear references early to avoid resolving multiple times
    pending = null;
    lastArgs = null;

    if (!current) return;

    if (trimmed.length < minLength || signal?.aborted) {
      current.resolve([]);
      onComplete?.();
      return;
    }

    try {
      onStart?.();
      const results = await searchFn(trimmed, signal);
      current.resolve(results);
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === 'AbortError' || error.message === 'Request aborted')
      ) {
        current.resolve([]);
      } else {
        onError?.(error);
        console.error('Search error:', error);
        current.resolve([]);
      }
    } finally {
      onComplete?.();
    }
  };

  const debouncedRunner = debounce(run, wait, { maxWait, trailing: true });

  return {
    search: (term: string, signal?: AbortSignal): Promise<T[]> => {
      // Reject previous pending promise, if any, since a new search supersedes it
      if (pending) {
        pending.reject?.({
          name: 'AbortError',
          message: 'Superseded by new search',
        });
      }

      lastArgs = { term, signal };
      return new Promise<T[]>((resolve, reject) => {
        pending = { resolve, reject };
        debouncedRunner();
      });
    },
    cancel: () => {
      debouncedRunner.cancel();
      if (pending) {
        pending.reject?.({ name: 'AbortError', message: 'Request aborted' });
        pending = null;
      }
      lastArgs = null;
    },
    flush: () => {
      debouncedRunner.flush();
    },
  };
}
