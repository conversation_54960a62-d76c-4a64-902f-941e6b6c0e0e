import { formatICD, formatSnomedCT } from '@/utils/medical-codes';

import { api, arcaAxios } from '@/core/lib/interceptor';
import { SnomedApiResponse, ICDApiResponse } from '@/types/medical-codes';

export type SnomedParams = {
  term: string;
};

export type ICDParams = {
  q: string;
};

export const fetchSnomedCT = async (
  params: SnomedParams
): Promise<SnomedApiResponse> => {
  const { data } = await arcaAxios.get<SnomedApiResponse>(`/snomed-proxy`, {
    params,
  });
  return data;
};

export const fetchICDCode = async (
  params: ICDParams
): Promise<ICDApiResponse> => {
  // const { data } = await arcaAxios.get<ICDApiResponse>(`/icd-proxy`, {
  const { data } = await api.get<ICDApiResponse>(
    `/consultation/v0.1/icd-both-proxy`,
    {
      params,
    }
  );
  return data;
};

export const searchMedicalCodes = async (query: string) => {
  try {
    if (!query || query.trim().length < 3) {
      return [];
    }

    const trimmedQuery = query.trim();
    const startsWithSlash = trimmedQuery.startsWith('/');

    let snomedRes: SnomedApiResponse = {} as SnomedApiResponse;
    let icdRes: ICDApiResponse = {} as ICDApiResponse;

    if (!startsWithSlash) {
      try {
        snomedRes = await fetchSnomedCT({ term: trimmedQuery });
      } catch (error) {
        console.error('SNOMED API error:', error);
      }
    }

    try {
      icdRes = await fetchICDCode({ q: trimmedQuery });
    } catch (error) {
      console.error('ICD API error:', error);
    }

    const formattedSnomedRes = formatSnomedCT(snomedRes);
    const formattedIcdRes = formatICD(icdRes);

    if (startsWithSlash) {
      return formattedIcdRes;
    } else {
      return [...formattedSnomedRes, ...formattedIcdRes];
    }
  } catch (error) {
    console.error(error);
    return [];
  }
};
