import { VitalStatus } from '@/components/vitals-card/types';

import { Address } from './mrd/manage-patient/patient';

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the type/mrd/manage-patient/vitals instead.
 */
export type Vitals = {
  bloodPressure: string;
  bmi: string;
  diastolic: string;
  height: string;
  pulse?: string;
  rr?: string;
  systolic: string;
  weight: string;
  updatedOn?: string;
  heartRate?: string;
  respiratoryRate?: string;
  sbp?: string;
  dbp?: string;
  spO2?: string;
  temperature?: string;
  pp?: string;
};

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the type/mrd/manage-patient/patient instead.
 */
export type PatientRecord = {
  id: string;
  patientId: string;
  created_by: string;
  created_on: string;
  update_by: string;
  updated_on: string;
  vitals: Vitals;
  vitalStatuses?: {
    height?: VitalStatus;
    weight?: VitalStatus;
    bmi?: VitalStatus;
    pulse?: VitalStatus;
    rr?: VitalStatus;
    bp?: VitalStatus;
    temperature?: VitalStatus;
    spO2?: VitalStatus;
    pp?: VitalStatus;
  };
  _attachments: string;
  _etag: string;
  _rid: string;
  _self: string;
  _ts: number;
};

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the type/mrd/manage-patient/patient instead.
 */
export interface PatientI {
  cmcId?: string;
  name: string;
  sex: string;
  age: string;
  dob: string;
  height: string;
  weight: string;
  address: Address;
  aadhar: string;
  abha: string;
  contact: {
    phone: string;
    email: string;
  };
  maritalStatus?: string;
  insurance: {
    provider: string;
    id: string;
  };
  vitals?: PatientRecord[];
  last_consultation_date: string | null;
  id: string;
  created_on: string;
  updated_on: string;
  _rid: string;
  _self: string;
  _etag: string;
  _attachments: string;
  _ts: number;
}

export type BaseOption = {
  label: string;
  value: string;
};

export type Replace<T, R> = Omit<T, keyof R> & R;
