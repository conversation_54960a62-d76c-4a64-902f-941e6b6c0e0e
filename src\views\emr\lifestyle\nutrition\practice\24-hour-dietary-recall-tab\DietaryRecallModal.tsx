import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { dietaryRecallStore } from '@/store/emr/lifestyle/nutrition/practice/dietary-recall-store';

import { detectAmbientInObject } from '@/utils/ambient-detection';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import LifestyleModalWrapper from '@/views/emr/lifestyle/shared/LifestyleModalWrapper';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import DietaryRecallForm from './DietaryRecallForm';

const DietaryRecallModal: React.FC<{
  patientData?: QuestionnaireResponse | null;
  mode: LifestyleMode;
  onAfterSubmit?: () => void;
  hideSaveButton?: boolean;
  onSaveRef?: MutableRefObject<(() => void) | null>;
  initialValues?: any;
}> = ({
  patientData,
  mode,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
  initialValues,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
  } = dietaryRecallStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { patient } = useCurrentPatientStore();

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? initialValues ?? questions,
    mode: 'onChange',
  });

  const { handleSubmit } = methods;

  const formFields = useMemo(() => {
    if (!questions?.questions?.length) return [];

    return questions.questions;
  }, [questions]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      try {
        const currentFormValues = methods.getValues();

        const processedData = {
          ...data,
          questions: data.questions?.map((question, questionIndex) => ({
            ...question,
            fields: question.fields?.map((field, fieldIndex) => {
              if (
                field.type === 'grouped_table' &&
                Array.isArray(field.value)
              ) {
                const fieldTemplate = questions?.questions
                  ?.find((q: any) => q.id === question.id)
                  ?.fields?.find((f: any) => f.id === field.id);

                const currentFieldValue =
                  currentFormValues?.questions?.[questionIndex]?.fields?.[
                    fieldIndex
                  ]?.value;

                const processedGroups = field.value.map(
                  (group: any, groupIndex: number) => {
                    if (!group.rows || !Array.isArray(group.rows)) {
                      return group;
                    }
                    const currentGroup = currentFieldValue?.[groupIndex];
                    const currentGroupRows =
                      currentGroup?.rows || group.rows || [];

                    const firstRowWithTime =
                      currentGroupRows.find(
                        (row: any) =>
                          row?.time_range &&
                          (row.time_range.from || row.time_range.to)
                      ) ||
                      group.rows.find(
                        (row: any) =>
                          row?.time_range &&
                          (row.time_range.from || row.time_range.to)
                      );

                    let groupDefaultTime =
                      currentGroup?.defaultTime || group.defaultTime;

                    if (
                      (!groupDefaultTime?.from ||
                        groupDefaultTime.from === '') &&
                      (!groupDefaultTime?.to || groupDefaultTime.to === '') &&
                      firstRowWithTime?.time_range
                    ) {
                      groupDefaultTime = {
                        from: firstRowWithTime.time_range.from || '',
                        to: firstRowWithTime.time_range.to || '',
                      };
                    }

                    // If still empty, try to get from template
                    if (
                      (!groupDefaultTime?.from ||
                        groupDefaultTime.from === '') &&
                      (!groupDefaultTime?.to || groupDefaultTime.to === '') &&
                      fieldTemplate &&
                      fieldTemplate.type === 'grouped_table' &&
                      fieldTemplate.mealGroups
                    ) {
                      const mealGroupTemplate = fieldTemplate.mealGroups.find(
                        (mg: any) => mg.id === group.id
                      );

                      if (
                        mealGroupTemplate &&
                        (mealGroupTemplate as any).defaultTime
                      ) {
                        groupDefaultTime = (mealGroupTemplate as any)
                          .defaultTime;
                      }
                    }
                    groupDefaultTime = groupDefaultTime || { from: '', to: '' };

                    const processedRows = group.rows.map(
                      (row: any, rowIndex: number) => {
                        const currentRowTimeRange =
                          currentGroupRows[rowIndex]?.time_range || {};
                        const submittedTimeRange = row.time_range || {};

                        const rowTimeRange =
                          currentRowTimeRange.from || currentRowTimeRange.to
                            ? currentRowTimeRange
                            : submittedTimeRange;

                        const isTimeRangeEmpty =
                          (!rowTimeRange.from || rowTimeRange.from === '') &&
                          (!rowTimeRange.to || rowTimeRange.to === '');

                        const finalTimeRange =
                          isTimeRangeEmpty &&
                          (groupDefaultTime.from || groupDefaultTime.to)
                            ? {
                                from: groupDefaultTime.from || '',
                                to: groupDefaultTime.to || '',
                              }
                            : {
                                from: rowTimeRange.from || '',
                                to: rowTimeRange.to || '',
                              };

                        return {
                          ...row,
                          time_range: finalTimeRange,
                        };
                      }
                    );

                    return {
                      ...group,
                      rows: processedRows,
                    };
                  }
                );

                return {
                  ...field,
                  value: processedGroups,
                };
              }

              // Handle regular table fields
              if (field.type === 'table' && Array.isArray(field.value)) {
                const processedRows = field.value.map((row: any) => {
                  if (!row.time_range || typeof row.time_range !== 'object') {
                    return {
                      ...row,
                      time_range: {
                        from: row.time_range?.from || '',
                        to: row.time_range?.to || '',
                      },
                    };
                  }

                  return {
                    ...row,
                    time_range: {
                      from: row.time_range.from || '',
                      to: row.time_range.to || '',
                    },
                  };
                });

                return {
                  ...field,
                  value: processedRows,
                };
              }

              return field;
            }),
          })),
        };

        if (processedData?.id) {
          const updateData = {
            ...processedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
            // Use processedData.questions which includes the time_range defaults
            questions:
              processedData.questions ||
              patientData?.questions ||
              questions.questions,
          };

          if (!updateData.questions || !Array.isArray(updateData.questions)) {
            console.error(
              ' Questions data is missing or invalid:',
              updateData.questions
            );
            throw new Error('Questions data is required for update');
          }

          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...processedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
            // Include ambient listening data if available from initialValues
            ...(initialValues?.conversation && {
              conversation: initialValues.conversation,
            }),
            ...(initialValues?.recordingDuration && {
              recordingDuration: initialValues.recordingDuration,
            }),
          };

          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error(' Error submitting dietary recall:', error);
      }
    },
    [
      setModalOpen,
      profile?.id,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.general?.department,
      patientData?.questions,
      questions.questions,
      updateLifestyleData,
      createLifestyleData,
      onAfterSubmit,
      initialValues,
      methods,
    ]
  );

  const handleSaveClick = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = handleSaveClick;
    }
    return () => {
      if (onSaveRef) {
        onSaveRef.current = null;
      }
    };
  }, [onSaveRef, handleSaveClick]);

  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  useEffect(() => {
    if (patientData) {
      methods.reset(patientData);
    } else if (initialValues) {
      methods.reset(initialValues);
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, initialValues, questions, methods]);

  return (
    <FormProvider {...methods}>
      <LifestyleModalWrapper
        loading={questionLoading}
        onSubmit={handleSubmit(onSubmit)}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
        data={patientData || undefined}
        doctorName={profile?.general?.fullName}
        patientName={patient?.name}
      >
        <DietaryRecallForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          isAmbientForm={detectAmbientInObject(initialValues || patientData)}
        />
      </LifestyleModalWrapper>
    </FormProvider>
  );
};

export default memo(DietaryRecallModal);
