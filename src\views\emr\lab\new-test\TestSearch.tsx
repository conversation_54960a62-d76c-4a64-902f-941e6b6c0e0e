import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { components, SingleValue } from 'react-select';

import { Box, Divider, Typography } from '@mui/material';
import AsyncSelect from 'react-select/async';

import { useTestStore } from '@/store/emr/lab/reports-store';

import { createDebouncedSearch } from '@/utils/search';

import { LabTestItem } from '@/types/emr/lab';

import CommonSelectDropdown from '../packages/DepartmentDropdown';

interface TestOption {
  value: string;
  label: string;
  test: LabTestItem;
}

interface TestSearchProps {
  placeholder?: string;
  onChange?: (test: LabTestItem | null) => void;
  pageKey?: string;
}

const TestSearch: React.FC<TestSearchProps> = ({
  placeholder = 'Search by Test',
  onChange,
  pageKey,
}) => {
  const { searchTests, setSelectedSearchTest, labDepartments } = useTestStore();

  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<TestOption | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('ALL');

  const abortControllerRef = useRef<AbortController | null>(null);

  const { search: debouncedSearch, cancel: cancelSearch } = useMemo(
    () =>
      createDebouncedSearch<LabTestItem>(
        async (term: string, _signal?: AbortSignal) => {
          const results = await searchTests(term, selectedDepartment);
          return results;
        },
        500,
        {
          minLength: 1,
          maxWait: 1000,
          onError: (error) => {
            console.error('Search error:', error);
          },
        }
      ),
    [searchTests, selectedDepartment]
  );

  const transformResults = useCallback(
    (results: LabTestItem[]): TestOption[] =>
      results
        .filter(
          (test): test is LabTestItem & { id: string; name: string } =>
            !!test.id && !!test.name
        )
        .map((test) => ({
          value: test.id,
          label: test.name,
          test,
        })),
    []
  );

  const loadOptions = async (inputValue: string): Promise<TestOption[]> => {
    const trimmed = inputValue.trim();
    if (!trimmed) {
      cancelSearch();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      return [];
    }

    try {
      // Create a new controller for this specific request and cancel the previous one
      const controller = new AbortController();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = controller;

      const results = (await debouncedSearch(trimmed, controller.signal)) ?? [];

      // If another request started after this one, ignore these results
      if (abortControllerRef.current !== controller) {
        return [];
      }

      return transformResults(results);
    } catch (error) {
      console.error('Load options error:', error);
      return [];
    }
  };

  const handleInputChange = useCallback((newValue: string) => {
    setInputValue(newValue);
  }, []);

  const handleDepartmentChange = useCallback(
    (value: string) => {
      setSelectedDepartment(value);
      cancelSearch();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    },
    [cancelSearch]
  );

  const handleChange = (newValue: SingleValue<TestOption>) => {
    const test = newValue ? newValue.test : null;
    if (test) {
      setSelectedSearchTest(test, pageKey || '');
    }
    onChange?.(test);
    setSelectedOption(newValue);
    setInputValue('');

    // Reset selection visually after a delay
    setTimeout(() => setSelectedOption(null), 800);
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      cancelSearch();
    };
  }, [cancelSearch]);

  const CustomOption = (props: any) => {
    const { test } = props.data;
    return (
      <>
        <components.Option {...props}>
          <Box display="flex" alignItems="center" px={1} py={0.5}>
            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
              {test.name}
            </Typography>
          </Box>
        </components.Option>
        <Divider sx={{ my: 0 }} />
      </>
    );
  };

  const CustomSingleValue = (props: any) => {
    const { test } = props.data;
    return (
      <Box display="flex" alignItems="center" px={1} py={0.5}>
        <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
          {test.name}
        </Typography>
      </Box>
    );
  };

  return (
    <>
      <div className="flex-[2]">
        <AsyncSelect
          loadOptions={loadOptions}
          onChange={handleChange}
          placeholder={placeholder}
          value={selectedOption}
          defaultOptions={[]}
          cacheOptions={false}
          components={{
            Option: CustomOption,
            SingleValue: CustomSingleValue,
            DropdownIndicator: () => null,
            IndicatorSeparator: () => null,
          }}
          inputValue={inputValue}
          onInputChange={handleInputChange}
          noOptionsMessage={({ inputValue }) =>
            inputValue.trim().length === 0
              ? 'Start typing to search tests'
              : 'No tests found'
          }
          menuPortalTarget={document.body}
          styles={{
            menuPortal: (base) => ({ ...base, zIndex: 9999 }),
            control: (base) => ({
              ...base,
              minHeight: '36px',
              height: '36px',
            }),
            valueContainer: (base) => ({
              ...base,
              height: '36px',
              padding: '0 8px',
            }),
            input: (base) => ({
              ...base,
              margin: '0px',
            }),
          }}
        />
      </div>

      <div className="flex-[1.2]">
        <CommonSelectDropdown
          name="department"
          value={selectedDepartment}
          placeholder="Select Department"
          options={labDepartments}
          onChange={handleDepartmentChange}
          className="w-full"
          formControlProps={{ sx: { maxWidth: '100%' } }}
        />
      </div>
    </>
  );
};

export default TestSearch;
