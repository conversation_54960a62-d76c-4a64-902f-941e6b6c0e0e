import { useEffect, useState } from 'react';

import { Controller, FormProvider, useForm } from 'react-hook-form';

import FileInput from '@core/components/file-input';
import TextInput from '@core/components/text-input';

import { cn } from '@/lib/utils';

import { useEnterKeyNavigation } from '@/hooks/useEnterKeyNavigation';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';
import { isFileList } from '@/utils/fileTypeChecks';
import {
  allowOnlyNumbers,
  enforceNumericInput,
  handleNumericInput,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import {
  bloodGroups,
  casteOptions,
  maritalStatus,
  nationalityOptions,
  profileTabs,
  religionOptions,
  yesNoOptions,
} from '@/constants/emr/doctor-profile/personal-info';

import AddressSection from '@/views/emr/doctor-profile/personal-info/AddressSection';
import MiniAddressSection from '@/views/emr/doctor-profile/personal-info/MiniAddressSection';
import SectionTitle from '@/views/emr/doctor-profile/personal-info/SectionTitle';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { useFileUpload } from '@/emr/hooks/use-file-upload';

import DatePicker from '@/core/components/date-picker';
import InputLabel from '@/core/components/input-label';
import {
  DoctorInfo,
  personalDefaultValues,
  PersonalInfo,
} from '@/types/emr/doctor-profile/personal-info';

import { FormRow, SelectField } from './Components';

import AutoResizeTextArea from './shared/AutoResizeTextArea';
import SaveButton from './shared/SaveButton';

const YES = 'yes';
const NO = 'no';
const OTHERS = 'Others';

export function PersonalDetailsSection() {
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);
  const {
    doctorProfile,
    createDoctorProfile,
    updateDoctorProfile,
    fetchDoctorProfileByEmail,
    setTabName,
  } = useDoctorStore();
  const { data: userData } = useUserStore();

  const userId = userData?.id;
  const { fileUpload } = useFileUpload({ userId, type: 'idProof' });

  const methods = useForm({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: personalDefaultValues,
  });

  const { register, setValue, watch, handleSubmit, reset, control } = methods;

  const isPersonWithDisability = watch('isPersonWithDisability');

  const fieldOrder = [
    'age',
    'bloodGroup',
    'height',
    'weight',
    'isPersonWithDisability',
    'identificationMark',
    'maritalStatus',
    'dateOfWedding',
    'percentOfDisability',
    'nationality',
    'religion',
    'otherReligion',
    'caste',
    'otherCaste',
    'category',
    'reservationDetails',
    'idProof.url',
    'idProof.description',
    // Permanent address fields
    'address.permanent.home',
    'address.permanent.pinCode',
    'address.permanent.street',
    'address.permanent.city',
    'address.permanent.district',
    'address.permanent.state',
    'address.permanent.country',
    'address.permanent.phone',
    'address.permanent.mobile',
    'address.permanent.email',
    'address.permanent.proof.url',
    'address.permanent.proof.description',
    // Current address fields
    'address.current.home',
    'address.current.pinCode',
    'address.current.street',
    'address.current.city',
    'address.current.district',
    'address.current.state',
    'address.current.country',
    'address.current.phone',
    'address.current.mobile',
    'address.current.email',
    'address.current.proof.url',
    'address.current.proof.description',
    // Home Town fields
    'hometownDetails.hometown',
    'hometownDetails.state',
    'hometownDetails.district',
    'hometownDetails.country',
    // Place of Birth fields
    'birthDetails.placeOfBirth',
    'birthDetails.state',
    'birthDetails.district',
    'birthDetails.country',
  ];

  // Configure Enter key navigation
  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder,
    isFieldDisabled: (fieldName: string) => isFieldDisabled(fieldName),
    shouldTriggerSave: (fieldName: string) => {
      // Trigger save on the last field (place of birth country)
      return fieldName === 'birthDetails.country';
    },
  });

  useEffect(() => {
    if (doctorProfile) {
      const { personal } = doctorProfile;
      const casteExists = casteOptions.some(
        (option) => option.value === personal?.caste
      );
      const religionExists = religionOptions.some(
        (option) => option.value === personal?.religion
      );

      reset({
        ...personal,
        age: personal?.age?.toString() ?? '',
        height: personal?.height?.toString() ?? '',
        weight: personal?.weight?.toString() ?? '',
        isPersonWithDisability: (personal?.isPersonWithDisability && YES) || NO,
        percentOfDisability: personal?.percentOfDisability ?? '0',
        caste:
          casteExists && personal?.caste
            ? personal?.caste
            : personal?.caste
              ? OTHERS
              : '',
        otherCaste: personal?.caste && !casteExists ? personal?.caste : '',
        religion:
          religionExists && personal?.religion
            ? personal?.religion
            : personal?.religion
              ? OTHERS
              : '',
        otherReligion:
          personal?.religion && !religionExists ? personal?.religion : '',
        idProof: {
          url:
            typeof personal?.idProof?.url === 'string'
              ? personal.idProof.url
              : '',
          description: personal?.idProof?.description || '',
        },
        address: {
          permanent: {
            ...personal?.address?.permanent,
            proof: {
              description:
                personal?.address?.permanent?.proof?.description ?? '',
              url:
                typeof personal?.address?.permanent?.proof?.url === 'string'
                  ? personal?.address?.permanent?.proof?.url
                  : '',
            },
          },
          current: {
            ...personal?.address?.current,
            proof: {
              description: personal?.address?.current?.proof?.description ?? '',
              url:
                typeof personal?.address?.current?.proof?.url === 'string'
                  ? personal?.address?.current?.proof?.url
                  : '',
            },
          },
        },
      });
    }
  }, [doctorProfile, reset]);

  const onSubmit = async ({
    otherReligion,
    otherCaste,
    ...data
  }: PersonalInfo) => {
    try {
      let uploadedIdProofUrl = data?.idProof?.url;

      if (isFileList(uploadedIdProofUrl)) {
        if (uploadedIdProofUrl.length) {
          uploadedIdProofUrl = await fileUpload(uploadedIdProofUrl[0]);
        }
      }

      const finalAddress = await Promise.all([
        async () => {
          let permanentProofUrl = data?.address?.permanent?.proof?.url;
          let currentProofUrl = data?.address?.current?.proof?.url;

          if (isFileList(data?.address?.permanent?.proof?.url)) {
            if (data?.address?.permanent?.proof?.url.length) {
              permanentProofUrl = await fileUpload(
                data?.address?.permanent?.proof?.url[0]
              );
            }
          }

          if (isFileList(data?.address?.current?.proof?.url)) {
            if (data?.address?.current?.proof?.url.length) {
              currentProofUrl = await fileUpload(
                data?.address?.current?.proof?.url[0]
              );
            }
          }

          return {
            permanent: {
              ...data?.address?.permanent,
              proof: {
                ...data?.address?.permanent?.proof,
                url: permanentProofUrl,
              },
            },
            current: {
              ...data?.address?.current,
              proof: {
                ...data?.address?.current?.proof,
                url: currentProofUrl,
              },
            },
          };
        },
      ]).then(([result]) => result);

      const personalData: Partial<DoctorInfo['personal']> = {
        ...data,
        percentOfDisability: '0',
        isPersonWithDisability:
          data.isPersonWithDisability == YES ? true : false,
        dateOfWedding: formatDate(
          data.dateOfWedding,
          DateFormats.DATE_YYYY_MM_DD
        ),
        idProof: {
          ...data.idProof,
          url: uploadedIdProofUrl,
          type: '',
          number: '',
        },
        address: await finalAddress(),
        caste: data.caste === OTHERS ? otherCaste || data.caste : data.caste,
        religion:
          data.religion === OTHERS
            ? otherReligion || data.religion
            : data.religion,
      };

      const payload = { personal: personalData };
      if (doctorProfile && doctorProfile.id) {
        await updateDoctorProfile(doctorProfile.id, payload);
        setEditableField(new Set());
        setIsSubmitted(true);
      } else {
        await createDoctorProfile({
          personal: personalData,
          username: userData?.email,
        });
        setEditableField(new Set());
        setIsSubmitted(true);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  useEffect(() => {
    if (isPersonWithDisability === 'no') {
      setValue('percentOfDisability', '');
    }
  }, [isPersonWithDisability, setValue]);

  useEffect(() => {
    setTabName(profileTabs.PERSONAL_DETAILS);
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData?.email);
    }
  }, [userData?.email, fetchDoctorProfileByEmail, setTabName]);

  const handleEditClick = (fieldName: string) => {
    setEditableField((prev) => new Set(prev.add(fieldName)));
  };

  const casteExists = casteOptions.some(
    (option) => option.value === doctorProfile?.personal?.caste
  );
  const religionExists = religionOptions.some(
    (option) => option.value === doctorProfile?.personal?.religion
  );

  const isFieldDisabled = (fieldName: string) => {
    if (editableField.has(fieldName)) {
      return false;
    }

    const formValues = methods.getValues() as PersonalInfo;

    // Handle conditional fields for navigation - if they're not rendered, treat them as disabled
    if (fieldName === 'otherReligion') {
      // If religion is not "Others", the otherReligion field is not rendered, so treat as disabled for navigation
      if (watch('religion') !== OTHERS) {
        return true;
      }
      // Otherwise, check the normal disabled logic
      if (
        (isSubmitted && methods.getValues('otherReligion')) ||
        !religionExists
      ) {
        return isFieldDisabled('religion');
      }
    }

    if (fieldName === 'otherCaste') {
      // If caste is not "Others", the otherCaste field is not rendered, so treat as disabled for navigation
      if (watch('caste') !== OTHERS) {
        return true;
      }
      // Otherwise, check the normal disabled logic
      if ((isSubmitted && methods.getValues('otherCaste')) || !casteExists) {
        return isFieldDisabled('caste');
      }
    }

    const fieldValue = formValues[fieldName as keyof PersonalInfo];
    const doctorFieldValue = getNestedValue(doctorProfile?.personal, fieldName);

    return (
      !!doctorFieldValue ||
      (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
    );
  };

  const renderEditIcon = (fieldName: string) => {
    return isFieldDisabled(fieldName) ? (
      <button type="button" onClick={() => handleEditClick(fieldName)}>
        <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
      </button>
    ) : null;
  };

  return (
    <div className="w-full mb-3 pr-2 px-1 pb-5 md:pb-0">
      <SectionTitle className="mb-5 mt-1" title="personal details" />
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-y-4 md:gap-y-6">
            <div className="w-full flex gap-2 flex-col md:flex-row">
              <TextInput
                key={'age'}
                label={'Age'}
                placeholder={'Your Age'}
                color="white"
                {...register('age')}
                onKeyDown={(e) => {
                  allowOnlyNumbers(e);
                  handleKeyDown(e, 'age');
                }}
                onInput={enforceNumericInput()}
                pattern="[0-9]*"
                inputMode="numeric"
                disabled={isFieldDisabled('age')}
                endDecoration={renderEditIcon('age')}
                className="w-full md:w-[18%]"
              />
              <div className={cn('flex gap-2', 'w-full md:w-[36%]')}>
                <SelectField
                  id="bloodGroup"
                  label="Blood Group"
                  options={bloodGroups}
                  value={watch('bloodGroup')}
                  onChange={(value) => {
                    setValue('bloodGroup', value);
                  }}
                  wrapperClassName="w-full"
                  placeholder="Select"
                  disabledInput={isFieldDisabled('bloodGroup')}
                  endDecoration={renderEditIcon('bloodGroup')}
                  onKeyDown={(e) => handleKeyDown(e, 'bloodGroup')}
                />
                <TextInput
                  key={'height'}
                  label={'Height(Cm)'}
                  placeholder={'158'}
                  color="white"
                  {...register('height')}
                  disabled={isFieldDisabled('height')}
                  endDecoration={renderEditIcon('height')}
                  onKeyDown={(e) => {
                    allowOnlyNumbers(e);
                    handleKeyDown(e, 'height');
                  }}
                  onInput={enforceNumericInput()}
                  pattern="[0-9]*"
                  inputMode="numeric"
                  className="w-full"
                />
              </div>
              <div className={cn('flex gap-2', 'w-full md:w-[46%]')}>
                <TextInput
                  key={'weight'}
                  label={'Weight(Kg)'}
                  placeholder={'57'}
                  color="white"
                  {...register('weight')}
                  disabled={isFieldDisabled('weight')}
                  endDecoration={renderEditIcon('weight')}
                  onKeyDown={(e) => {
                    handleNumericInput(e);
                    handleKeyDown(e, 'weight');
                  }}
                  onInput={enforceNumericInput({ decimalPlaces: 1 })}
                  pattern="[0-9]*"
                  inputMode="numeric"
                  className="w-[30%] md:w-2/5"
                />
                <SelectField
                  id="isPersonWithDisability"
                  label="Are you a person with disability?"
                  options={yesNoOptions}
                  value={watch('isPersonWithDisability')}
                  onChange={(value) => {
                    setValue('isPersonWithDisability', value);
                  }}
                  wrapperClassName="w-[70%] md:w-3/5"
                  placeholder="No"
                  disabledInput={isFieldDisabled('isPersonWithDisability')}
                  endDecoration={renderEditIcon('isPersonWithDisability')}
                  onKeyDown={(e) => handleKeyDown(e, 'isPersonWithDisability')}
                />
              </div>
            </div>
            <FormRow>
              <AutoResizeTextArea
                key={'identificationMark'}
                label="Identification Mark"
                color="white"
                name="identificationMark"
                control={control}
                disabled={isFieldDisabled('identificationMark')}
                endDecoration={renderEditIcon('identificationMark')}
                onKeyDown={(e) => {
                  preventNonAlphabeticInput(e);
                  handleKeyDown(e, 'identificationMark');
                }}
                className="w-full md:w-1/4 order-2 md:order-1 pr-0 md:pr-2"
              />
              <SelectField
                id="maritalStatus"
                label=" Marital Status"
                options={maritalStatus}
                wrapperClassName="w-1/2 md:w-1/4 order-3 md:order-2 pr-2"
                value={watch('maritalStatus')}
                onChange={(value) => {
                  setValue('maritalStatus', value);
                }}
                placeholder="Select"
                disabledInput={isFieldDisabled('maritalStatus')}
                endDecoration={renderEditIcon('maritalStatus')}
                onKeyDown={(e) => handleKeyDown(e, 'maritalStatus')}
              />
              <div className="w-1/2 md:w-1/4 order-4 md:order-3 pr-0 md:pr-2">
                <DatePicker
                  label="Date of Wedding"
                  format="DD/MM/YYYY"
                  name="dateOfWedding"
                  control={control}
                  disableInput={isFieldDisabled('dateOfWedding')}
                  renderEditIcon={() => renderEditIcon('dateOfWedding')}
                  onKeyDown={(e) => handleKeyDown(e, 'dateOfWedding')}
                />
              </div>
              <TextInput
                key={'percentOfDisability'}
                label="Percentage of Disability"
                placeholder="0"
                color="white"
                {...register('percentOfDisability')}
                disabled={
                  isFieldDisabled('percentOfDisability') ||
                  isPersonWithDisability === NO
                }
                endDecoration={
                  renderEditIcon('percentOfDisability') ||
                  isPersonWithDisability === YES
                }
                onKeyDown={(e) => {
                  allowOnlyNumbers(e);
                  handleKeyDown(e, 'percentOfDisability');
                }}
                className="w-full md:w-1/4 order-1 md:order-4"
              />
            </FormRow>

            <FormRow>
              <SelectField
                id="nationality"
                label="Nationality"
                options={nationalityOptions}
                value={watch('nationality')}
                wrapperClassName="w-1/2 md:w-1/4 pr-2"
                onChange={(value) => {
                  setValue('nationality', value);
                }}
                placeholder="Select"
                disabledInput={isFieldDisabled('nationality')}
                endDecoration={renderEditIcon('nationality')}
                onKeyDown={(e) => handleKeyDown(e, 'nationality')}
              />
              <SelectField
                id="religion"
                label="Religion"
                options={religionOptions}
                value={watch('religion')}
                wrapperClassName="w-1/2 md:w-1/4"
                onChange={(value) => {
                  setValue('religion', value);
                }}
                placeholder="Select"
                disabledInput={isFieldDisabled('religion')}
                endDecoration={renderEditIcon('religion')}
                onKeyDown={(e) => handleKeyDown(e, 'religion')}
              />
              {watch('religion') === OTHERS && (
                <TextInput
                  key={'otherReligion'}
                  label="Specify Religion"
                  placeholder="Enter Religion"
                  color="white"
                  {...register('otherReligion')}
                  disabled={isFieldDisabled('otherReligion')}
                  endDecoration={renderEditIcon('otherReligion')}
                  onKeyDown={(e) => {
                    preventNonAlphabeticInput(e);
                    handleKeyDown(e, 'otherReligion');
                  }}
                  className="w-full md:w-1/4 md:pl-2"
                />
              )}
              <SelectField
                id="caste"
                label="Caste"
                options={casteOptions}
                value={watch('caste')}
                wrapperClassName="w-1/2 md:w-1/4 pr-2 md:pl-2"
                onChange={(value) => {
                  setValue('caste', value);
                }}
                placeholder="Select"
                disabledInput={isFieldDisabled('caste')}
                endDecoration={renderEditIcon('caste')}
                onKeyDown={(e) => handleKeyDown(e, 'caste')}
              />
              {watch('caste') === OTHERS && (
                <TextInput
                  key={'otherCaste'}
                  label="Specify Caste"
                  placeholder="Enter Caste"
                  color="white"
                  disabled={isFieldDisabled('otherCaste')}
                  endDecoration={renderEditIcon('otherCaste')}
                  onKeyDown={(e) => {
                    preventNonAlphabeticInput(e);
                    handleKeyDown(e, 'otherCaste');
                  }}
                  className="w-1/2 md:w-1/4"
                  {...register('otherCaste')}
                />
              )}
            </FormRow>

            <FormRow>
              <div className="flex flex-col w-full md:w-1/4">
                <InputLabel label="Category" className="mb-1 md:mb-4.5" />
                <div className="relative">
                  {/* Invisible focusable element for navigation */}
                  <input
                    name="category"
                    type="text"
                    className="absolute inset-0 opacity-0 cursor-pointer"
                    value=""
                    onChange={() => {}}
                    onKeyDown={(e) => handleKeyDown(e, 'category')}
                    onFocus={(e) => {
                      // When this invisible input gets focus, immediately blur it
                      // but keep the visual focus on the radio group
                      e.target.blur();
                    }}
                    tabIndex={0}
                  />
                  <RadioGroup
                    value={watch('category')}
                    onValueChange={(value) => setValue('category', value)}
                    className="flex gap-6"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="General" id="general" />
                      <label htmlFor="general">General</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Reserved" id="reserved" />
                      <label htmlFor="reserved">Reserved</label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
              <TextInput
                key={'reservationDetails'}
                label="Reservation Details"
                color="white"
                {...register('reservationDetails')}
                disabled={isFieldDisabled('reservationDetails')}
                endDecoration={renderEditIcon('reservationDetails')}
                onKeyDown={(e) => {
                  preventNonAlphabeticInput(e);
                  handleKeyDown(e, 'reservationDetails');
                }}
                className="w-full md:w-122"
              />
            </FormRow>

            <FormRow>
              <Controller
                name="idProof.url"
                control={control}
                render={({ field }) => (
                  <FileInput
                    label="ID Proof with photo"
                    value={field.value}
                    onChange={(files) => field.onChange(files)}
                    className="w-1/2 pr-1"
                    allowedFileTypes={[
                      'image/jpeg',
                      'image/png',
                      'application/pdf',
                    ]}
                    fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
                    showPreview
                    maxFileSize={6}
                    isBoxStyle
                    onKeyDown={(e) => handleKeyDown(e, 'idProof.url')}
                  />
                )}
              />
              <TextInput
                key={'idProof.description'}
                label="ID Description"
                color="white"
                className="w-1/2 pl-1"
                {...register('idProof.description')}
                disabled={isFieldDisabled('idProof.description')}
                endDecoration={renderEditIcon('idProof.description')}
                onKeyDown={(e) => {
                  preventNonAlphabeticInput(e);
                  handleKeyDown(e, 'idProof.description');
                }}
              />
            </FormRow>

            <FormRow className="flex-col md:flex-row flex-nowrap">
              <AddressSection
                className="flex-1"
                type="permanent"
                isFieldDisabled={isFieldDisabled}
                renderEditIcon={renderEditIcon}
                handleKeyDown={handleKeyDown}
              />
              <div className="bg-[#637D92] w-0 md:w-[0.05rem] mx-0 md:mx-8"></div>
              <AddressSection
                className="flex-1"
                type="current"
                isFieldDisabled={isFieldDisabled}
                renderEditIcon={renderEditIcon}
                handleKeyDown={handleKeyDown}
              />
            </FormRow>

            <div className="mt-4 md:mt-8 flex flex-col gap-4 md:gap-6 w-full">
              <MiniAddressSection
                title="Home Town"
                type="hometownDetails"
                value="hometown"
                isFieldDisabled={isFieldDisabled}
                renderEditIcon={renderEditIcon}
                handleKeyDown={handleKeyDown}
              />
              <MiniAddressSection
                title="Place Of Birth"
                type="birthDetails"
                value="placeOfBirth"
                isFieldDisabled={isFieldDisabled}
                renderEditIcon={renderEditIcon}
                handleKeyDown={handleKeyDown}
              />
            </div>
          </div>
          <SaveButton className="mt-15" />
        </form>
      </FormProvider>
    </div>
  );
}
