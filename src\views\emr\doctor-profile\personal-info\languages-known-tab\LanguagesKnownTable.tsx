import React, { FC, useMemo } from 'react';

import { Control, Controller } from 'react-hook-form';

import Checkbox from '@mui/material/Checkbox';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

import { useEnterKeyNavigation } from '@/hooks/useEnterKeyNavigation';

import {
  fluencyLevels,
  languages,
} from '@/constants/emr/doctor-profile/personal-info';

import { cellStyles, checkboxStyles, headerCellStyles } from '../Components';

import { FormData } from '.';

type Props = {
  control: Control<FormData>;
};

const LanguagesKnownTable: FC<Props> = ({ control }) => {
  // Create field order for all language checkboxes
  const fieldOrder = useMemo(() => {
    const allFields: string[] = [];
    languages.forEach((language) => {
      fluencyLevels.forEach((level) => {
        allFields.push(`languages.${language}.${level}`);
      });
    });
    return allFields;
  }, []);

  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder,
    shouldTriggerSave: (fieldName: string) => {
      // Trigger save on the last checkbox
      const lastLanguage = languages[languages.length - 1];
      const lastLevel = fluencyLevels[fluencyLevels.length - 1];
      return fieldName === `languages.${lastLanguage}.${lastLevel}`;
    },
  });

  return (
    <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={headerCellStyles}>Languages</TableCell>
            {fluencyLevels?.map((level) => (
              <TableCell key={level} sx={headerCellStyles}>
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {languages.map((language) => (
            <TableRow key={language}>
              <TableCell sx={cellStyles}>{language}</TableCell>
              {fluencyLevels.map((level) => (
                <TableCell key={level} sx={cellStyles}>
                  <Controller
                    name={`languages.${language}.${level}`}
                    control={control}
                    render={({ field }) => (
                      <Checkbox
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        sx={checkboxStyles}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleKeyDown(e, `languages.${language}.${level}`);
                          }
                        }}
                      />
                    )}
                  />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default LanguagesKnownTable;
