import { useCallback, useMemo } from 'react';

import { cn } from '@/lib/utils';

import {
  AppointmentStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

export type AvatarProps = {
  name: string;
  status?: AppointmentStatus;
  patientStatus?: PatientStatus;
  queuePosition?: number;
};
export default function Avatar({
  name,
  status,
  patientStatus,
  queuePosition,
}: AvatarProps) {
  const displayText = useMemo(() => {
    if (queuePosition !== undefined) {
      return queuePosition.toString();
    }
    return name
      .split(' ')
      .slice(0, 2)
      .map((item) => item[0]?.toUpperCase())
      .join('');
  }, [name, queuePosition]);

  const getAvatarColor = useCallback(() => {
    const red = 'ring-2 ring-[#E4626F]';
    const green = 'ring-2 ring-[#06C6A7]';
    const yellow = 'ring-2 ring-[#FF9F2A]';
    const darkGrey = 'ring-2 ring-[#7E8F99]';

    if (status === AppointmentStatus.Consultation) {
      return green;
    }

    if (status === AppointmentStatus.Booked) {
      if (patientStatus === PatientStatus.NoShow) {
        return red;
      }

      if (patientStatus === PatientStatus.Arrived) {
        return green;
      }

      if (patientStatus === PatientStatus.ProxyVisit) {
        return yellow;
      }

      return darkGrey;
    }

    return red;
  }, [patientStatus, status]);

  return (
    <div
      className={cn([
        'rounded-full w-8 h-8 2xl:h-10 2xl:w-10 flex items-center justify-center col-span-2',
      ])}
    >
      <div
        className={cn(
          getAvatarColor(),
          'h-7 w-7 2xl:h-9 2xl:w-9 rounded-full bg-[#C2CDD6] flex items-center justify-center'
        )}
      >
        <span className="text-white text-[10px] 2xl:text-xs font-medium">
          {displayText}
        </span>
      </div>
    </div>
  );
}
