import React, { ReactN<PERSON>, use<PERSON><PERSON>back, MouseEvent, useEffect } from 'react';

import { styled, SxProps, Theme, Typography } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { AiOutlineRight } from 'react-icons/ai';
import { FaRegUser } from 'react-icons/fa6';
import { IoMenu } from 'react-icons/io5';
import { MdPowerSettingsNew } from 'react-icons/md';
import { TbSwitchHorizontal } from 'react-icons/tb';

import { usePathname, useRouter } from 'next/navigation';

import { logout } from '@core/lib/auth/services';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useMobileMenuStore } from '@/store/mobileMenuStore';
import { useUserStore } from '@/store/userStore';

import colors from '@/utils/colors';
import { menu, MobileMenuType } from '@/utils/constants/mobileMenu';
import { getModuleSwitchUrl } from '@/utils/switch-module';

import { routes } from '@/constants/routes';

import Avatar from '@/views/emr/doctor-profile/profile-sidebar/avatar';

interface MenuListItemProps {
  icon?: ReactNode;
  text: string;
  onClick?: () => void;
  sx?: SxProps<Theme>;
  showArrow?: boolean;
  disabled?: boolean;
}

const menuItemStyles = {
  borderRadius: 1,
  border: `1px solid ${colors.common.ashGray}`,
  marginTop: 2,
  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
  fontSize: '16px',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
};

const StyledMenu = styled(Menu)(({}) => ({
  '& .MuiPaper-root': {
    transition: 'none !important',
    transform: 'none !important',
    top: '97px !important',
    left: '16px !important',
    boxShadow: 'none',
    width: '100vw',
    height: 'calc(100dvh - 6.3rem)',
  },
  '& .MuiMenu-paper': {
    transition: 'none !important',
  },
  '& .MuiPopover-root': {
    transition: 'none !important',
  },
}));

const MenuListItem = ({
  icon,
  text,
  onClick,
  showArrow = false,
  sx,
  disabled = false,
}: MenuListItemProps) => (
  <MenuItem
    onClick={onClick}
    disabled={disabled}
    sx={{
      ...menuItemStyles,
      padding: (t) => t.spacing(0.5, 2),
      ...sx,
    }}
  >
    <div className="flex items-center gap-2">
      {icon && (
        <ListItemIcon className="text-black font-bold">{icon}</ListItemIcon>
      )}
      <Typography className="!font-archivo font-medium">{text}</Typography>
    </div>
    {showArrow && <AiOutlineRight className="text-black text-lg" />}
  </MenuItem>
);

const MobileMenu = () => {
  const {
    anchorEl,
    setAnchorEl,
    mobileMenuPage,
    setMobileMenuPage,
    setIsFromMenu,
    setMenuClick,
    selectedRoute,
    setSelectedRoute,
    isMenuOpen,
    setIsMenuOpen,
    isNavigating,
    setIsNavigating,
    isPageLoading,
    setIsPageLoading,
    setIsProfileMenu,
    isFromProfile,
    setIsFromProfile,
  } = useMobileMenuStore();
  const { doctorProfile } = useDoctorStore();
  const {
    data: userData,
    permissions = [],
    fetchUserPermissions,
  } = useUserStore();

  const router = useRouter();
  const pathName = usePathname();
  const isClinicUser = userData?.accountType === 'clinic';
  const open =
    Boolean(anchorEl) ||
    Object.values(menu).includes(mobileMenuPage as MobileMenuType);

  const handleClick = useCallback(
    (event: MouseEvent<HTMLElement>) => {
      setAnchorEl(event.currentTarget);
      setMobileMenuPage(menu.MAIN_MENU);
      setMenuClick(true);
      setIsMenuOpen(true);
      setIsFromProfile(false);
    },
    [
      setAnchorEl,
      setMobileMenuPage,
      setMenuClick,
      setIsMenuOpen,
      setIsFromProfile,
    ]
  );

  const handleClose = useCallback(() => {
    if (mobileMenuPage === menu.PROFILE_MENU) {
      setIsProfileMenu(false);
      setMobileMenuPage(menu.MAIN_MENU);
      setIsFromProfile(true);
    } else if (mobileMenuPage === menu.MAIN_MENU && isFromProfile) {
      setMobileMenuPage(menu.PROFILE_MENU);
      setIsFromProfile(false);
    } else {
      setAnchorEl(null);
      setMobileMenuPage('');
    }
  }, [
    mobileMenuPage,
    isFromProfile,
    setIsProfileMenu,
    setMobileMenuPage,
    setIsFromProfile,
    setAnchorEl,
  ]);

  const handleUserProfileClick = useCallback(() => {
    setSelectedRoute('');
    setMobileMenuPage(menu.PROFILE_MENU);
  }, [setMobileMenuPage, setSelectedRoute]);

  const handleLogout = useCallback(() => {
    handleClose();
    logout();
  }, [handleClose]);

  const handleProfileNavigation = useCallback(
    (route: string) => async () => {
      try {
        setIsNavigating(true);
        setIsPageLoading(true);
        await router.push(route);
        await new Promise((resolve) => setTimeout(resolve, 300));

        setIsPageLoading(true);
        setSelectedRoute(route);

        await new Promise((resolve) => {
          const checkLoaded = () => {
            if (document.readyState === 'complete') {
              resolve(true);
            } else {
              setTimeout(checkLoaded, 300);
            }
          };
          checkLoaded();
        });

        await new Promise((resolve) => setTimeout(resolve, 1000));

        setIsFromMenu(true);
        setMobileMenuPage('');
        setIsMenuOpen(false);
        setAnchorEl(null);
      } catch (error) {
        console.error('Navigation error:', error);
      } finally {
        setIsNavigating(false);
        setIsPageLoading(false);
      }
    },
    [
      setIsNavigating,
      setIsPageLoading,
      router,
      setSelectedRoute,
      setIsFromMenu,
      setMobileMenuPage,
      setIsMenuOpen,
      setAnchorEl,
    ]
  );

  const handleSwitchModule = useCallback(async () => {
    try {
      const freshPermissions = await fetchUserPermissions();
      const switchUrl = getModuleSwitchUrl(pathName, freshPermissions);
      router.push(switchUrl);
      handleClose();
    } catch (error) {
      console.error('Error switching modules:', error);

      router.push(getModuleSwitchUrl(pathName, permissions));
      handleClose();
    }
  }, [router, pathName, permissions, handleClose, fetchUserPermissions]);

  useEffect(() => {
    if (pathName === selectedRoute && !isMenuOpen) {
      handleClose();
    }
  }, [pathName, selectedRoute, handleClose, isMenuOpen]);

  useEffect(() => {
    if (
      pathName === routes.EMR_PROFILE_PERSONAL_INFO ||
      pathName === routes.EMR_PROFILE_CUSTOMISE_EMR
    ) {
      setSelectedRoute(pathName);
    }
  }, [pathName, setSelectedRoute]);

  const renderMainMenu = useCallback(
    () => (
      <>
        <MenuListItem
          icon={
            <FaRegUser color="black" size={18} className="text-xl font-bold" />
          }
          text="My Profile"
          onClick={handleUserProfileClick}
        />
        <MenuListItem
          icon={
            <TbSwitchHorizontal color="black" className="text-xl font-bold" />
          }
          text="Switch to MRD"
          onClick={handleSwitchModule}
        />
        <MenuListItem
          icon={
            <MdPowerSettingsNew color="black" className="text-xl font-bold" />
          }
          text="Logout"
          onClick={handleLogout}
        />
      </>
    ),
    [handleUserProfileClick, handleLogout, handleSwitchModule]
  );

  const renderProfileMenu = useCallback(
    () => (
      <>
        <Typography
          variant="subtitle1"
          className="!font-archivo font-medium"
          sx={{
            borderBottom: `1px solid ${colors.common.ashGray}`,
            fontSize: '20px',
          }}
        >
          My profile
        </Typography>

        <div className="card grid gap-1 place-items-center px-1 pt-3 pb-2 2xl:pb-5">
          <Avatar />
          <div className="grid gap-0.5 w-full text-black px-1">
            <div className="font-semibold leading-7 text-lg xl:text-xl text-center break-words w-full">
              {doctorProfile?.general?.fullName || userData?.name}
            </div>
            <div className="font-extralight capitalize text-center w-full">
              {doctorProfile?.general?.designation}
              {doctorProfile?.general?.designation &&
                doctorProfile?.general?.department &&
                ' | '}
              {doctorProfile?.general?.department}
            </div>
          </div>
        </div>

        <MenuItem
          disabled={isNavigating || isPageLoading}
          onClick={handleProfileNavigation(routes.EMR_PROFILE_PERSONAL_INFO)}
          sx={menuItemStyles}
        >
          <div className="flex justify-between items-center w-full">
            <span>Personal Info</span>
            {(isNavigating || isPageLoading) &&
              selectedRoute === routes.EMR_PROFILE_PERSONAL_INFO && (
                <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
              )}
          </div>
        </MenuItem>

        <MenuItem
          disabled={isNavigating || isPageLoading}
          onClick={handleProfileNavigation(routes.EMR_PROFILE_CUSTOMISE_EMR)}
          sx={menuItemStyles}
        >
          <div className="flex justify-between items-center w-full">
            <span>Customise EMR</span>
            {(isNavigating || isPageLoading) &&
              selectedRoute === routes.EMR_PROFILE_CUSTOMISE_EMR && (
                <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
              )}
          </div>
        </MenuItem>

        {isClinicUser && (
          <MenuItem
            disabled={isNavigating || isPageLoading}
            onClick={handleProfileNavigation('/emr/profile/payments-plans')}
            sx={menuItemStyles}
          >
            <div className="flex justify-between items-center w-full">
              <span>Payments & Plans</span>
              {(isNavigating || isPageLoading) &&
                selectedRoute === '/emr/profile/payments-plans' && (
                  <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                )}
            </div>
          </MenuItem>
        )}
      </>
    ),
    [
      doctorProfile?.general?.fullName,
      doctorProfile?.general?.designation,
      doctorProfile?.general?.department,
      userData?.name,
      handleProfileNavigation,
      isNavigating,
      isPageLoading,
      selectedRoute,
    ]
  );

  return (
    <>
      <style jsx global>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
      <div>
        <IconButton
          onClick={handleClick}
          aria-controls={open ? 'account-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
        >
          <IoMenu />
        </IconButton>
        {open && (
          <div className="absolute z-[1111] top-300 left-0 right-0 bg-white p-4 h-[calc(100dvh-6.3rem)] "></div>
        )}
        <StyledMenu
          anchorEl={anchorEl}
          id="account-menu"
          open={open}
          onClose={handleClose}
          transformOrigin={{ horizontal: 'left', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'left', vertical: 'top' }}
        >
          {mobileMenuPage === menu.PROFILE_MENU
            ? renderProfileMenu()
            : renderMainMenu()}
        </StyledMenu>
      </div>
    </>
  );
};

export default MobileMenu;
