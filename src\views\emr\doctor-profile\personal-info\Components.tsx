import { forwardRef, ReactNode, TextareaHTMLAttributes } from 'react';

import { FieldError } from 'react-hook-form';

import { Autocomplete, Button, TextField } from '@mui/material';
import { MdKeyboardArrowDown } from 'react-icons/md';

import TextInput from '@core/components/text-input';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import InputLabel from '@/core/components/input-label';
interface OptionType {
  label: string;
  value: string;
}
export interface SelectFieldProps {
  id?: string;
  label?: string;
  options: OptionType[];
  value?: string;
  onChange: (value: string) => void;
  wrapperClassName?: string;
  placeholder?: string;
  disabled?: boolean;
  disabledInput?: boolean;
  endDecoration?: React.ReactNode;
  customArrowIcon?: React.ReactNode;
  selectClassName?: string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

export const EditableSelectField: React.FC<SelectFieldProps> = ({
  id,
  label,
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  disabled,
  disabledInput,
  endDecoration,
  wrapperClassName,
  customArrowIcon = <MdKeyboardArrowDown size={20} color="gray" />,
  onKeyDown,
}) => {
  const selectedOption = options.find((option) => option.value === value) || {
    label: value,
    value: value,
  };

  return (
    <div className={`flex flex-col ${wrapperClassName}`}>
      {label && <InputLabel label={label} />}
      {disabledInput ? (
        <TextInput
          key={id}
          color="white"
          disabled={disabledInput}
          endDecoration={endDecoration}
          value={value}
        />
      ) : (
        <Autocomplete
          id={id}
          options={options}
          getOptionLabel={(option) => option.label || ''}
          value={selectedOption}
          onChange={(_, newValue) => {
            if (newValue) {
              onChange(newValue?.value || '');
            }
          }}
          disableClearable
          popupIcon={customArrowIcon}
          fullWidth
          filterOptions={(options, { inputValue }) => {
            const filtered = options.filter(
              (option) =>
                (option.label?.toLowerCase() ?? '').indexOf(
                  inputValue.toLowerCase()
                ) >= 0
            );
            if (
              inputValue &&
              !filtered.find(
                (opt) => opt?.label?.toLowerCase() === inputValue.toLowerCase()
              )
            ) {
              filtered.push({
                label: inputValue,
                value: inputValue,
              });
            }
            return filtered;
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              name={id}
              placeholder={placeholder}
              variant="outlined"
              disabled={disabled}
              onChange={(event) => {
                onChange(event.target.value);
              }}
              onKeyDown={onKeyDown}
              sx={{
                '& .MuiOutlinedInput-root': {
                  padding: '2px 10px',
                  minHeight: '36px',
                  fontSize: '15px',
                  borderRadius: '6px',
                  border: '0.5px solid #637D92',
                  '& fieldset': {
                    border: 'none',
                  },
                  '&:hover': {
                    border: '0.5px solid #637D92',
                  },
                  '&.Mui-focused': {
                    border: '0.5px solid #637D92',
                  },
                },
                '& .MuiInputBase-input': {
                  padding: '4px 0',
                },
              }}
            />
          )}
        />
      )}
    </div>
  );
};

export const SelectField: React.FC<SelectFieldProps> = ({
  id,
  label,
  options,
  value,
  onChange,
  wrapperClassName = '',
  selectClassName = '',
  placeholder = 'Select an option',
  disabled,
  disabledInput,
  endDecoration,
  onKeyDown,
}) => {
  const selectedOption = options?.find((option) => option?.value === value);

  return (
    <div className={`flex flex-col ${wrapperClassName}`}>
      {label && <InputLabel label={label} />}
      {disabledInput ? (
        <TextInput
          key={id}
          color="white"
          disabled={disabledInput}
          endDecoration={endDecoration}
          value={selectedOption?.label}
        />
      ) : (
        <Select value={value} onValueChange={onChange} disabled={disabled}>
          <SelectTrigger
            id={id}
            name={id}
            className={`${value ? 'text-black' : 'text-[#637D92]'} ${selectClassName} text-base py-5 border border-[#637D92]`}
            onKeyDown={onKeyDown}
          >
            <SelectValue placeholder={placeholder}>
              {selectedOption?.label || placeholder}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <div className="max-h-70 overflow-y-auto">
              {options.length > 0 ? (
                options.map((option) => (
                  <SelectItem key={option.value} value={option.value || ''}>
                    {option.label}
                  </SelectItem>
                ))
              ) : (
                <div className="text-center py-2 text-gray-500">
                  No options available
                </div>
              )}
            </div>
          </SelectContent>
        </Select>
      )}
    </div>
  );
};

export function FormRow(props: { children: ReactNode; className?: string }) {
  return (
    <div
      className={cn(
        'w-full flex flex-wrap gap-y-4 md:gap-y-6',
        props.className
      )}
    >
      {props.children}
    </div>
  );
}

export const OutlinedButton: React.FC<{
  label: string;
  isSelected: boolean;
  onClick: () => void;
}> = ({ label, isSelected, onClick }) => {
  const handleClick = () => {
    onClick();
  };

  return (
    <Button
      variant="outlined"
      onClick={handleClick}
      sx={{
        py: 0,
        px: 3,
        textColor: 'black',
        maxHeight: '32px',
        backgroundColor: isSelected ? 'black' : 'transparent',
        borderColor: 'black',
        color: isSelected ? 'white' : 'black',
        '&:hover': {
          backgroundColor: isSelected ? 'black' : 'transparent',
          borderColor: 'black',
        },
      }}
    >
      {label}
    </Button>
  );
};

export const headerCellStyles = {
  backgroundColor: '#001926',
  color: 'white',
  padding: '4px',
  fontSize: '16px',
  border: `1px solid ${colors.common.charcoalGray}`,
  paddingLeft: '8px',
  height: '36px',
};

export const cellStyles = {
  border: `1px solid ${colors.common.charcoalGray}`,
  padding: '2px',
  paddingLeft: '8px',
  fontSize: '12px',
  height: '36px',
};

export const checkboxStyles = {
  padding: '2px',
  '& .MuiSvgIcon-root': {
    fontSize: '20px',
    color: 'black',
  },
  '&.Mui-checked .MuiSvgIcon-root': {
    color: 'black',
  },
};

export interface TextAreaProps
  extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  errors?: FieldError;
  endDecoration?: React.ReactNode;
  inputClassName?: string;
  labelClassName?: string;
  iconClassName?: string;
  endIconSecondary?: React.ReactNode;
  textareaClassName?: string;
}

export const TextAreaInput = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  (
    {
      className,
      errors,
      label,
      endDecoration,
      inputClassName,
      disabled,
      labelClassName,
      iconClassName,
      endIconSecondary,
      placeholder,
      required = false,
      textareaClassName = '',
      ...rest
    },
    ref
  ) => {
    return (
      <label className={`relative flex flex-col text-[#001926] ${className}`}>
        <InputLabel
          label={label}
          className={labelClassName}
          required={required}
        />
        <div className={`relative ${inputClassName}`}>
          <textarea
            ref={ref}
            className={`rounded-md text-base py-2 px-4.5 pr-13 h-full border w-full focus:outline-none focus:ring-0 resize-none ${
              errors?.message ? 'border-red-500' : 'border-[#637D92]'
            } ${textareaClassName}`}
            disabled={disabled}
            placeholder={placeholder}
            {...rest}
          />
          {endDecoration && (
            <div
              className={`absolute right-4 top-5 -translate-y-1/2 ${iconClassName}`}
            >
              {endIconSecondary && endIconSecondary}
              {endDecoration}
            </div>
          )}
        </div>
        {errors?.message && (
          <span className="absolute top-[100%] left-0 text-red-500 text-xs">
            {errors.message}
          </span>
        )}
      </label>
    );
  }
);

TextAreaInput.displayName = 'TextArea';
