'use client';

import { useEffect, ReactNode } from 'react';

import { useRouter, usePathname } from 'next/navigation';

import { canAccessSubscriptionStep } from '@/utils/subscription';

interface SubscriptionGuardProps {
  children: ReactNode;
}

/**
 * Guard component to protect subscription flow pages
 * Ensures users can only access subscription steps they're eligible for
 */
export default function SubscriptionGuard({
  children,
}: SubscriptionGuardProps) {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Temporarily disabled for testing - allow access to all subscription pages
    // Check if user can access current subscription step
    const canAccess = canAccessSubscriptionStep(pathname);
    console.log(
      'SubscriptionGuard - pathname:',
      pathname,
      'canAccess:',
      canAccess,
      '(GUARD DISABLED FOR TESTING)'
    );

    // Commented out redirect logic for testing
    // if (!canAccess) {
    //   // Redirect to the appropriate step
    //   const nextStep = getNextSubscriptionStep();
    //   console.log('SubscriptionGuard - Redirecting to:', nextStep);
    //   router.push(nextStep);
    // }
  }, [pathname, router]);

  // Always render children - the redirect will happen if needed
  return <>{children}</>;
}
