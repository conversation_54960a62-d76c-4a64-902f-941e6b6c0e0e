import React, { useState, useEffect } from 'react';

import { useSubscriptionStore } from '@/store/subscription';

import { Feature } from '@/utils/subscription';

import AppModal from '@/core/components/app-modal';

interface AddFeaturesModalProps {
  open: boolean;
  onClose: () => void;
  plan?: any; // The selected plan with add-on features
  currentPlanId?: string; // Current user plan ID
  subscriberData?: any; // Current subscriber data with active subscription
}

const AddFeaturesModal: React.FC<AddFeaturesModalProps> = ({
  open,
  onClose,
  plan,
  currentPlanId,
  subscriberData,
}) => {
  const {
    billingCycle,
    selectedAddOnFeatures,
    toggleAddOnFeature,
    setSelectedAddOnFeatures,
  } = useSubscriptionStore();

  // Check if current plan matches selected plan and get already subscribed features
  const getAlreadySubscribedFeatures = () => {
    if (!subscriberData?.activeSubscription || !currentPlanId || !plan?.id) {
      return [];
    }

    // Only consider features if the plans match
    if (currentPlanId === plan.id) {
      const activeAddOnFeatures =
        subscriberData.activeSubscription.addOnFeatures;
      if (!activeAddOnFeatures) return [];

      // Flatten all currently subscribed add-on features
      return [
        ...(activeAddOnFeatures.MRD || []),
        ...(activeAddOnFeatures.EMR || []),
        ...(activeAddOnFeatures.Billing || []),
      ];
    }

    return [];
  };

  const alreadySubscribedFeatures = getAlreadySubscribedFeatures();

  // Get all available add-on features from the plan
  const availableFeatures = plan?.apiPlan
    ? (Object.values(plan.apiPlan.addOnFeatures).flat() as Feature[])
    : [];

  // Get initial features for current plan from store
  const getInitialSelectedFeatures = () => {
    if (!availableFeatures.length) return [];
    return selectedAddOnFeatures.filter((selectedFeature) =>
      availableFeatures.some(
        (availableFeature) =>
          availableFeature.featureId === selectedFeature.featureId
      )
    );
  };

  // Local state for modal - initialize with current store data for this plan
  const [modalSelectedFeatures, setModalSelectedFeatures] = useState<Feature[]>(
    getInitialSelectedFeatures()
  );

  // Initialize modal state when modal opens
  useEffect(() => {
    if (open) {
      setModalSelectedFeatures(getInitialSelectedFeatures());
    }
  }, [open]); // Only depend on open state to avoid infinite loops

  const handleFeatureToggle = (feature: Feature) => {
    const isAlreadySelected = modalSelectedFeatures.some(
      (f) => f.featureId === feature.featureId
    );

    if (isAlreadySelected) {
      // Remove from modal state
      setModalSelectedFeatures((prev) =>
        prev.filter((f) => f.featureId !== feature.featureId)
      );
      // Also remove from store
      const updatedStoreFeatures = selectedAddOnFeatures.filter(
        (f) => f.featureId !== feature.featureId
      );
      setSelectedAddOnFeatures(updatedStoreFeatures);
    } else {
      // Add to modal state
      setModalSelectedFeatures((prev) => [...prev, feature]);
      // Also add to store
      const updatedStoreFeatures = [...selectedAddOnFeatures, feature];
      setSelectedAddOnFeatures(updatedStoreFeatures);
    }
  };

  // Check if feature is selected in modal state
  const isSelectedInModal = (feature: Feature) => {
    return modalSelectedFeatures.some((f) => f.featureId === feature.featureId);
  };

  // Check if feature is already subscribed in current plan
  const isAlreadySubscribed = (feature: Feature) => {
    return alreadySubscribedFeatures.some(
      (subscribedFeature) => subscribedFeature.featureId === feature.featureId
    );
  };

  return (
    <AppModal
      open={open}
      onClose={onClose}
      title="Add Features"
      classes={{
        root: 'w-[700px] max-h-[90vh]',
        body: 'p-8',
        backdrop: 'backdrop-blur-md bg-black/50',
      }}
    >
      <div className="h-[70vh] overflow-y-auto">
        {availableFeatures.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">
              No add-on features available for this plan.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-3 gap-4">
            {availableFeatures.map((feature) => {
              const isAdded = isSelectedInModal(feature);
              const isDisabled = isAlreadySubscribed(feature);
              const cost =
                billingCycle === 'monthly'
                  ? feature.monthlyAmount
                  : feature.yearlyAmount;
              const period = billingCycle === 'monthly' ? 'month' : 'year';

              return (
                <div
                  key={feature.featureId}
                  className={`relative border border-gray-200 rounded-lg p-4 pb-16 ${
                    isDisabled ? 'opacity-100' : ''
                  }`}
                >
                  <div>
                    <h4 className="text-[16px] font-bold text-[#012436] mb-1">
                      {feature.featureName}
                    </h4>
                    <div className="min-h-16 mb-4">
                      {feature.description && (
                        <p className="text-[13px] text-gray-600">
                          {feature.description}
                        </p>
                      )}
                    </div>
                    <div className="text-[14px] font-normal text-black">
                      ₹ {cost.toLocaleString()}/{period}
                    </div>
                  </div>

                  {/* Absolute positioned button at bottom */}
                  <div className="absolute bottom-4 left-4 right-4">
                    <button
                      onClick={() =>
                        !isDisabled && handleFeatureToggle(feature)
                      }
                      disabled={isDisabled}
                      className={`w-full py-2 px-4 rounded-md text-[14px] font-normal transition-colors ${
                        isDisabled
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : isAdded
                            ? 'bg-transparent text-black border border-black hover:bg-gray-50'
                            : 'bg-[#012436] text-white hover:bg-[#013547]'
                      }`}
                    >
                      {isDisabled
                        ? 'Already Subscribed'
                        : isAdded
                          ? 'Remove'
                          : 'Add to Cart'}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </AppModal>
  );
};

export default AddFeaturesModal;
