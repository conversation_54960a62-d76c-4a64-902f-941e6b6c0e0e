import * as React from 'react';

import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';

import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import {
  DatePicker as MuiDatePicker,
  DatePickerProps as MuiDatePickerProps,
} from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';

import InputLabel from '../input-label';

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the app date picker component instead.
 */
export type DatePickerProps = Omit<
  MuiDatePickerProps<Dayjs>,
  'onChange' | 'value'
> & {
  label?: string;
  name: string;
  control: Control<any>;
  error?: FieldError;
  renderEditIcon?: () => React.ReactNode;
  disableInput?: boolean;
  inputClassName?: string;
  disableManualInput?: boolean;
  rules?: RegisterOptions;
  isNotValid?: boolean;
  showHelperText?: boolean;
  onKeyDown?: (e: React.KeyboardEvent) => void;
};

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app date picker component instead.
 */
export default function DatePicker({
  label,
  name,
  control,
  error,
  slotProps,
  disabled,
  renderEditIcon,
  disableInput,
  inputClassName,
  disableManualInput,
  rules,
  showHelperText = true,
  onKeyDown,
  ...rest
}: DatePickerProps) {
  const slots: Record<string, any> = { ...((slotProps as any)?.slots || {}) };

  if (typeof renderEditIcon === 'function') {
    const customIcon = renderEditIcon();
    if (customIcon) {
      slots.openPickerIcon = () => customIcon;
    } else {
      if ('openPickerIcon' in slots) {
        delete slots.openPickerIcon;
      }
    }
  } else {
    if ('openPickerIcon' in slots) {
      delete slots.openPickerIcon;
    }
  }

  return (
    <label
      className={`relative flex flex-col text-[#001926] w-full ${inputClassName}`}
    >
      {label && <InputLabel label={label} />}
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Controller
          name={name}
          control={control}
          defaultValue={null}
          rules={rules}
          render={({ field, fieldState }) => (
            <>
              <MuiDatePicker
                {...field}
                {...rest}
                desktopModeMediaQuery="(min-width: 0px)"
                value={field.value ? dayjs(field.value) : null}
                onChange={(value) => {
                  const date = dayjs(value);

                  if (date.isValid()) {
                    field.onChange(date.toISOString());
                  } else {
                  }
                }}
                disabled={disabled}
                slots={slots}
                slotProps={{
                  ...slotProps,
                  textField: {
                    size: 'small',
                    variant: 'outlined',
                    disabled: disableInput,
                    error: !!fieldState.error || !!error,
                    helperText: showHelperText ? error?.message : undefined,
                    onKeyDown: onKeyDown,
                    inputProps: {
                      readOnly: disableManualInput ?? false,
                    },
                    sx: {
                      backgroundColor: disableInput ? '#fafafc' : undefined,
                      '& .MuiOutlinedInput-root': {
                        fontSize: { xs: '0.85rem', md: '1rem' },
                        '& .MuiInputBase-input': {
                          padding: { xs: '11px 14px', md: '8.5px 14px' },
                        },
                        '& fieldset': {
                          borderColor: '#637D92 !important',
                        },
                        '&:hover fieldset': {
                          borderColor: 'black !important',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'black !important',
                        },
                      },
                      '& .MuiOutlinedInput-input.Mui-disabled': {
                        opacity: 1,
                        color: 'black !important',
                        WebkitTextFillColor: 'black !important',
                      },
                      ...rest.sx,
                    },
                    ...slotProps?.textField,
                  },
                }}
              />
              {showHelperText &&
                (fieldState.error?.message || error?.message) && (
                  <span className="text-red-600 text-sm mt-1">
                    {fieldState.error?.message || error?.message}
                  </span>
                )}
            </>
          )}
        />
      </LocalizationProvider>
    </label>
  );
}
