import React, { FC, memo } from 'react';

import { Box, Grid2, Modal, ModalProps } from '@mui/material';

import AppIcon from '../app-icon';
import AppIconButton from '../app-icon-button';
import AppTitle from '../app-title';

type Props = ModalProps & {
  onClose?: () => void;
  title?: string;
  children: React.ReactNode;
  titleClass?: string;
  disableBackdropClick?: boolean;
  classes?: {
    root?: string;
    header?: string;
    body?: string;
    title?: string;
    closeButton?: string;
    backdrop?: string;
    modal?: string;
  };
};

const AppModal: FC<Props> = ({
  onClose,
  title,
  children,
  classes,
  titleClass = '',
  id = 'app-modal',
  disableBackdropClick = false,
  ...props
}) => {
  const handleModalClose = (
    event: {},
    reason: 'backdropClick' | 'escapeKeyDown'
  ) => {
    if (disableBackdropClick && reason === 'backdropClick') {
      return;
    }
    onClose?.();
  };

  return (
    <Modal
      onClose={handleModalClose}
      className={classes?.modal}
      classes={{ backdrop: classes?.backdrop }}
      aria-labelledby={id}
      id={id}
      {...props}
    >
      <Box
        className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white w-120 rounded-lg ${classes?.root}`}
      >
        {title && (
          <Grid2
            className={`flex justify-between items-center border-b-2 p-base ${classes?.header}`}
            container
            justifyContent="space-between"
            alignItems="center"
          >
            <AppTitle className={`${classes?.title || ''} ${titleClass}`}>
              {title}
            </AppTitle>
            <AppIconButton
              color="inherit"
              onClick={onClose}
              className={classes?.closeButton}
              variant="outlined"
            >
              <AppIcon icon="ic:round-close" />
            </AppIconButton>
          </Grid2>
        )}
        <Box className={`p-base ${classes?.body}`}>{children}</Box>
      </Box>
    </Modal>
  );
};

export default memo(AppModal);
