import { FC, useMemo } from 'react';

import { MdDragIndicator } from 'react-icons/md';

import { cn } from '@/lib/utils';

import { formatAddress } from '@/utils/mrd/manage-patient/format-address';
import { truncateString } from '@/utils/truncateText';

import {
  AppointmentStatus,
  Department,
  PatientQueueLabStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

import Avatar from '@/views/mrd/queue/queue-card/avatar';

import PatientStatusSelector from './PatientStatusSelector';

export type QueueCardProps = {
  name: string;
  age?: number | null;
  sex?: string | null;
  city?: string;
  state?: string;
  token?: number;
  type?: Department;
  status: AppointmentStatus;
  patientStatus?: PatientStatus;
  address?: string;
  labTestStatus?: PatientQueueLabStatus;
  onChangeStatus?: (status?: PatientStatus) => void;
  statusReadOnly?: boolean;
};

export default function QueueCard(props: QueueCardProps) {
  const details = [
    `${props.age} yrs`,
    props.sex && typeof props.sex === 'string'
      ? props.sex.charAt(0).toUpperCase()
      : '',
    props.address && truncateString(formatAddress(props.address)),
  ].filter((item) => !!item);

  function getOPIP() {
    if (props.type === Department.InPatient) {
      return 'IP';
    }

    return 'OP';
  }

  return (
    <div className="border border-[#DAE1E7] rounded-[5px] ">
      <div className="flex items-center justify-between gap-3 shadow-custom-xs py-2.5 px-2 pr-5  ">
        <div className="flex items-center gap-2 min-w-[60%]  ">
          <div className="flex items-center gap-2 ">
            <MdDragIndicator className="cursor-grab text-lg text-[#323F49]" />
            <Avatar
              name={props.name}
              status={props.status}
              patientStatus={props.patientStatus}
            />
          </div>

          <div className=" flex-grow flex flex-col  ">
            <span className="text-base capitalize font-medium -tracking-[2.2%]">
              {props.name}
            </span>
            <div className="flex items-center gap-1 text-xs font-light text-[#001926] whitespace-nowrap">
              {details.map((item, index) => (
                <div key={index} className="flex items-center gap-1">
                  <span>{item}</span>
                  {index < details.length - 1 && (
                    <span className="w-[4px] h-[4px] rounded-full bg-[#A5B2BD]" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="pl-6  border-l border-l-[#C2CDD6] max-w-[50%]">
          <PatientStatusSelector
            value={props.patientStatus}
            onChange={props.onChangeStatus}
            readOnly={props.statusReadOnly}
          />
        </div>
      </div>

      <div className="flex items-center gap-4 pt-2.5 pl-5 pb-2 pr-5">
        <DataBox label="Token" value={props.token} />
        <DataBox label="Type" value={getOPIP()} />
        {/* TODO: Do lab reports */}
        <StatusButton
          className="flex-grow"
          status={props.labTestStatus ?? PatientQueueLabStatus.NO_TEST_ORDERED}
        />
      </div>
    </div>
  );
}

type DataBoxProps = {
  value?: string | number;
  label: string | number;
};

function DataBox(props: DataBoxProps) {
  return (
    <div className="border border-black h-10 w-16 rounded-[6px] flex flex-col items-center justify-center">
      <div className="text-lg leading-none">{props.value || '--'}</div>
      <div className="text-[#637D92] text-[10px] leading-none">
        {props.label}
      </div>
    </div>
  );
}

const StatusButton: FC<{
  className?: string;
  status: PatientQueueLabStatus;
}> = (props) => {
  const statusClasses = useMemo(() => {
    switch (props.status) {
      case PatientQueueLabStatus.NO_TEST_ORDERED:
        return 'bg-[#C2CDD6]';
      case PatientQueueLabStatus.LAB_REPORT_AWAITING:
        return 'bg-[#FF9F2A]';
      case PatientQueueLabStatus.LAB_REPORT_READY:
        return 'bg-[#06C6A7]';
      case PatientQueueLabStatus.REGISTERED_NOT_PAID:
        return 'bg-[#E4626F]';
      default:
        return 'bg-[#C2CDD6]';
    }
  }, [props.status]);

  return (
    <div
      className={cn(
        props.className,
        statusClasses,
        `flex items-center justify-center rounded-xl border-none h-10 px-4 xl:px-3 py-2 xl:py-1 text-center whitespace-normal xl:whitespace-nowrap text-xs xl:text-sm`
      )}
    >
      {props.status}
    </div>
  );
};
