import React, { FC, memo, useCallback, useState, useEffect } from 'react';

import { useForm } from 'react-hook-form';

import { Box, Modal, IconButton } from '@mui/material';
import { AiFillFilePdf } from 'react-icons/ai';
import { FiUpload } from 'react-icons/fi';
import { IoCloseSharp } from 'react-icons/io5';
import { toast } from 'sonner';

import { cn } from '@/lib/utils';

import useFileUpload, {
  ACCEPTED_FILE_EXTENSIONS,
} from '@/hooks/use-file-upload';
import usePdf from '@/hooks/use-pdf';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import { previewTestResult } from '@/query/emr/lab';

import { DateFormats } from '@/utils/dateUtils/dateFormats';

import UnderlinedPencilIcon from '@/assets/svg/UnderlinedPencilIcon';

import OutLinedIconButton from '@/emr/components/lifestyle/lifestyle-forms/shared/OutlinedIconButton';

import AppIcon from '@/core/components/app-icon';
import DatePicker from '@/core/components/date-picker';
import PrimaryButton from '@/core/components/primary-button';

type Props = {
  open: boolean;
  onClose?: () => void;
  onUploadSuccess?: () => void;
  id: string;
  fileMetadata?: any[];
  status?: string;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const UploadFileModal: FC<Props> = ({
  open,
  onClose,
  onUploadSuccess,
  id,
  fileMetadata = [],
  status,
}) => {
  const { patient } = useCurrentPatientStore();
  const { control, getValues, setValue, reset } = useForm();
  const { previewPdf } = usePdf();
  const {
    selectedFiles,
    isDragging,
    isUploading,
    error,
    imagePreviews,
    fileInputRef,
    handleFileSelect,
    handleRemoveFileByIndex,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleClose,
    handleUpload: uploadFile,
  } = useFileUpload({
    onUploadSuccess,
    onClose,
    resetFormValues: () => reset(),
  });

  // Helper function to deduplicate files based on file ID
  const deduplicateFiles = (files: any[]) => {
    const seen = new Set();
    return files.filter((file) => {
      if (seen.has(file.id)) {
        return false;
      }
      seen.add(file.id);
      return true;
    });
  };

  // Track files to show (existing and new) - deduplicated
  const [existingFiles, setExistingFiles] = useState(() =>
    deduplicateFiles(fileMetadata)
  );
  // Track files to remove
  const [removedFileIds, setRemovedFileIds] = useState<string[]>([]);

  // Update existingFiles when fileMetadata changes
  useEffect(() => {
    setExistingFiles(deduplicateFiles(fileMetadata));
  }, [fileMetadata]);

  const handleUpload = async () => {
    if (
      !selectedFiles.length &&
      !removedFileIds.length &&
      !existingFiles.length
    )
      return;
    const date = getValues('date');

    // Create an array to store all file blobs
    const existingFileBlobs = await Promise.all(
      existingFiles.map(async (file) => {
        try {
          const blob = await previewTestResult(file.id);
          if (blob instanceof Blob) {
            return new File([blob], file.fileName, { type: blob.type });
          }
        } catch (error) {
          console.error(
            `Error fetching existing file ${file.fileName}:`,
            error
          );
        }
        return null;
      })
    );

    // Filter out any null values from failed fetches
    const validExistingFiles = existingFileBlobs.filter(
      (file): file is File => file !== null
    );

    await uploadFile({
      patientId: patient?.id ?? '',
      labTestId: id,
      date: date ?? new Date(),
      fileIdsToRemove: removedFileIds.length ? removedFileIds : undefined,
      existingFiles: validExistingFiles,
    });

    setValue('date', null);
    setRemovedFileIds([]);
  };

  const hasExistingFiles =
    (status === 'Uploaded' || status === 'Ready') &&
    existingFiles &&
    existingFiles.length > 0;

  const handleRemoveExistingFile = (fileId: string) => {
    setExistingFiles((prev) => prev.filter((f) => f.id !== fileId));
    setRemovedFileIds((prev) => [...prev, fileId]);
  };

  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
    });
  };

  const handlePreviewFile = async (file: any) => {
    try {
      const blob = await previewTestResult(file.id);

      if (blob instanceof Blob) {
        const fileType = file.fileType?.toLowerCase() || '';

        if (fileType.includes('pdf') || fileType === 'application/pdf') {
          const base64Data = await blobToBase64(blob);
          const byteCharacters = atob(base64Data.split(',')[1]);
          const byteNumbers = new Array(byteCharacters.length)
            .fill(0)
            .map((_, i) => byteCharacters.charCodeAt(i));
          const byteArray = new Uint8Array(byteNumbers);
          const fileBlob = new Blob([byteArray], { type: 'application/pdf' });
          const fileUrl = URL.createObjectURL(fileBlob);
          window.open(fileUrl, '_blank');
        } else if (fileType.startsWith('image/')) {
          const imageUrl = URL.createObjectURL(blob);
          window.open(imageUrl, '_blank');
        } else {
          const fileUrl = URL.createObjectURL(blob);
          window.open(fileUrl, '_blank');
        }
      }
    } catch (error) {
      console.error('Error previewing file:', error);
      toast.error('Failed to preview file');
    }
  };

  const FilesPreview = useCallback(() => {
    return (
      <div className="flex flex-row flex-wrap gap-2 w-full p-2 items-center">
        {hasExistingFiles &&
          existingFiles.map((file, idx) => (
            <div
              key={file.id || idx}
              className="relative flex flex-col items-center min-w-[120px] max-w-[180px] group"
            >
              <OutLinedIconButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveExistingFile(file.id);
                }}
                sx={{
                  position: 'absolute',
                  top: 2,
                  right: 10,
                  width: 20,
                  height: 20,
                  p: 0.3,
                  zIndex: 2,
                }}
              >
                <IoCloseSharp size={22} />
              </OutLinedIconButton>

              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  handlePreviewFile(file);
                }}
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: 32,
                  height: 32,
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  opacity: 0,
                  transition: 'opacity 0.2s ease-in-out',
                  zIndex: 1,
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  },
                  '.group:hover &': {
                    opacity: 1,
                  },
                }}
              >
                <AppIcon icon="mdi:eye" fontSize="small" />
              </IconButton>

              <div className="flex flex-col items-center justify-center mb-2 text-red-500">
                <AiFillFilePdf size={32} />
                <span className="text-xs text-center text-gray-500 break-words whitespace-normal leading-tight px-1">
                  {file.fileName}
                </span>
              </div>
            </div>
          ))}
        {/* Show newly selected files */}
        {selectedFiles.map((file, idx) => {
          let filePreview: React.ReactNode = null;
          if (imagePreviews[idx]) {
            filePreview = (
              <div className="mb-2 w-[80px] h-[60px] flex items-center justify-center overflow-hidden shadow-base bg-gray-300">
                <img
                  src={imagePreviews[idx] || ''}
                  alt="Preview"
                  className="max-w-full max-h-full object-contain rounded"
                  style={{ width: 'auto', height: 'auto' }}
                />
              </div>
            );
          } else if (file.type === 'application/pdf') {
            filePreview = (
              <div className="flex flex-col items-center justify-center mb-2 text-red-500">
                <AiFillFilePdf size={32} />
                <span className="text-xs text-center text-gray-500 break-words whitespace-normal leading-tight px-1">
                  {file.name}
                </span>
              </div>
            );
          }
          return (
            <div
              key={idx}
              className="relative flex flex-col items-center min-w-[120px] max-w-[180px]"
            >
              <OutLinedIconButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveFileByIndex(idx);
                }}
                sx={{
                  position: 'absolute',
                  top: 2,
                  right: 10,
                  width: 20,
                  height: 20,
                  p: 0.3,
                }}
              >
                <IoCloseSharp size={22} />
              </OutLinedIconButton>
              {filePreview}
            </div>
          );
        })}
      </div>
    );
  }, [
    selectedFiles,
    imagePreviews,
    handleRemoveFileByIndex,
    existingFiles,
    hasExistingFiles,
  ]);

  const UploadPrompt = () => (
    <div className="absolute inset-0 flex flex-col w-full h-full items-center justify-center p-4 pointer-events-none select-none z-0">
      <FiUpload fontSize={40} color="#ccc" />
      <span className="text-[#ccc] italic font-thin mt-2 text-center">
        Click here or drop files to upload
      </span>
      {error && <div className="text-red-500 text-sm mt-2">{error}</div>}
    </div>
  );

  return (
    <Modal open={open} onClose={handleClose}>
      <Box
        className={cn(
          'absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]',
          'w-[32%] min-h-[65vh] max-h-[90vh] overflow-auto',
          'flex flex-col bg-white rounded-md shadow-md p-4 justify-between gap-2 cursor-pointer'
        )}
      >
        <div className="flex justify-between items-center h-10">
          <div className="flex items-center flex-1 w-full justify-center">
            <span className="font-medium text-center text-lg">
              Upload Results
            </span>
          </div>
          <OutLinedIconButton onClick={handleClose}>
            <IoCloseSharp />
          </OutLinedIconButton>
        </div>
        <DatePicker
          control={control}
          name="date"
          renderEditIcon={() => (
            <UnderlinedPencilIcon style={{ color: '#ccc !important' }} />
          )}
          sx={{ '& .MuiOutlinedInput-input': { p: 1, borderRadius: 3 } }}
          format={DATE_DD_MM_YYYY_SLASH}
        />
        <div
          className={cn(
            'relative flex-1 w-full h-full border-2 rounded-md border-dashed flex items-stretch',
            {
              'bg-gray-100': isDragging,
              'border-red-500': error,
              'cursor-pointer': true,
            }
          )}
          onClick={() => {
            fileInputRef.current?.click();
          }}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          role="button"
          tabIndex={0}
          style={{ minHeight: 180 }}
        >
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            multiple
            onChange={(e) => {
              if (e.target.files && e.target.files.length > 0) {
                handleFileSelect(e.target.files);
              }
            }}
            accept={ACCEPTED_FILE_EXTENSIONS}
          />
          {/* Always show the upload prompt in the background */}
          <UploadPrompt />
          {/* Overlay previews and plus button */}
          <div className="relative z-10 w-full max-h-60 overflow-auto">
            <FilesPreview />
          </div>
        </div>
        <PrimaryButton
          disabled={!selectedFiles.length || isUploading}
          onClick={handleUpload}
          isLoading={isUploading}
          className="min-h-10 disabled:bg-[#ccc]"
        >
          Upload
        </PrimaryButton>
      </Box>
    </Modal>
  );
};

export default memo(UploadFileModal);
