import React from 'react';

/**
 * Utility function to safely render HTML content with proper styling for bullet points and other elements.
 * This function processes HTML strings and adds inline styles to ensure consistent rendering across components.
 * For organization plans, it includes hover support to make text white on hover.
 *
 * @param htmlContent - The HTML content string to render
 * @param isOrganizationPlan - Whether this is for an organization plan (enables hover support)
 * @returns JSX element with the processed HTML content
 */
export const renderHTMLContent = (
  htmlContent: string,
  isOrganizationPlan: boolean = false
): JSX.Element => {
  // Process the HTML to ensure bullet points and other elements are properly styled
  const processedContent = htmlContent
    // List styling for unordered lists
    .replace(
      /<ul>/g,
      `<ul style="list-style-type: disc !important; margin-left: 20px !important; padding-left: 0 !important;${isOrganizationPlan ? ' color: inherit !important;' : ''}">`
    )
    // List styling for ordered lists
    .replace(
      /<ol>/g,
      `<ol style="list-style-type: decimal !important; margin-left: 20px !important; padding-left: 0 !important;${isOrganizationPlan ? ' color: inherit !important;' : ''}">`
    )
    // List item styling
    .replace(
      /<li[^>]*>/g,
      `<li style="margin-bottom: 8px !important; line-height: 1.5 !important; display: list-item !important;${isOrganizationPlan ? ' color: inherit !important;' : ''}">`
    )
    // Heading 1 styling
    .replace(
      /<h1[^>]*>/g,
      `<h1 style="font-size: 18px !important; font-weight: bold !important; margin-bottom: 16px !important;${isOrganizationPlan ? ' color: inherit !important;' : ' color: #231D4F !important;'}">`
    )
    // Heading 2 styling
    .replace(
      /<h2[^>]*>/g,
      `<h2 style="font-size: 16px !important; font-weight: bold !important; margin-bottom: 12px !important;${isOrganizationPlan ? ' color: inherit !important;' : ' color: #231D4F !important;'}">`
    )
    // Heading 3 styling
    .replace(
      /<h3[^>]*>/g,
      `<h3 style="font-size: 14px !important; font-weight: bold !important; margin-bottom: 10px !important;${isOrganizationPlan ? ' color: inherit !important;' : ' color: #231D4F !important;'}">`
    )
    // Paragraph styling
    .replace(
      /<p[^>]*>/g,
      `<p style="margin-bottom: 12px !important; line-height: 1.6 !important;${isOrganizationPlan ? ' color: inherit !important;' : ' color: #001926 !important;'}">`
    )
    // Strong text styling
    .replace(
      /<strong>/g,
      `<strong style="font-weight: 700 !important;${isOrganizationPlan ? ' color: inherit !important;' : ''}">`
    )
    // Add color inheritance to all other elements
    .replace(/<([^>]+)>/g, (match, tag) => {
      // Skip already processed tags and self-closing tags
      if (
        match.includes('style=') ||
        match.includes('/>') ||
        tag.startsWith('!--')
      ) {
        return match;
      }
      // Add color inheritance for organization plans
      return isOrganizationPlan
        ? match.replace(/>/, ` style="color: inherit !important;">`)
        : match;
    });

  return React.createElement('div', {
    dangerouslySetInnerHTML: { __html: processedContent },
  });
};

export default renderHTMLContent;
