export const PERMISSION_KEYS = {
  // Module Access Permissions
  EMR_ACCESS: 'emr.access',
  MRD_ACCESS: 'mrd.access',

  EMR_DASHBOARD_VIEW: 'emr.dashboard.view',
  MRD_DASHBOARD_VIEW: 'mrd.dashboard.view',

  EMR_PATIENT_INFO_VIEW: 'emr.patientinfo.view',
  EMR_PATIENT_INFO_EDIT: 'emr.patientinfo.edit',
  EMR_CONSULTATION_MANAGE: 'emr.consultation.manage',
  EMR_REPORTS_MANAGE: 'emr.reports.manage',
  EMR_PRESCRIPTION_VIEW: 'emr.prescription.view',
  EMR_PRESCRIPTION_MANAGE: 'emr.prescription.manage',
  EMR_MEDICINE_PACKAGE_MANAGE: 'emr.medicine-package.manage',
  EMR_TEST_PACKAGE_MANAGE: 'emr.test-package.manage',
  EMR_DOCTOR_PROFILE_VIEW: 'emr.doctorprofile.view',
  EMR_DOCTOR_PROFILE_EDIT: 'emr.doctorprofile.edit',

  // MRD Module Permissions
  MRD_MANAGE_PATIENT_VIEW: 'mrd.manage-patient.view',
  MRD_MANAGE_PATIENT_EDIT: 'mrd.manage-patient.edit',
  MRD_PATIENT_QUEUE_MANAGE: 'mrd.patient-queue.manage',

  PAYMENT_CREATE: 'payment.create',
  MRD_PAYMENT_PATIENT_REGISTRATION: 'mrd.payment.patient_registration',
  EMR_PAYMENT_APPOINTMENT_BOOKING: 'emr.payment.appointment_booking',
  EMR_PAYMENT_PRESCRIPTION: 'emr.payment.prescription',
  EMR_PAYMENT_LAB_TEST: 'emr.payment.lab_test',
} as const;

// Type for TypeScript type checking
export type PermissionKey =
  (typeof PERMISSION_KEYS)[keyof typeof PERMISSION_KEYS];

// Helper function to check if a string is a valid permission key
export function isPermissionKey(key: string): key is PermissionKey {
  return Object.values(PERMISSION_KEYS).includes(key as PermissionKey);
}
