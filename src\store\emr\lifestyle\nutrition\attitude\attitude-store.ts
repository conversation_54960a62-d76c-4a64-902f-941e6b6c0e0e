import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createLifestyleData,
  getLifestyleQuestions,
  getPatientLifestyle,
  updateLifestyleData,
} from '@/query/emr/lifestyle';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import {
  FieldGroup,
  Questionnaire,
  QuestionnaireResponse,
} from '@/types/emr/lifestyle/questionnaire';

import { useLifestyleFilterStore } from '../../filter-store';

type AttitudeState = {
  questions: Questionnaire;
  questionLoading: boolean;
  updating: boolean;
  patientData: QuestionnaireResponse[];
  loading: boolean;
  finalizing: boolean;
};

type AttitudeActions = {
  getLifestyleQuestions: () => Promise<void>;
  createLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  updateLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  finalizeRecord: (id: string) => Promise<void>;
  getPatientData: (
    fromDate?: string,
    toDate?: string,
    silent?: boolean
  ) => Promise<void>;
  refreshData: () => void;
};

export type AttitudeStore = AttitudeState & AttitudeActions;

const defaultQuestions = {
  source: LifestyleSources.NUTRITION_ATTITUDE,
  questions: [],
};

const initialState: AttitudeState = {
  questions: defaultQuestions,
  questionLoading: false,
  updating: false,
  patientData: [],
  loading: false,
  finalizing: false,
};

export const nutritionAttitudeStore = create<AttitudeStore>((set, get) => ({
  ...initialState,
  getLifestyleQuestions: async () => {
    try {
      set({ questionLoading: true });

      const data = await getLifestyleQuestions(
        LifestyleSources.NUTRITION_ATTITUDE
      );

      set({ questions: data });
    } catch (error) {
      console.error('Error fetching nutrition attitude questions:', error);
      toast.error(
        'Failed to load nutrition attitude questionnaire. Please try again.'
      );

      set({ questions: defaultQuestions });
    } finally {
      set({ questionLoading: false });
    }
  },

  createLifestyleData: async (data: Record<string, unknown>) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const hasSubmissionToday = get().patientData.some(
        (item) => item.created_on && item.created_on.startsWith(today)
      );

      if (hasSubmissionToday) {
        toast.error(
          'You have already submitted a nutrition attitude record today'
        );
        return;
      }

      set({ updating: true });
      const payload = {
        ...data,
        source: LifestyleSources.NUTRITION_ATTITUDE,
      };
      await createLifestyleData(payload);
      toast.success('Nutrition attitude record created successfully');
      get().refreshData();
    } catch (error) {
      console.error('Error creating nutrition attitude data:', error);
      toast.error('Failed to create nutrition attitude record');
    } finally {
      set({ updating: false });
    }
  },

  updateLifestyleData: async (data: Record<string, unknown>) => {
    try {
      set({ updating: true });

      const {
        questions,
        created_on,
        updated_on,
        create_by,
        update_by,
        _attachments,
        _etag,
        _rid,
        _self,
        _ts,
        ...formData
      } = data;

      const payload = {
        ...formData,
        source: LifestyleSources.NUTRITION_ATTITUDE,
        questions: (questions || data.questions) as FieldGroup[],
      };

      await updateLifestyleData(payload, data.id as string);
      toast.success('Nutrition attitude record updated successfully');
      get().refreshData();
    } catch (error) {
      console.error('Error updating nutrition attitude data:', error);
      toast.error('Failed to update nutrition attitude record');
    } finally {
      set({ updating: false });
    }
  },

  finalizeRecord: async (id: string) => {
    try {
      set({ finalizing: true });
      const record = get().patientData.find((item) => item.id === id);
      if (record) {
        const payload = {
          ...record,
          status: LifestyleRecordStatus.FINALIZED,
        };
        await updateLifestyleData(payload, id);
        toast.success('Nutrition attitude record finalized successfully');
        get().refreshData();
      }
    } catch (error) {
      console.error('Error finalizing nutrition attitude record:', error);
      toast.error('Failed to finalize nutrition attitude record');
    } finally {
      set({ finalizing: false });
    }
  },

  getPatientData: async (fromDate, toDate, silent = false) => {
    try {
      set({ loading: !silent });
      const data = await getPatientLifestyle(
        LifestyleSources.NUTRITION_ATTITUDE,
        fromDate,
        toDate
      );
      set({ patientData: data || [] });
    } catch (error) {
      console.error('Error fetching nutrition attitude patient data:', error);
      set({ patientData: [] });
    } finally {
      set({ loading: false });
    }
  },

  refreshData: () => {
    const { getPatientData } = get();
    const { fromDate, toDate } = useLifestyleFilterStore.getState();
    getPatientData(fromDate, toDate, true);
  },
}));
