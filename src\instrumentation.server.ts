﻿// Server-only instrumentation file
// This file contains the actual telemetry setup logic

export async function initializeTelemetry() {
  const serviceName = process.env.SERVICE_NAME || 'arca-emr';

  // Detect environment based on available configuration
  const isVercel = !!process.env.VERCEL;
  const isAzure = !!process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;
  const hasGrafanaConfig = !!(
    process.env.GRAFANA_OTLP_ENDPOINT &&
    process.env.GRAFANA_USER &&
    process.env.GRAFANA_API_KEY
  );

  console.log('🚀 Initializing OpenTelemetry...');
  console.log(
    `📍 Deployment context: Vercel=${isVercel}, Azure=${isAzure}, Grafana=${hasGrafanaConfig}`
  );

  let traceExporter: any;
  let telemetryProvider: string;

  try {
    // Priority 1: Azure Application Insights (Production)
    if (isAzure) {
      const connectionString =
        process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;

      console.log('🔵 Using Azure Application Insights Exporter');
      telemetryProvider = 'Azure Application Insights';

      // Dynamic import to avoid webpack issues
      const { AzureMonitorTraceExporter } = await import(
        /* webpackIgnore: true */ '@azure/monitor-opentelemetry-exporter'
      );
      traceExporter = new AzureMonitorTraceExporter({
        connectionString,
      });
    }
    // Priority 2: Grafana Cloud OTLP (QA/Staging)
    else if (hasGrafanaConfig) {
      const otlpEndpoint = process.env.GRAFANA_OTLP_ENDPOINT;
      const grafanaUser = process.env.GRAFANA_USER;
      const grafanaApiKey = process.env.GRAFANA_API_KEY;

      console.log('🟢 Using Grafana Cloud OTLP Exporter');
      telemetryProvider = 'Grafana Cloud';

      // Dynamic import to avoid webpack issues
      const { OTLPTraceExporter } = await import(
        /* webpackIgnore: true */ '@opentelemetry/exporter-trace-otlp-http'
      );
      traceExporter = new OTLPTraceExporter({
        url: otlpEndpoint,
        headers: {
          Authorization: `Basic ${btoa(`${grafanaUser}:${grafanaApiKey}`)}`,
          'Content-Type': 'application/json',
        },
      });
    }
    // No telemetry configuration found
    else {
      console.log('⚪ No telemetry configuration detected');
      console.log('💡 To enable telemetry:');
      console.log('   - For Azure: Set APPLICATIONINSIGHTS_CONNECTION_STRING');
      console.log(
        '   - For Grafana: Set GRAFANA_OTLP_ENDPOINT, GRAFANA_USER, GRAFANA_API_KEY'
      );
      return;
    }

    // Register OpenTelemetry with the appropriate exporter
    const { registerOTel } = await import('@vercel/otel');
    registerOTel({
      serviceName,
      traceExporter,
    });

    console.log(
      `✅ OpenTelemetry successfully initialized for ${serviceName} using ${telemetryProvider}`
    );
  } catch (error) {
    console.error('❌ Failed to initialize OpenTelemetry:', error);
    // Don't throw - we don't want telemetry issues to break the app
  }
}
