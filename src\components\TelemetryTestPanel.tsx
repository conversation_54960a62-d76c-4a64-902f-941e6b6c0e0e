'use client';

import React from 'react';

import {
  createTestTrace,
  createNestedTestTrace,
  createErrorTestTrace,
  checkTelemetryStatus,
} from '@/lib/telemetry-test';

/**
 * Development/Testing component to verify OpenTelemetry integration
 * Only render this in development environments
 */
export default function TelemetryTestPanel() {
  // Detect if we're in a production-like environment
  const hasAzureConfig = !!process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;
  const hasGrafanaConfig = !!(
    process.env.GRAFANA_OTLP_ENDPOINT &&
    process.env.GRAFANA_USER &&
    process.env.GRAFANA_API_KEY
  );

  // Don't render if telemetry is configured (likely production/staging)
  if (hasAzureConfig || hasGrafanaConfig) {
    return null;
  }

  const handleSimpleTrace = () => {
    createTestTrace('user-clicked-simple-test');
  };

  const handleNestedTrace = () => {
    createNestedTestTrace();
  };

  const handleErrorTrace = () => {
    createErrorTestTrace();
  };

  const handleStatusCheck = () => {
    checkTelemetryStatus();
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm">
      <h3 className="text-lg font-semibold mb-3 text-gray-800">
        🔍 Telemetry Test Panel
      </h3>

      <div className="text-sm text-gray-600 mb-3">
        Mode:{' '}
        <span className="font-mono bg-gray-100 px-1 rounded">Development</span>
      </div>

      <div className="space-y-2">
        <button
          onClick={handleStatusCheck}
          className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Check Status
        </button>

        <button
          onClick={handleSimpleTrace}
          className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          Simple Trace
        </button>

        <button
          onClick={handleNestedTrace}
          className="w-full px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
        >
          Nested Trace
        </button>

        <button
          onClick={handleErrorTrace}
          className="w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Error Trace
        </button>
      </div>

      <div className="mt-3 text-xs text-gray-500">
        Check browser console for logs
      </div>
    </div>
  );
}
