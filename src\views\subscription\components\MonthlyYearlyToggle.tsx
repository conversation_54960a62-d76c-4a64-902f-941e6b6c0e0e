'use client';

interface MonthlyYearlyToggleProps {
  billingCycle: 'monthly' | 'yearly';
  onBillingCycleChange: (cycle: 'monthly' | 'yearly') => void;
  className?: string;
}

const MonthlyYearlyToggle = ({
  billingCycle,
  onBillingCycleChange,
  className = '',
}: MonthlyYearlyToggleProps) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="bg-white rounded-full p-1 shadow-sm border">
        <button
          onClick={() => onBillingCycleChange('monthly')}
          className={`px-6 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
            billingCycle === 'monthly'
              ? 'bg-gray-900 text-white'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Monthly
        </button>
        <button
          onClick={() => onBillingCycleChange('yearly')}
          className={`px-6 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
            billingCycle === 'yearly'
              ? 'bg-gray-900 text-white'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Yearly
        </button>
      </div>
    </div>
  );
};

export default MonthlyYearlyToggle;
