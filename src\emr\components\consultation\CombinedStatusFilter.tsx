import React, { useEffect, useRef, useState } from 'react';

import { cn } from '@/lib/utils';

type CombinedStatusOption = {
  label: string;
  value: string;
  diagnosisStatus: 'provisional' | 'confirmed' | 'all';
  activityStatus: 'active' | 'inactive' | 'all';
};

type CombinedStatusFilterProps = {
  value?: string;
  onChange?: (option: CombinedStatusOption) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
};

export default function CombinedStatusFilter({
  value,
  onChange,
  disabled = false,
  placeholder = 'All Status',
  className,
}: CombinedStatusFilterProps) {
  const options: CombinedStatusOption[] = [
    {
      label: 'All Status',
      value: 'all',
      diagnosisStatus: 'all',
      activityStatus: 'all',
    },
    {
      label: 'Provisional',
      value: 'provisional',
      diagnosisStatus: 'provisional',
      activityStatus: 'all',
    },
    {
      label: 'Confirmed',
      value: 'confirmed',
      diagnosisStatus: 'confirmed',
      activityStatus: 'all',
    },
    {
      label: 'Active',
      value: 'active',
      diagnosisStatus: 'all',
      activityStatus: 'active',
    },
    {
      label: 'Inactive',
      value: 'inactive',
      diagnosisStatus: 'all',
      activityStatus: 'inactive',
    },
  ];

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleChange = (newValue: string) => {
    const selectedOption = options.find((opt) => opt.value === newValue);

    if (selectedOption && onChange) {
      onChange(selectedOption);
    }
  };

  const handleOptionClick = (optionValue: string) => {
    handleChange(optionValue);
    setIsOpen(false);
  };

  const displayText = () => {
    const selectedOption = options.find((opt) => opt.value === value);
    return selectedOption ? selectedOption.label : placeholder;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={cn(
          'h-8 border border-black rounded-lg px-2 text-sm w-[160px] bg-transparent text-black cursor-pointer hover:bg-gray-50 flex items-center justify-between',
          disabled && 'cursor-not-allowed opacity-50',
          className
        )}
        disabled={disabled}
      >
        <span>{displayText()}</span>
        <svg
          className="w-4 h-4 ml-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 rounded-lg min-w-[160px] bg-white shadow-lg border border-gray-200 z-[99999]">
          {options.map((option) => (
            <div
              key={option.value}
              onClick={() => handleOptionClick(option.value)}
              className="h-8 text-sm cursor-pointer hover:bg-gray-100 px-2 py-1 first:rounded-t-lg last:rounded-b-lg text-black"
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
