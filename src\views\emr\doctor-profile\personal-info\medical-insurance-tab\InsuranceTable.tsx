import React, { FC, memo, useMemo } from 'react';

import {
  Control,
  FieldArrayWithId,
  FieldErrors,
  useWatch,
} from 'react-hook-form';

import {
  useEnterKeyNavigation,
  createArrayFieldNavigation,
} from '@/hooks/useEnterKeyNavigation';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { getMinMaxDate } from '@/utils/emr/doctor-profile/personal-info';
import {
  allowAlphanumericInput,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import { medicalInsuranceHeaders } from '@/constants/emr/doctor-profile/personal-info';

import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import {
  actionButtonCellProps,
  getInputCellProps,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import ActionButton from '../shared/ActionButton';
import TableDatePicker from '../shared/TableDatePicker';
import TableTextarea from '../shared/TableTextarea';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'insurance', 'id'>[];
  control: Control<FormData>;
  handleItemEdit: (_index: number) => () => void;
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
  itemToEdit: number | null;
  error?: FieldErrors<FormData>;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const InsuranceTable: FC<Props> = ({
  fields,
  control,
  itemToEdit,
  handleItemEdit,
  handleOnDelete,
  error,
}) => {
  // Create field order for all insurance entries
  const allFieldOrder = useMemo(() => {
    const allFields: string[] = [];
    fields.forEach((_, index) => {
      const baseFieldOrder = [
        'policyName',
        'policyNumber',
        'validFrom',
        'validTo',
      ];
      const fieldOrder = createArrayFieldNavigation(
        baseFieldOrder,
        index,
        'insurance'
      );
      allFields.push(...fieldOrder);
    });
    return allFields;
  }, [fields.length]);

  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder: allFieldOrder,
    shouldTriggerSave: (fieldName: string) => {
      // Trigger save on the last field (validTo) of the last insurance
      return (
        fieldName.includes('validTo') &&
        fieldName.includes(`${fields.length - 1}`)
      );
    },
  });
  const watchedInsurances = useWatch({
    control,
    name: 'insurance',
  });

  const rows = useMemo<Row[]>(
    () =>
      fields?.map((field, index) => ({
        key: field.id,
        policyName: {
          value: (
            <TableTextarea
              name={`insurance.${index}.policyName`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `insurance.${index}.policyName`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        policyNumber: {
          value: (
            <TableTextarea
              name={`insurance.${index}.policyNumber`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                allowAlphanumericInput(e);
                handleKeyDown(e, `insurance.${index}.policyNumber`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        from: {
          value: (
            <TableDatePicker
              name={`insurance.${index}.validFrom`}
              control={control}
              disabled={itemToEdit !== index}
              format={DATE_DD_MM_YYYY_SLASH}
              maxDate={getMinMaxDate(watchedInsurances[index]?.validTo)}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        to: {
          value: (
            <TableDatePicker
              name={`insurance.${index}.validTo`}
              control={control}
              disabled={itemToEdit !== index}
              format={DATE_DD_MM_YYYY_SLASH}
              minDate={getMinMaxDate(watchedInsurances[index]?.validFrom)}
              isNotValid={!!error?.insurance?.[index]?.validTo?.message}
            />
          ),
          cellProps: getInputCellProps(
            itemToEdit !== index,
            !!error?.insurance?.[index]?.validTo?.message
          ),
        },
        status: {
          value: (
            <TableTextarea
              name={`insurance.${index}.status`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        edit: {
          value:
            itemToEdit !== index ? (
              <ActionButton actionFor="edit" onClick={handleItemEdit(index)} />
            ) : (
              <></>
            ),
          cellProps: actionButtonCellProps,
        },
        delete: {
          value: (
            <ActionButton
              actionFor="delete"
              onClick={() => handleOnDelete({ index, uuId: field?.uuId })}
            />
          ),
          cellProps: actionButtonCellProps,
        },
      })),
    [
      fields,
      control,
      handleOnDelete,
      itemToEdit,
      handleItemEdit,
      watchedInsurances,
      error?.insurance,
      handleKeyDown,
    ]
  );

  return (
    <Table
      headers={medicalInsuranceHeaders}
      rows={rows}
      tableContainerProps={{
        sx: {
          '& tbody td': {
            minHeight: 30,
          },
        },
      }}
    />
  );
};

export default memo(InsuranceTable);
