import { FC, useEffect, useState } from 'react';

import { ItemInterface, ReactSortable, SortableEvent } from 'react-sortablejs';

import { toast } from 'sonner';

import Loading from '@/lib/common/loading';

import { useDoctorStore } from '@/store/mrd/queue/doctor';

import { GetPatientQueueRes, QueueItem } from '@/query/patient';

import * as queueService from '@/utils/mrd/queue/queue';

import { PatientStatus } from '@/constants/mrd/manage-patient/consultation';

import QueueCard from '@/views/mrd/queue/queue-card';

interface QueueListProps {}

type SortableItem = QueueItem & ItemInterface;

const QueueList: FC<QueueListProps> = () => {
  const {
    selectedAppointments,
    consultingQueueItem: consultingAppointment,
    appointmentsByDoctor,
    isQueueLoading,
    selectedDoctor,
    loadSelectedDoctorAppointments,
  } = useDoctorStore();

  const [localQueueItems, setLocalQueueItems] = useState<SortableItem[]>([]);

  async function handleChangePatientStatus(
    item: QueueItem,
    status: PatientStatus | undefined
  ) {
    try {
      if (!status || !selectedDoctor) {
        return;
      }

      await queueService.changeQueueItemStatus({
        doctorId: selectedDoctor.id,
        queueId: item.queueId,
        status: item.status,
        patientStatus: status,
      });

      toast.success('Patient status updated');
    } catch (err) {
      console.error(err);

      toast.error('Error updating status');
    }
  }

  async function handleSort(evt: SortableEvent) {
    try {
      if (
        !selectedDoctor ||
        !(typeof evt.oldIndex === 'number' && typeof evt.newIndex === 'number')
      ) {
        return;
      }

      const newItems = [...localQueueItems];
      const [removed] = newItems.splice(evt.oldIndex, 1);
      newItems.splice(evt.newIndex, 0, removed);

      await queueService.reorderQueue({
        doctorId: selectedDoctor.id,
        appointment: {
          ...selectedAppointments[0],
          queues: newItems.map((item) => ({
            ...item,
            id: undefined,
          })),
        },
      });

      useDoctorStore.setState((state) => ({
        ...state,
        consultingQueueItem: state.consultingQueueItem || consultingAppointment,
      }));

      toast.success('Reordered queue');
    } catch (err) {
      console.error(err);
      toast.error('Ordering queue failed');
    }
  }

  function updateLocalQueue(appointments: GetPatientQueueRes[]) {
    const newList: SortableItem[] = [];

    for (const appointment of appointments) {
      for (const item of appointment.queues) {
        newList.push({
          ...item,
          id: item.queueId,
        });
      }
    }

    setLocalQueueItems(newList);
  }

  useEffect(() => {
    loadSelectedDoctorAppointments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appointmentsByDoctor, consultingAppointment]);

  useEffect(() => {
    updateLocalQueue(selectedAppointments);
  }, [selectedAppointments]);

  if (isQueueLoading) {
    return <Loading />;
  }

  if (!selectedAppointments.length) {
    return (
      <div className="flex justify-center text-gray-400">
        No appointments for the selected
      </div>
    );
  }

  return (
    <ReactSortable
      className="flex flex-col gap-2 hover:cursor-grab active:cursor-grabbing"
      list={localQueueItems}
      setList={setLocalQueueItems}
      onEnd={handleSort}
      animation={200}
    >
      {localQueueItems.map((item) => (
        <QueueCard
          key={item.queueId}
          name={item.patient.name}
          age={item.patient.age}
          sex={item.patient.sex}
          token={item.queuePosition}
          status={item.status}
          patientStatus={item.patientStatus}
          type={item.department}
          city={item.patient.city}
          state={item.patient.state}
          address={item.patient.address}
          labTestStatus={item.labTestStatus}
          onChangeStatus={(status) => handleChangePatientStatus(item, status)}
        />
      ))}
    </ReactSortable>
  );
};

export default QueueList;
