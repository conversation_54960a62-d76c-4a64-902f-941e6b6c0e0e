import {
  OrganizationPlan,
  SubscriptionPlansResponse,
  QuoteRequest,
} from '@/utils/subscription';

import { publicApi, api } from '@/core/lib/interceptor';

/**
 * Get organization custom plan details
 */
export const getOrganizationPlan = async (): Promise<OrganizationPlan> => {
  const response = await publicApi.get<OrganizationPlan>(
    '/subscription/v0.1/organization-plan'
  );
  return response.data;
};

/**
 * Get all subscription plans
 */
export const getSubscriptionPlans = async (
  includeInactive: boolean = true
): Promise<SubscriptionPlansResponse> => {
  const response = await publicApi.get<SubscriptionPlansResponse>(
    `/subscription/v0.1/subscription-plan?includeInactive=${includeInactive}`
  );
  return response.data;
};

/**
 * Submit quote request
 */
export const submitQuoteRequest = async (
  data: QuoteRequest
): Promise<{ success: boolean; message: string }> => {
  const response = await publicApi.post<{ success: boolean; message: string }>(
    '/subscription/v0.1/subscription/quote',
    data
  );
  return response.data;
};

/**
 * Calculate prorated amount for plan upgrade
 */
export const calculateProratedAmount = async (data: {
  currentPlanId: string;
  newPlanId: string;
  billingPeriod: 'monthly' | 'yearly';
  currentPlanStartDate: string;
}): Promise<{
  proratedAmount: number;
  currentPlanRefund: number;
  newPlanAmount: number;
  finalAmount: number;
}> => {
  const response = await publicApi.post<{
    proratedAmount: number;
    currentPlanRefund: number;
    newPlanAmount: number;
    finalAmount: number;
  }>('/subscription/v0.1/calculate-prorated', data);
  return response.data;
};

/**
 * Start trial for free plan
 */
export const startTrial = async (data: {
  email: string;
  name: string;
  phoneNumber?: string | null;
  planId: string;
  billingType: 'monthly' | 'yearly';
  roleId: string;
  userType: string;
  userRole: string;
  Billing: Array<{
    name: string;
    email: string;
    pincode: string;
  }>;
}): Promise<{ success: boolean; message: string; subscription?: any }> => {
  const response = await publicApi.post<any>(
    '/subscription/v0.1/clinic/subscription?action=start-trial',
    data
  );

  // Check if response contains subscription data (indicates success)
  if (response.data?.subscription?.id) {
    return {
      success: true,
      message: 'Trial started successfully',
      subscription: response.data.subscription,
    };
  }

  // Check for success field in response
  if (response.data?.success !== false) {
    return {
      success: true,
      message: 'Trial started successfully',
    };
  }

  // If we get here, something went wrong
  return {
    success: false,
    message: response.data?.message || 'Failed to start trial',
  };
};

/**
 * Subscribe to plan after successful payment
 */
export const subscribeAfterPayment = async (data: {
  email?: string;
  name?: string;
  phoneNumber?: string;
  planId?: string;
  billingType: 'monthly' | 'yearly';
  roleId?: string;
  userType?: string;
  userRole?: string;
  selectedAddOnFeatures: {
    MRD: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    EMR: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    Billing: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
  };
  paymentId: string;
  Billing: Array<{
    name: string;
    email: string;
    pincode: string;
  }>;
}): Promise<{ success: boolean; message: string; subscription?: any }> => {
  const response = await publicApi.post<any>(
    '/subscription/v0.1/clinic/subscription?action=subscribe',
    data
  );

  // Check if response contains subscription data (indicates success)
  if (response.data?.subscription?.id) {
    return {
      success: true,
      message: 'Subscription created successfully',
      subscription: response.data.subscription,
    };
  }

  // Check for success field in response
  if (response.data?.success !== false) {
    return {
      success: true,
      message: 'Subscription created successfully',
    };
  }

  // If we get here, something went wrong
  return {
    success: false,
    message: response.data?.message || 'Failed to create subscription',
  };
};

/**
 * Change plan subscription (for profile flows)
 */
export const changePlanSubscription = async (data: {
  email: string;
  newPlanId: string;
  billingType: 'monthly' | 'yearly';
  selectedAddOnFeatures: {
    MRD: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    EMR: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    Billing: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
  };
  paymentId: string;
}): Promise<{ success: boolean; message: string; subscription?: any }> => {
  const response = await publicApi.post<any>(
    '/subscription/v0.1/clinic/subscription?action=change-plan',
    data
  );

  // Check if response contains subscription data (indicates success)
  if (response.data?.subscription?.id) {
    return {
      success: true,
      message: 'Plan changed successfully',
      subscription: response.data.subscription,
    };
  }

  // Check for success field in response
  if (response.data?.success !== false) {
    return {
      success: true,
      message: 'Plan changed successfully',
    };
  }

  // If we get here, something went wrong
  return {
    success: false,
    message: response.data?.message || 'Failed to change plan',
  };
};

/**
 * Validate email for existing subscription
 */
export const validateEmail = async (
  email: string
): Promise<{
  isValid: boolean;
  canProceed: boolean;
  message: string;
  existingSubscription?: {
    id: string;
    status: string;
    planName: string;
  };
  existingUser?: any;
}> => {
  const response = await publicApi.post<{
    isValid: boolean;
    canProceed: boolean;
    message: string;
    existingSubscription?: {
      id: string;
      status: string;
      planName: string;
    };
    existingUser?: any;
  }>('/subscription/v0.1/clinic/subscription?action=validate-email', {
    email,
  });
  return response.data;
};

/**
 * Get roles for organization
 */
export const getOrganizationRoles = async (
  organizationId: string = 'da42e4a1-eecb-4f51-a848-5f83ab76325a',
  page: number = 1,
  pageSize: number = 10
): Promise<{
  currentPage: number;
  items: {
    id: string;
    name: string;
    description: string;
    isDefault: boolean;
    organizationId: string;
  }[];
  totalItemCount: number;
  totalPages: number;
}> => {
  const response = await publicApi.get<{
    currentPage: number;
    items: {
      id: string;
      name: string;
      description: string;
      isDefault: boolean;
      organizationId: string;
    }[];
    totalItemCount: number;
    totalPages: number;
  }>(
    `/role/v0.1/list-roles?page=${page}&pageSize=${pageSize}&organizationId=${organizationId}`
  );
  return response.data;
};

/**
 * Get subscriber data with subscription details
 */
export const getSubscriber = async (
  subscriberId: string
): Promise<{
  id: string;
  name: string;
  contactEmail: string;
  contactPersonName: string;
  contactPhone: string;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  pan: string;
  gstin: string;
  description: string;
  isActive: boolean;
  registrationFee: number;
  createdAt: string;
  updatedAt: string;
  created_by: string;
  created_on: string;
  updated_by: string;
  updated_on: string;
  activeSubscription: {
    id: string;
    organizationId: string;
    planId: string;
    planName: string;
    validity: string;
    billingType: string;
    features: any;
    addOnFeatures: any;
    accessibleModules: string[];
    totalAmount: number;
    status: string;
    subscriptionType: string;
    isFreeTrial: boolean;
    trialUsed: boolean;
    userCreated: boolean;
    startDate: string;
    endDate: string;
    autoRenew: boolean;
    paymentMethod: string;
    paymentId: string | null;
    contactEmail: string;
    created_by: string;
    created_on: string;
    updated_on: string;
    updated_by: string;
  };
  subscriptionHistory: any[];
}> => {
  const response = await api.get<{
    id: string;
    name: string;
    contactEmail: string;
    contactPersonName: string;
    contactPhone: string;
    phoneNumber: string;
    address: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    pan: string;
    gstin: string;
    description: string;
    isActive: boolean;
    registrationFee: number;
    createdAt: string;
    updatedAt: string;
    created_by: string;
    created_on: string;
    updated_by: string;
    updated_on: string;
    activeSubscription: {
      id: string;
      organizationId: string;
      planId: string;
      planName: string;
      validity: string;
      billingType: string;
      features: any;
      addOnFeatures: any;
      accessibleModules: string[];
      totalAmount: number;
      status: string;
      subscriptionType: string;
      isFreeTrial: boolean;
      trialUsed: boolean;
      userCreated: boolean;
      startDate: string;
      endDate: string;
      autoRenew: boolean;
      paymentMethod: string;
      paymentId: string | null;
      contactEmail: string;
      created_by: string;
      created_on: string;
      updated_on: string;
      updated_by: string;
    };
    subscriptionHistory: any[];
  }>(`/subscription/v0.1/subscriber?id=${subscriberId}`);
  return response.data;
};
