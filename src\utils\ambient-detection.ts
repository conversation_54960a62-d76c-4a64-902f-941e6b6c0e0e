/**
 * Utility to detect if an object contains ambient listening data
 */
export const detectAmbientInObject = (obj: any): boolean => {
  if (!obj) return false;

  // Check direct conversation array
  if (Array.isArray(obj.conversation) && obj.conversation.length > 0)
    return true;

  // Check nested conversation arrays
  if (
    Array.isArray(obj?.summary?.conversation) &&
    obj.summary.conversation.length > 0
  )
    return true;
  if (
    Array.isArray(obj?.response?.conversation) &&
    obj.response.conversation.length > 0
  )
    return true;

  // Check other ambient indicators
  if (obj.recordingDuration) return true;
  if (obj.source === 'ambient' || obj.isAmbientForm) return true;

  // Scan for conversation-like arrays (speaker & message objects)
  for (const key of Object.keys(obj)) {
    const val = obj[key];
    if (Array.isArray(val) && val.length > 0 && typeof val[0] === 'object') {
      const first = val[0];
      if (first && 'speaker' in first && 'message' in first) return true;
    }
  }

  return false;
};
