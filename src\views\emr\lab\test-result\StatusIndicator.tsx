import React, { useCallback, useMemo, useState } from 'react';

import { FiUpload } from 'react-icons/fi';

import { useTestStore } from '@/store/emr/lab/reports-store';

import colors from '@/utils/colors';

import { StatusType } from '@/types/emr/lab';

import UploadFileModal from './UploadFileModal';

interface StatusIndicatorProps {
  status: StatusType;
  id: string;
  fetchTestResult?: () => void;
  fileMetadata?: any[]; // add fileMetadata
  testName?: string;
}

const statusColors: Record<StatusType, string> = {
  Ready: colors.status.uploaded,
  Awaited: colors.status.awaited,
  'Not Paid': colors.status.notPaid,
  Upload: colors.status.upload,
  Uploaded: colors.status.uploaded,
};

export const getBgColor = (status: StatusType) => {
  return statusColors[status];
};

const statusStyles: Record<'Ready' | 'Upload', string> = {
  Ready: `bg-[${statusColors.Uploaded}] text-black`,
  Upload: `bg-transparent text-black cursor-pointer`,
};

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  id,
  fetchTestResult,
  fileMetadata,
  testName,
}) => {
  const [open, setOpen] = useState(false);
  const { fetchRecentTest } = useTestStore();

  const displayStatus: 'Ready' | 'Upload' = useMemo(
    () => (status === 'Ready' ? 'Ready' : 'Upload'),
    [status]
  );

  // Filter fileMetadata to only show files relevant to the current test
  const filteredFileMetadata = useMemo(() => {
    if (!fileMetadata || !testName) return fileMetadata || [];

    return fileMetadata.filter((file) => {
      // Check if the file's OCR result matches this test
      const ocrResult = file.ocrResult;
      if (ocrResult?.matchedTests) {
        return ocrResult.matchedTests.includes(testName);
      }

      // If no OCR result, include the file (fallback behavior)
      return true;
    });
  }, [fileMetadata, testName]);

  const onClick = useCallback(() => {
    if (displayStatus === 'Upload' || displayStatus === 'Ready') {
      setOpen(true);
    }
  }, [displayStatus]);

  return (
    <>
      <div
        onClick={onClick}
        className={`w-full h-full text-center py-4 font-medium flex justify-center items-center gap-2 ${displayStatus === 'Upload' || displayStatus === 'Ready' ? 'cursor-pointer' : ''} ${statusStyles[displayStatus]}`}
      >
        {displayStatus === 'Upload' && <FiUpload fontSize="small" />}
        <span className="whitespace-nowrap">{displayStatus}</span>
      </div>
      <UploadFileModal
        open={open}
        onClose={() => setOpen(false)}
        id={id}
        fileMetadata={filteredFileMetadata}
        status={status}
        onUploadSuccess={() => {
          fetchRecentTest();
          fetchTestResult?.();
          setOpen(false);
        }}
      />
    </>
  );
};

export default StatusIndicator;
