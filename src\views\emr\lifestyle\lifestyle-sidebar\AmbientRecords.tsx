import React, { memo, useRef } from 'react';

import { toast } from 'sonner';

import AddRecord from '@/lib/add_record';

import {
  useAmbientStore,
  ambientSelectors,
} from '@/store/emr/lifestyle/ambient-listening/ambient-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { nutritionAttitudeStore } from '@/store/emr/lifestyle/nutrition/attitude/attitude-store';
import { nutritionKnowledgeStore } from '@/store/emr/lifestyle/nutrition/knowledge/knowledge-store';
import { dietaryRecallStore } from '@/store/emr/lifestyle/nutrition/practice/dietary-recall-store';
import { foodFrequencyQuestionnaireStore } from '@/store/emr/lifestyle/nutrition/practice/food-frequency-questionnaire-store';
import { foodIntakePatternStore } from '@/store/emr/lifestyle/nutrition/practice/food-intake-pattern-store';
import { attitudeStore } from '@/store/emr/lifestyle/physical-activity/attitude/attitude-store';
import { knowledgeStore } from '@/store/emr/lifestyle/physical-activity/knowledge/knowledge-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import { checkTodayEntryExists } from '@/utils/emr/lifestyle/daily-entry-validation';

import { LifestyleMode } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import NutritionAttitudeModal from '../nutrition/attitude/attitude-tab/AttitudeModal';
import NutritionKnowledgeModal from '../nutrition/knowledge/knowledge-tab/NutritionKnowledgeModal';
import DietaryRecallModal from '../nutrition/practice/24-hour-dietary-recall-tab/DietaryRecallModal';
import FoodFrequencyQuestionnaireModal from '../nutrition/practice/food-frequency-questionnaire/FoodFrequencyQuestionnaireModal';
import FoodIntakePatternModal from '../nutrition/practice/food-intake-patterns-tab/FoodIntakePatternModal';
import AttitudeModal from '../physical-activity/attitude/attitude-tab/AttitudeModal';
import KnowledgeModal from '../physical-activity/knowledge/knowledge-tab/KnowledgeModal';
import ExercisePatternModal from '../physical-activity/practice/exercise-patterns-tab/ExercisePatternModal';

const {
  NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS,
  NUTRITION_ATTITUDE,
  NUTRITION_KNOWLEDGE,
  PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS,
  PHYSICAL_ACTIVITY_ATTITUDE,
  PHYSICAL_ACTIVITY_KNOWLEDGE,
  NUTRITION_PRACTICE_DIETARY_RECALL,
  NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE,
} = LifestyleSources;

// Update getFormModal to accept summary, conversation and saveRef
const getFormModal = (
  source: LifestyleSources | null,
  summary: any,
  conversation: any,
  onSaveRef: React.MutableRefObject<(() => void) | null>
) => {
  // Combine summary and conversation data for initialValues
  const ambientData = {
    ...summary,
    conversation: conversation || [],
    recordingDuration: summary?.recordingDuration,
  };

  const commonProps = {
    mode: LifestyleMode.CREATE,
    hideSaveButton: true,
    // Pass the ambient data (summary + conversation) as both initialValues and patientData
    initialValues: ambientData,
    patientData: ambientData,
    onSaveRef, // Pass the saveRef directly
  };

  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return <FoodIntakePatternModal {...commonProps} />;
    case NUTRITION_ATTITUDE:
      return <NutritionAttitudeModal {...commonProps} />;
    case NUTRITION_KNOWLEDGE:
      return <NutritionKnowledgeModal {...commonProps} />;
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return <ExercisePatternModal {...commonProps} />;
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return <AttitudeModal {...commonProps} />;
    case NUTRITION_PRACTICE_DIETARY_RECALL:
      return <DietaryRecallModal {...commonProps} />;
    case PHYSICAL_ACTIVITY_KNOWLEDGE:
      return <KnowledgeModal {...commonProps} />;
    case NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE:
      return <FoodFrequencyQuestionnaireModal {...commonProps} />;
    default:
      return <></>;
  }
};

const getFormTitle = (source: LifestyleSources | null) => {
  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return 'Practice';
    case NUTRITION_ATTITUDE:
      return 'Attitude';
    case NUTRITION_KNOWLEDGE:
      return 'Knowledge';
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return 'Practice';
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return 'Attitude';
    case PHYSICAL_ACTIVITY_KNOWLEDGE:
      return 'Knowledge';
    case NUTRITION_PRACTICE_DIETARY_RECALL:
      return 'Practice';
    case NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE:
      return 'Practice';
    default:
      return 'Add Record';
  }
};

const AmbientRecords = () => {
  const { source } = lifestyleStore();
  const { updating } = foodIntakePatternStore();
  const saveRef = useRef<(() => void) | null>(null);

  // Get summary and conversation from ambient store
  const summary = useAmbientStore(ambientSelectors.selectSummary);
  const conversation = useAmbientStore(ambientSelectors.selectConversation);

  // Hold latest initial values for the hidden modal so we can inject
  // manual edits from the ambient summary before triggering the modal save
  const [hiddenInitialValues, setHiddenInitialValues] =
    React.useState<any>(null);

  // Ensure hiddenInitialValues tracks ambient store values when they change
  React.useEffect(() => {
    setHiddenInitialValues({
      ...summary,
      conversation: conversation || [],
      recordingDuration: summary?.recordingDuration,
    });
  }, [summary, conversation]);

  const { patientData: exerciseData } = exercisePatternStore();
  const { patientData: physicalAttitudeData } = attitudeStore();
  const { patientData: nutritionAttitudeData } = nutritionAttitudeStore();
  const { patientData: knowledgeData } = knowledgeStore();
  const { patientData: nutritionKnowledgeData } = nutritionKnowledgeStore();
  const { patientData: foodIntakeData } = foodIntakePatternStore();
  const { patientData: dietaryRecallData } = dietaryRecallStore();
  const { patientData: foodFrequencyData } = foodFrequencyQuestionnaireStore();

  const isTodayEntryExists = checkTodayEntryExists(source, {
    exerciseData,
    physicalAttitudeData,
    nutritionAttitudeData,
    knowledgeData,
    nutritionKnowledgeData,
    foodIntakeData,
    dietaryRecallData,
    foodFrequencyData,
  });

  const isButtonDisabled = !source || isTodayEntryExists;

  const getAddRecordProps = () => {
    const modalTitle = `Add ${getFormTitle(source)}`;

    // Get the form modal with the current summary, conversation and saveRef
    const formModal = getFormModal(
      source,
      hiddenInitialValues,
      hiddenInitialValues?.conversation,
      saveRef
    );

    return {
      modalTitle,
      customSummaryForm: formModal,
      summaryFormTitle: getFormTitle(source),
      summaryFormSubtitle: 'Details',
    };
  };

  const addRecordProps = getAddRecordProps();

  // Render the modal component immediately (but hidden) so its useEffect can run
  // Create a separate ref for the hidden modal so it doesn't overwrite
  // the visible modal's saveRef when both are mounted. The visible
  // modal (inside AddRecord) should own `saveRef` so calling it will
  // submit the user's visible form (preserving manual edits).
  const hiddenSaveRef = React.useRef<(() => void) | null>(null);

  const hiddenModalComponent = getFormModal(
    source,
    hiddenInitialValues,
    hiddenInitialValues?.conversation,
    hiddenSaveRef
  );
  // Handler to trigger the save/submit function from the modal
  const handleAddRecordSave = async (data?: any) => {
    // If caller provided updated summary data (from AddRecord), inject it
    // into the hidden modal then trigger its save handler.
    if (data) {
      // Deep merge the current form values with the ambient data
      const mergedData = {
        ...hiddenInitialValues, // Start with original ambient data
        ...data, // Overlay with any updates from AddRecord
        summary: {
          ...(hiddenInitialValues?.summary || {}), // Original ambient summary
          ...(data?.summary || {}), // New summary changes
          vitals: {
            ...(hiddenInitialValues?.summary?.vitals || {}),
            ...(data?.summary?.vitals || {}),
          },
          anthropometry: {
            ...(hiddenInitialValues?.summary?.anthropometry || {}),
            ...(data?.summary?.anthropometry || {}),
          },
          generalPhysicalExamination: {
            ...(hiddenInitialValues?.summary?.generalPhysicalExamination || {}),
            ...(data?.summary?.generalPhysicalExamination || {}),
          },
          systemicExamination: {
            ...(hiddenInitialValues?.summary?.systemicExamination || {}),
            ...(data?.summary?.systemicExamination || {}),
          },
        },
        conversation:
          data?.conversation ||
          hiddenInitialValues?.conversation ||
          conversation,
        recordingDuration:
          data?.recordingDuration ||
          hiddenInitialValues?.recordingDuration ||
          summary?.recordingDuration,
      };
      setHiddenInitialValues(mergedData);
      // Wait a tick for the hidden modal to receive new props
      await new Promise((resolve) => setTimeout(resolve, 120));
    }

    // Give the modal components time to set up the saveRef if it's not available yet
    if (!saveRef.current) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    if (saveRef.current) {
      try {
        // Call the modal's save function directly
        saveRef.current();
      } catch (error) {
        console.error('Error in save operation:', error);
        toast.error('Error in saving record');
      }
    } else {
      toast.error('Save function not available');
    }
  };

  return (
    <>
      <AddRecord
        disabled={isButtonDisabled}
        isAmbientRecord={true}
        source={source || ''}
        {...addRecordProps}
        onSave={handleAddRecordSave}
        isLoading={updating}
      />
      {/* Render the modal component hidden so its useEffect can run and set up saveRef */}
      <div style={{ display: 'none' }}>{hiddenModalComponent}</div>
    </>
  );
};

export default memo(AmbientRecords);
