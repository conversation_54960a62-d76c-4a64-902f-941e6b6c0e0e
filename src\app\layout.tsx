import { archivo, inter } from '@/utils/fonts';

import { FAVICON_PNG, FAVICON_SVG } from '@/constants/image-url';

import { FaroInitializer } from '@/components/observability/FaroInitializer';

import appMetadata from '@/core/configs/app-metadata';
import AppProviders from '@/core/providers/AppProviders';
import '@/styles/global/common.css';
import '@/styles/global/scrollbar.css';
import '@/styles/global/styles.css';

export const metadata = appMetadata.root;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href={FAVICON_SVG} type="image/svg+xml" />
        <link rel="icon" href={FAVICON_PNG} type="image/png" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body
        className={`${archivo.variable} ${inter.variable} ${archivo.className}`}
      >
        <AppProviders>
          <FaroInitializer />
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
