import React, { memo, useCallback, useEffect, useRef } from 'react';

import {
  ArrayPath,
  Control,
  FieldArray,
  FieldValues,
  Path,
  useFieldArray,
} from 'react-hook-form';

import { FaClock } from 'react-icons/fa6';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import colors from '@/utils/colors';

import { lifestyleModes } from '@/constants/lifestyle';

import { SubQuestions } from '@/emr/types/lifestyle';

import AddButton from './AddButton';
import ControlledInput from './ControlledInput';
import RemoveButton from './RemoveButton';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: ArrayPath<T>;
  label?: string;
  isReadOnly?: boolean;
};

const { CREATE } = lifestyleModes;

const FieldArrayTimeRange = <T extends FieldValues>({
  control,
  name,
  label,
  isReadOnly,
}: Props<T>) => {
  const { formMode } = useLifestyleUtilStore();
  const containerRef = useRef<HTMLDivElement>(null);

  const { fields, remove, append } = useFieldArray({
    control,
    name: name,
  });

  const onAppend = useCallback(() => {
    append({ from: '', to: '' } as FieldArray<T, ArrayPath<T>>);
  }, [append]);

  useEffect(() => {
    if (formMode === CREATE && !isReadOnly) {
      onAppend();
    }
  }, [onAppend, formMode, isReadOnly]);

  const handleClockClick = useCallback(
    (index: number, type: 'from' | 'to') => {
      if (isReadOnly) return;

      const inputName = `${name}.${index}.${type}`;
      let inputElement = containerRef.current?.querySelector(
        `input[name="${inputName}"]`
      ) as HTMLInputElement;

      if (!inputElement) {
        const timeInputs =
          containerRef.current?.querySelectorAll('input[type="time"]');
        const targetIndex = type === 'from' ? index * 2 : index * 2 + 1;
        inputElement = timeInputs?.[targetIndex] as HTMLInputElement;
      }

      if (inputElement) {
        inputElement.focus();

        if (inputElement.showPicker) {
          try {
            inputElement.showPicker();
          } catch (_error) {
            console.warn(
              'FieldArray - showPicker not supported, falling back to click'
            );
            inputElement.click();
          }
        } else {
          inputElement.click();
        }
      } else {
        console.error(
          'FieldArray - Could not find input element for:',
          inputName
        );
      }
    },
    [name, isReadOnly]
  );

  const renderEndIcon = useCallback(
    (index: number, type: 'from' | 'to') => {
      return (
        <div
          className="pt-1"
          style={{
            cursor: isReadOnly ? 'default' : 'pointer',
            display: 'inline-block',
            padding: '2px',
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();

            handleClockClick(index, type);
          }}
        >
          <FaClock
            style={{
              marginTop: 3,
              fontSize: 16,
              color: colors.common.dark,
              pointerEvents: 'none',
            }}
          />
        </div>
      );
    },
    [handleClockClick, isReadOnly]
  );

  return (
    <div className="w-full md:w-1/2 flex flex-col gap-2 p-2" ref={containerRef}>
      <span>{label}</span>
      {fields?.map((field, index) => {
        if (
          (field as SubQuestions['value'])?.from === '' &&
          (field as SubQuestions['value'])?.to === '' &&
          isReadOnly
        ) {
          return null;
        }
        return (
          <div
            key={field.id}
            className="flex justify-between items-center gap-2"
          >
            <ControlledInput
              control={control}
              type="time"
              variant={isReadOnly ? 'transparent' : 'gray'}
              className="max-w-30"
              name={`${name}.${index}.from` as Path<T>}
              placeholder="Enter"
              inputClassName="!w-full"
              disabled={isReadOnly}
              endDecoration={renderEndIcon(index, 'from')}
              iconClassName="right-[0px]"
              showTooltip={false}
            />
            <span>to</span>
            <ControlledInput
              control={control}
              type="time"
              variant={isReadOnly ? 'transparent' : 'gray'}
              className="max-w-30"
              name={`${name}.${index}.to` as Path<T>}
              placeholder="Enter"
              inputClassName="!w-full"
              disabled={isReadOnly}
              endDecoration={renderEndIcon(index, 'to')}
              iconClassName="right-[0px]"
              showTooltip={false}
            />
            {!isReadOnly && (
              <>
                {fields?.length - 1 > index ? (
                  <RemoveButton onRemove={() => remove(index)} />
                ) : (
                  <AddButton onClick={onAppend} />
                )}
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default memo(FieldArrayTimeRange);
