import React, { memo, useEffect, useMemo, useState, useCallback } from 'react';

import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { foodFrequencyQuestionnaireStore } from '@/store/emr/lifestyle/nutrition/practice/food-frequency-questionnaire-store';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import FinalizeModal from '@/views/emr/lifestyle/shared/FinalizeModal';
import LifestyleAccordionWrapper from '@/views/emr/lifestyle/shared/LifestyleAccordionWrapper';
import TimeLine from '@/views/emr/lifestyle/shared/TimeLine';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import FoodFrequencyQuestionnaireEditModal from './FoodFrequencyQuestionnaireEditModal';
import FoodFrequencyQuestionnaireTimelineForm from './FoodFrequencyQuestionnaireTimelineForm';

const FoodFrequencyQuestionnaire = () => {
  const { setSource } = lifestyleStore();
  const { getPatientData, patientData, loading, finalizeRecord, finalizing } =
    foodFrequencyQuestionnaireStore();
  const { fromDate, toDate } = useLifestyleFilterStore();

  const [openedAccordion, setOpenedAccordion] = useState<number | null>(null);
  const [recordToFinalize, setRecordToFinalize] =
    useState<QuestionnaireResponse | null>(null);
  const [editRecord, setEditRecord] = useState<QuestionnaireResponse | null>(
    null
  );

  useEffect(() => {
    setSource(LifestyleSources.NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE);
  }, [setSource]);

  useEffect(() => {
    getPatientData(fromDate, toDate);
  }, [getPatientData, fromDate, toDate]);

  const handleFinalize = useCallback(async () => {
    if (!recordToFinalize?.id) return;

    try {
      await finalizeRecord(recordToFinalize.id);
      setRecordToFinalize(null);
    } catch (error) {
      console.error('Error finalizing record:', error);
    }
  }, [recordToFinalize, finalizeRecord]);

  const timelineItems = useMemo(() => {
    return patientData.map((data, index) => ({
      id: data?.id,
      content: (
        <LifestyleAccordionWrapper
          key={`${data.id}-${data.created_on}`}
          data={data}
          date={data.created_on}
          open={openedAccordion === index}
          onToggle={() => {
            setOpenedAccordion(openedAccordion === index ? null : index);
          }}
          onFinalise={() => setRecordToFinalize(data)}
          finalised={data.status === LifestyleRecordStatus.FINALIZED}
          doctorName={data?.doctor?.name}
          designation={data?.doctor?.designation}
          department={data?.doctor?.department}
          className="bg-white rounded-lg shadow-sm"
          onExpand={() => setEditRecord(data)}
          stepper={['Practice', 'Food Frequency Questionnaire']}
        >
          <FoodFrequencyQuestionnaireTimelineForm data={data} isExpandView />
        </LifestyleAccordionWrapper>
      ),
    }));
  }, [patientData, openedAccordion]);

  return (
    <div className="h-full flex flex-col w-full p-1 overflow-y-auto">
      <TimeLine items={timelineItems} loading={loading} />
      <FinalizeModal
        open={!!recordToFinalize}
        onClose={() => setRecordToFinalize(null)}
        onFinalize={handleFinalize}
        loading={finalizing}
      />
      <FoodFrequencyQuestionnaireEditModal
        open={!!editRecord}
        onClose={() => setEditRecord(null)}
        formFields={editRecord}
        onFinalize={() => setEditRecord(editRecord)}
      />
    </div>
  );
};

export default memo(FoodFrequencyQuestionnaire);
