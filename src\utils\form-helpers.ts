export const EMPTY_RESPONSES = [
  'No answer provided',
  'No information found',
  'No information',
  'Not found',
  'No data',
  'Unknown',
];

export const isEmptyField = (value: any): boolean => {
  if (value === undefined || value === null) return true;
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (trimmed === '') return true;
    const lower = trimmed.toLowerCase();
    return EMPTY_RESPONSES.some((r) => r.toLowerCase() === lower);
  }
  // For arrays/objects, consider emptiness if length/keys are zero
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};
