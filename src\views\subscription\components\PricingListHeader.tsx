interface PricingListHeaderProps {
  className?: string;
}

const PricingListHeader: React.FC<PricingListHeaderProps> = ({
  className = '',
}) => {
  return (
    <div className={`text-center mb-6 ${className}`}>
      <h1
        className="text-2xl font-bold text-gray-800 mb-2"
        style={{ fontSize: '24px', fontWeight: 700, color: '#231D4F' }}
      >
        Pricing List
      </h1>
      <p
        className="text-gray-600 mb-6"
        style={{ fontSize: '16px', fontWeight: 400, color: '#848199' }}
      >
        No contracts. No surprise fees.
      </p>
    </div>
  );
};

export default PricingListHeader;
