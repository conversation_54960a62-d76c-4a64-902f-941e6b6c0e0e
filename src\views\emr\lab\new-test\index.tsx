import React, { useState, useEffect, useMemo, useCallback } from 'react';

import { useForm, useFieldArray } from 'react-hook-form';

import { cn } from '@/lib/utils';

import usePaymentPermissions from '@/hooks/usePaymentPermissions';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { usePackageSelectorStore } from '@/store/emr/lab/package-store';
import { useLabPrintStore } from '@/store/emr/lab/print-store';
import { useTestStore } from '@/store/emr/lab/reports-store';
import { usePaymentStore } from '@/store/payments';
import { useUserStore } from '@/store/userStore';

import { formatDate } from '@/utils/dateUtils/dayUtils';

import {
  commonButtonProps,
  reportHeaders,
  tableContainerSx,
} from '@/constants/emr/lab';

import ConfirmationModal from '@/views/emr/prescription/shared/SaveConfirmationModal';
import PaymentFailureModal from '@/views/mrd/payment/PaymentFailureModal';

import FieldValidationError from '@/emr/components/shared/FieldValidationError';

import PrimaryButton from '@/core/components/primary-button';
import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import {
  defaultLabTestRow,
  LabTestItem,
  labTestModalTypes,
  modalModes,
  packageTypes,
  testReportTabs,
  TestResultItem,
} from '@/types/emr/lab';

import LabTestPaymentConfirmationModal from './LabTestPaymentConfirmationModal';
import LabTestPaymentSuccessModal from './LabTestPaymentSuccessModal';
import LabTestRow from './LabTestRow';

export type LabTest = {
  labTest: LabTestItem[];
};

const { TEST_RESULTS } = testReportTabs;
const { SAVE, CLEAR } = labTestModalTypes;

const NewTest = () => {
  const {
    setActiveTab,
    removeSelectedSearchTest,
    selectedSearchTests,
    clearSelectedLabTest,
    createLabTest,
    isLoading,
  } = useTestStore();
  const doctorId = useDoctorStore((state) => state?.doctorProfile?.id);

  const { openModal, startCreatePackage } = usePackageSelectorStore();

  const { patient } = useCurrentPatientStore();
  const { data: userData } = useUserStore();
  const { isPaymentEnabled } = usePaymentPermissions();

  const { createOrder, openRazorpayPayment, resetPaymentState } =
    usePaymentStore();

  // Check if lab test payment is enabled based on user permissions/settings
  const isLabTestPaymentEnabled = isPaymentEnabled('labTestEnabled');

  const { onExpand } = useLabPrintStore();

  const [modalType, setModalType] = useState<string | null>('');
  const [isOrdering, setIsOrdering] = useState(false);
  const [isValidationEnabled, setIsValidationEnabled] = useState(false);
  const [showPaymentConfirmationModal, setShowPaymentConfirmationModal] =
    useState(false);
  const [showPaymentSuccessModal, setShowPaymentSuccessModal] = useState(false);

  useEffect(() => {}, [showPaymentSuccessModal]);
  const [showFailureModal, setShowFailureModal] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const defaultValues = useMemo(() => {
    return {
      labTest:
        selectedSearchTests?.length > 0
          ? selectedSearchTests
          : [defaultLabTestRow],
    };
  }, [selectedSearchTests]);

  const {
    control,
    handleSubmit,
    clearErrors,
    reset,
    trigger,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues: defaultValues,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const { fields } = useFieldArray({
    control,
    name: 'labTest',
  });

  const watchLabTest = watch('labTest');

  const [forceUpdate, setForceUpdate] = useState(0);

  const totalCost = useMemo(() => {
    if (!watchLabTest) return 0;

    return watchLabTest.reduce((sum, item) => {
      if (!item?.cost) return sum;

      const costValue = parseFloat(item.cost.replace(/[^\d.]/g, '')) || 0;
      return sum + costValue;
    }, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchLabTest, forceUpdate]);

  const handleRemove = useCallback(
    (index: number, id: string) => {
      removeSelectedSearchTest(id);
      const prevValues = getValues();
      const existingLabTests = (prevValues.labTest || []).filter(Boolean);

      const updatedLabTests = existingLabTests.filter(
        (test) => test?.id !== id
      );

      const renumberedLabTests = updatedLabTests.map((row, idx) => ({
        ...row,
        no: String(idx + 1),
        cost: row.cost ?? '',
        testName: row.testName ?? '',
        quantity: row.quantity ?? 1,
        instructions: row.instructions ?? '',
        toBeDoneBy: row.toBeDoneBy ?? '',
      }));

      if (renumberedLabTests.length === 0) {
        reset({
          ...prevValues,
          labTest: [defaultLabTestRow],
        });
      } else {
        reset({
          ...prevValues,
          labTest: renumberedLabTests as LabTestItem[],
        });
      }

      setForceUpdate((prev) => prev + 1);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [removeSelectedSearchTest, getValues, reset, defaultLabTestRow]
  );

  useEffect(() => {
    const filteredTests = selectedSearchTests.filter(
      (test) =>
        test.pageKey === packageTypes.DEFAULT ||
        test.pageKey === packageTypes.NEW_TEST
    );

    if (filteredTests.length > 0) {
      const prevValues = getValues();
      const existingLabTests = (prevValues.labTest || []).filter(Boolean);

      const existingTestIds = new Set(existingLabTests.map((test) => test?.id));

      const newTests = filteredTests.filter(
        (test) => !existingTestIds.has(test.id)
      );
      if (newTests.length > 0) {
        const mappedLabTests = newTests.map((test) => ({
          id: test.id,
          testName: test.name,
          quantity: 1,
          cost:
            test.cost ||
            (test.organizationPrice
              ? String(test.organizationPrice)
              : Number(test.organizationCost) > 0
                ? String(test.organizationCost)
                : Number(test.price) > 0
                  ? String(test.price)
                  : '0'),
        }));

        const existingRowsWithNumbers = existingLabTests
          .filter((test) => test?.id && test?.testName)
          .map((row, index) => ({
            ...row,
            no: String(index + 1),
          }));

        const newRows = mappedLabTests.map((test, index) => ({
          ...test,
          no: String(existingRowsWithNumbers.length + index + 1),
          testId: test.id,
        }));

        const mergedLabTest = [...existingRowsWithNumbers, ...newRows];

        reset({
          ...prevValues,
          labTest: mergedLabTest as LabTestItem[],
        });
      }
    }
  }, [selectedSearchTests, reset, getValues]);

  const handleCostChange = () => {
    setForceUpdate((prev) => prev + 1);
  };

  const rows: Row[] = fields.map((field, index) => {
    return LabTestRow({
      index,
      control,
      clearErrors,
      watchLabTest,
      errors,
      handleRemove,
      selectedSearchTests,
      defaultLabTestRow,
      isValidationEnabled,
      onCostChange: handleCostChange,
    });
  });

  const onSubmit = useCallback(
    async (data: LabTest) => {
      const today = new Date().toISOString().split('T')[0];

      const formattedLabTests = (data.labTest ?? []).map((test) => ({
        testName: test.testName,
        qty: test.quantity,
        instructions: test.instructions,
        cost: test.cost,
        toBeDoneBy: formatDate(test.toBeDoneBy),
        date: today,
        testId: test.id,
        results: null,
        reference: null,
      }));

      const payload = {
        patientId: patient?.id,
        labTests: formattedLabTests,
        doctorId,
      };

      const response = await createLabTest(payload);
      reset({ labTest: [defaultLabTestRow] });
      clearSelectedLabTest();
      return response;
    },
    [patient?.id, doctorId, createLabTest, reset, clearSelectedLabTest]
  );

  const handleSaveClick = async () => {
    setIsValidationEnabled(true);
    const isValid = await trigger();
    if (isValid) {
      setShowPaymentConfirmationModal(true);
    }
  };

  const handleClearClick = () => {
    setModalType(CLEAR);
  };

  const handlePaymentModalClose = useCallback(() => {
    setShowPaymentConfirmationModal(false);
  }, []);

  const handlePaymentModalSave = useCallback(async () => {
    setShowPaymentConfirmationModal(false);

    try {
      const formData = getValues();
      const today = new Date().toISOString().split('T')[0];

      const formattedLabTests = (formData.labTest ?? []).map((test) => ({
        testName: test.testName,
        qty: test.quantity,
        instructions: test.instructions,
        cost: test.cost,
        toBeDoneBy: formatDate(test.toBeDoneBy),
        date: today,
        testId: test.id,
        results: null,
        reference: null,
      }));

      const payload = {
        patientId: patient?.id,
        labTests: formattedLabTests,
        doctorId,
      };

      const response = await createLabTest(payload);

      reset({ labTest: [defaultLabTestRow] });
      clearSelectedLabTest();

      setActiveTab(TEST_RESULTS);

      const testResultItem: TestResultItem = {
        id: response?.id || 'temp-id',
        patientId: patient?.id || '',
        status: 'Upload',
        created_on: new Date().toISOString(),
        updated_on: new Date().toISOString(),
        labTests: {
          General: formattedLabTests.map((test) => ({
            id: test.testId || '',
            testName: test.testName || '',
            reference: test.reference,
            results: test.results,
            qty: parseInt(test.qty || '1'),
            instructions: test.instructions || '',
            cost: parseFloat(test.cost || '0'),
            toBeDoneBy: test.toBeDoneBy || '',
            date: test.date,
            testId: test.testId || '',
            department: 'General',
            fileMetadata: [],
            status: 'Upload',
          })),
        },
      };

      onExpand([testResultItem]);
    } catch (error) {
      console.error('Error saving lab test order:', error);

      setModalType(SAVE);
    }
  }, [
    getValues,
    patient?.id,
    doctorId,
    createLabTest,
    reset,
    clearSelectedLabTest,
    setActiveTab,
  ]);

  const handleProceedToPay = useCallback(async () => {
    if (!patient?.id || !userData?.organizationId) {
      console.error('Missing patient ID or organization ID');
      return;
    }

    try {
      setIsProcessingPayment(true);

      const paymentData = {
        amount: parseFloat(totalCost.toFixed(2)),
        currency: 'INR',
        paymentType: 'lab_test',
        organizationId: userData.organizationId,
        patientId: patient.id,
        description: 'Lab Test Orders',
        metadata: {},
      };

      const order = await createOrder(paymentData);

      if (!order) {
        throw new Error('Failed to create payment order');
      }

      // Extract paymentId from order creation response
      const orderPaymentId = order.data.paymentId;

      setIsProcessingPayment(false);
      setShowPaymentConfirmationModal(false);

      await openRazorpayPayment(
        order,
        patient.name || 'Patient',
        async (response) => {
          // Console log Razorpay response for lab test

          setIsProcessingPayment(false);
          try {
            const currentLabTests = getValues('labTest');
            const today = new Date().toISOString().split('T')[0];
            const formattedLabTests = (currentLabTests ?? []).map((test) => ({
              testName: test.testName,
              qty: test.quantity,
              instructions: test.instructions,
              cost: test.cost,
              toBeDoneBy: formatDate(test.toBeDoneBy),
              date: today,
              testId: test.id,
              results: null,
              reference: null,
              status: 'Paid',
            }));

            const payload = {
              patientId: patient?.id,
              labTests: formattedLabTests,
              doctorId,
              // Use paymentId from order creation response
              paymentId: orderPaymentId,
            };

            const labTestData = await createLabTest(payload, false);

            reset({ labTest: [defaultLabTestRow] });
            clearSelectedLabTest();

            setShowPaymentSuccessModal(true);
          } catch (error) {
            console.error('❌ Error saving lab test after payment:', error);
            console.error('❌ Full error details:', error);
            setShowFailureModal(true);
          }
        },
        (error) => {
          console.error('Payment error:', error);

          setIsProcessingPayment(false);
          setShowFailureModal(true);
        }
      );
    } catch (error) {
      console.error('Error in payment process:', error);
      setIsProcessingPayment(false);
      setShowPaymentConfirmationModal(false);
      setShowFailureModal(true);
    }
  }, [
    patient?.id,
    patient?.name,
    userData?.organizationId,
    totalCost,
    createOrder,
    openRazorpayPayment,
    setActiveTab,
    getValues,
    doctorId,
    createLabTest,
    reset,
    clearSelectedLabTest,
  ]);

  const handlePaymentSuccessClose = useCallback(() => {
    setShowPaymentSuccessModal(false);
    resetPaymentState();

    setActiveTab(TEST_RESULTS);
  }, [resetPaymentState, setActiveTab]);

  const handlePaymentFailureClose = useCallback(() => {
    setShowFailureModal(false);
  }, []);

  const handleRetryPayment = useCallback(() => {
    setShowFailureModal(false);
    handleProceedToPay();
  }, [handleProceedToPay]);

  const currentLabTests = getValues('labTest');

  const isDefaultPrescriptionOnly =
    currentLabTests.length === 1 &&
    Object.keys(defaultLabTestRow).every(
      (key) => currentLabTests[0][key] === defaultLabTestRow[key]
    );

  const handleSaveConfirm = () => {
    if (!isDefaultPrescriptionOnly) {
      handleSubmit(async (data) => {
        await onSubmit(data);
        setModalType(null);
        setActiveTab(TEST_RESULTS);
      })();
    } else {
      setModalType(null);
    }
  };

  const handleClearConfirm = () => {
    reset({
      labTest: [defaultLabTestRow],
    });
    setModalType(null);
    clearSelectedLabTest();
  };

  const handleCancel = () => {
    setModalType(null);
  };

  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      if (name?.includes('.cost') || type === 'change') {
        setForceUpdate((prev) => prev + 1);
      }

      if (name?.startsWith('labTest.')) {
        if (isValidationEnabled) {
          trigger(name);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [
    watch,
    trigger,
    getValues,
    setValue,
    selectedSearchTests,
    isValidationEnabled,
  ]);

  useEffect(() => {
    return () => {
      reset({
        labTest: [defaultLabTestRow],
      });
    };
  }, [reset]);

  const handleCreatePackage = () => {
    openModal(packageTypes.USER);
    startCreatePackage(modalModes.ADD);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col h-full">
      <div className="flex-1 overflow-hidden pb-5">
        <Table
          headers={reportHeaders}
          rows={rows}
          stickyHeader
          tableContainerProps={{
            sx: tableContainerSx,
          }}
        />
        <FieldValidationError errors={errors} fieldKey="labTest" />
      </div>

      <div className="flex items-center justify-between px-4 py-3  mt-auto">
        <div
          className={cn(
            commonButtonProps.className,
            'text-[13px] font-medium border p-1'
          )}
        >
          Total cost: ₹{totalCost.toFixed(2)}
        </div>

        <div className="flex gap-2">
          <PrimaryButton
            variant="outlined"
            {...commonButtonProps}
            onClick={handleCreatePackage}
          >
            Create new package +
          </PrimaryButton>

          <PrimaryButton
            onClick={handleClearClick}
            {...commonButtonProps}
            className={`${commonButtonProps.className} bg-gray-200 text-black !min-w-[110px]    hover:bg-gray-300`}
          >
            Clear All
          </PrimaryButton>

          {isLabTestPaymentEnabled ? (
            <PrimaryButton
              onClick={handleSaveClick}
              {...commonButtonProps}
              className={`${commonButtonProps.className} !min-w-[110px]`}
              isDisabled={isDefaultPrescriptionOnly}
            >
              Proceed
            </PrimaryButton>
          ) : (
            <PrimaryButton
              onClick={async () => {
                if (isOrdering || isDefaultPrescriptionOnly) return;

                setIsOrdering(true);
                try {
                  const isValid = await trigger();
                  if (isValid) {
                    await handleSubmit(async (data) => {
                      await onSubmit(data);
                      setActiveTab(TEST_RESULTS);
                    })();
                  }
                } finally {
                  setIsOrdering(false);
                }
              }}
              {...commonButtonProps}
              className={`${commonButtonProps.className} !min-w-[110px]`}
              isDisabled={isDefaultPrescriptionOnly || isOrdering}
              isLoading={isOrdering}
            >
              Order
            </PrimaryButton>
          )}
        </div>
      </div>

      <ConfirmationModal
        open={modalType === SAVE}
        onConfirm={handleSaveConfirm}
        onCancel={handleCancel}
        isLoading={isLoading}
        title="Confirm order"
        confirmText="Order"
        cancelText="Cancel"
      />
      <ConfirmationModal
        open={modalType === CLEAR}
        onConfirm={handleClearConfirm}
        onCancel={handleCancel}
        title={
          <>
            Do you want to clear all <br /> the tests?
          </>
        }
        confirmText="Clear"
        cancelText="Cancel"
        width="24vw"
      />

      <LabTestPaymentConfirmationModal
        open={showPaymentConfirmationModal}
        onClose={handlePaymentModalClose}
        onOrderAndPrint={handlePaymentModalSave}
        onProceedToPay={handleProceedToPay}
        loading={isProcessingPayment}
        totalCost={`₹${totalCost.toFixed(2)}`}
      />

      <LabTestPaymentSuccessModal
        open={showPaymentSuccessModal}
        onClose={handlePaymentSuccessClose}
      />

      <PaymentFailureModal
        open={showFailureModal}
        onClose={handlePaymentFailureClose}
        onRetry={handleRetryPayment}
        showCloseButton={false}
        errorMessage={
          <>
            Transaction failed!
            <br />
            Please try again later.
          </>
        }
      />
    </form>
  );
};

export default NewTest;
