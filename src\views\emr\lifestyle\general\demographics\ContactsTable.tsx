import { useMemo, useState, useCallback, useEffect } from 'react';

import { UseFieldArrayReturn } from 'react-hook-form';

import { InputBase } from '@mui/material';

import PenIcon from '@/assets/svg/PenIcon';

import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';
import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2 } from '@/core/components/table-v2/types';
import { Contact } from '@/types/mrd/manage-patient/patient-details';

// Extend the Contact type to include id for react-hook-form and make fields optional
type ContactWithId = Partial<Contact> & { id?: string };

interface ContactsTableProps {
  contacts: Contact[];
  isViewMode?: boolean;
  fieldArray?: Omit<UseFieldArrayReturn<any, 'contacts', 'id'>, 'fields'> & {
    fields: ContactWithId[];
  };
  register?: any;
  onPhoneKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onPhoneInput?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const tableHeaders: HeaderV2[] = [
  {
    key: 'phoneNumber',
    header: 'Phone Number',
    cellProps: { align: 'left' as const },
  },
  {
    key: 'email',
    header: 'Email',
    cellProps: { align: 'left' as const },
  },
];

const ContactsTable = ({
  contacts,
  isViewMode = false,
  fieldArray,
  register,
  onPhoneKeyDown = () => {},
  onPhoneInput = () => {},
}: ContactsTableProps) => {
  const { fields = [], append, remove } = fieldArray || {};
  const [editingFields, setEditingFields] = useState<
    Record<string, { phone: boolean; email: boolean }>
  >({});
  const [initialValues, setInitialValues] = useState<
    Record<string, { phone: string; email: string }>
  >({});

  // Track initial values on first render
  useEffect(() => {
    if (!fields || fields.length === 0) return;

    const hasNoInitialValues = Object.keys(initialValues).length === 0;
    const fieldsChanged = fields.length !== Object.keys(initialValues).length;

    if (hasNoInitialValues || fieldsChanged) {
      const initial: Record<string, { phone: string; email: string }> = {};

      fields.forEach(
        (
          field: { id?: string; phone?: string; email?: string },
          index: number
        ) => {
          const fieldId = field.id || index.toString();
          // Only set initial value if it doesn't exist or if the field has changed
          if (
            !initialValues[fieldId] ||
            initialValues[fieldId].phone !== field.phone ||
            initialValues[fieldId].email !== field.email
          ) {
            initial[fieldId] = {
              phone: field.phone || '',
              email: field.email || '',
            };
          } else {
            initial[fieldId] = initialValues[fieldId];
          }
        }
      );

      // Only update if there are changes
      if (JSON.stringify(initial) !== JSON.stringify(initialValues)) {
        setInitialValues(initial);
      }
    }
  }, [fields]); // Removed initialValues from dependencies

  const isNewRow = useCallback(
    (id: string) => {
      return (
        !initialValues[id] ||
        (!initialValues[id].phone && !initialValues[id].email)
      );
    },
    [initialValues]
  );

  const toggleEditField = useCallback(
    (id: string, field: 'phone' | 'email') => {
      setEditingFields((prev) => ({
        ...prev,
        [id]: {
          ...(prev[id] || { phone: false, email: false }),
          [field]: !prev[id]?.[field],
        },
      }));
    },
    []
  );

  // Handler to add a new contact row (always blank, no duplicate validation here)
  const handleAddRow = useCallback(() => {
    if (!append) return;
    append({ phone: '', email: '' });
  }, [append]);

  // Ensure there's always at least one empty row
  useEffect(() => {
    if (fields.length === 0) {
      handleAddRow();
    }
  }, [fields.length, handleAddRow]);

  const headers = useMemo<HeaderV2[]>(() => {
    const headers: HeaderV2[] = tableHeaders;
    if (isViewMode) {
      return headers;
    } else {
      return [
        ...headers,
        {
          key: 'action',
          header: (
            <AppIconButton
              onClick={handleAddRow}
              variant="outlined"
              size="small"
              sx={{ minWidth: 24, width: 24, height: 24, p: 0 }}
            >
              <AppIcon icon="mynaui:plus-solid" />
            </AppIconButton>
          ),
          cellProps: {
            align: 'center',
            sx: {
              border: 'none !important',
              backgroundColor: 'white !important',
              width: 36,
              minWidth: 36,
              maxWidth: 36,
              padding: 0,
            },
          },
        },
      ];
    }
  }, [isViewMode, append, fields, handleAddRow]);

  const handlePhoneInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // Only allow numbers and limit to 10 digits
      const value = e.target.value.replace(/\D/g, '').slice(0, 10);
      e.target.value = value;

      if (onPhoneInput) {
        onPhoneInput(e);
      }
    },
    [onPhoneInput]
  );

  const rows: RowV2[] = useMemo(() => {
    if (isViewMode) {
      return contacts.map((contact, index) => ({
        key: index.toString(),
        phoneNumber: {
          value: (
            <div className="text-left text-[14px] w-full">
              {contact?.phone || '-'}
            </div>
          ),
          cellProps: {
            align: 'left',
            sx: {
              textAlign: 'left',
              fontSize: '14px',
              width: '50%',
              minWidth: '180px',
              padding: '6px 10px',
            },
          },
        },
        email: {
          value: (
            <div className="text-left text-[14px] w-full">
              {contact.email || '-'}
            </div>
          ),
          cellProps: {
            align: 'left',
            sx: {
              textAlign: 'left',
              fontSize: '14px',
              borderLeft: '1px solid #E5E7EB',
              width: '50%',
              minWidth: '180px',
              padding: '6px 10px',
            },
          },
        },
      }));
    }
    return (fields || []).map((field: ContactWithId, index) => {
      const fieldId = field.id || index.toString();
      const isPhoneEditable = editingFields[fieldId]?.phone;
      const isEmailEditable = editingFields[fieldId]?.email;

      // Determine if this row contains masked data (contains asterisks)
      const isMasked = field.phone?.includes('*') || field.email?.includes('*');

      return {
        key: fieldId,
        phoneNumber: {
          value: (
            <div className="flex items-center gap-2 w-full">
              <InputBase
                {...register(
                  `contacts.${index}.phone`,
                  isMasked
                    ? {}
                    : {
                        pattern: {
                          value: /^\d{0,10}$/,
                          message: 'Phone number must be 10 digits or less',
                        },
                      }
                )}
                className={`w-full border-none bg-transparent focus:outline-none text-[14px] ${!isNewRow(fieldId) && !isPhoneEditable ? 'text-gray-400' : 'text-black'}`}
                placeholder="9876543210"
                readOnly={!isNewRow(fieldId) && !isPhoneEditable}
                disabled={false}
                onKeyDown={onPhoneKeyDown}
                onChange={handlePhoneInput}
                inputProps={{
                  maxLength: 10,
                  inputMode: 'numeric',
                  pattern: isMasked ? undefined : '[0-9]*',
                  className: 'hover:bg-transparent focus:bg-transparent',
                  readOnly: !isNewRow(fieldId) && !isPhoneEditable,
                }}
                sx={{
                  '&.Mui-disabled': {
                    WebkitTextFillColor: '#9CA3AF !important',
                    '&:hover': {
                      cursor: 'default',
                      backgroundColor: 'transparent !important',
                    },
                  },
                  '&:not(.Mui-disabled)': {
                    WebkitTextFillColor: '#1F2937 !important',
                    '&:hover': {
                      backgroundColor: 'transparent !important',
                    },
                  },
                  '& .MuiInputBase-input': {
                    '&:hover, &:focus': {
                      backgroundColor: 'transparent !important',
                    },
                  },
                }}
              />
              {!isNewRow(fieldId) &&
                !isPhoneEditable &&
                initialValues[fieldId]?.phone && (
                  <button
                    type="button"
                    onClick={() => toggleEditField(fieldId, 'phone')}
                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  >
                    <PenIcon className="w-4 h-4" />
                  </button>
                )}
            </div>
          ),
          cellProps: {
            align: 'left',
            sx: {
              fontSize: '14px',
              width: '50%',
              minWidth: '180px',
              padding: '6px 10px',
            },
          },
        },
        email: {
          value: (
            <div className="flex items-center gap-2 w-full">
              <InputBase
                {...register(`contacts.${index}.email`)}
                className={`w-full border-none bg-transparent focus:outline-none text-[14px] ${!isNewRow(fieldId) && !isEmailEditable && initialValues[fieldId]?.email ? 'text-gray-400' : 'text-black'}`}
                placeholder="<EMAIL>"
                readOnly={
                  !isNewRow(fieldId) &&
                  !isEmailEditable &&
                  !!initialValues[fieldId]?.email
                }
                disabled={false}
                inputProps={{
                  className: 'hover:bg-transparent focus:bg-transparent',
                  readOnly:
                    !isNewRow(fieldId) &&
                    !isEmailEditable &&
                    !!initialValues[fieldId]?.email,
                }}
                sx={{
                  '&.Mui-disabled': {
                    WebkitTextFillColor: '#9CA3AF',
                    '&:hover': {
                      cursor: 'default',
                      backgroundColor: 'transparent !important',
                    },
                  },
                  '&:not(.Mui-disabled)': {
                    WebkitTextFillColor: '#1F2937',
                    '&:hover': {
                      backgroundColor: 'transparent !important',
                    },
                  },
                  '& .MuiInputBase-input': {
                    '&:hover, &:focus': {
                      backgroundColor: 'transparent !important',
                    },
                  },
                }}
              />
              {!isNewRow(fieldId) &&
                !isEmailEditable &&
                initialValues[fieldId]?.email && (
                  <button
                    type="button"
                    onClick={() => toggleEditField(fieldId, 'email')}
                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  >
                    <PenIcon className="w-4 h-4" />
                  </button>
                )}
            </div>
          ),
          cellProps: {
            align: 'left',
            sx: {
              fontSize: '14px',
              borderLeft: '1px solid #E5E7EB',
              width: '50%',
              minWidth: '180px',
              padding: '6px 10px',
            },
          },
        },
        action: {
          value: (
            <AppIconButton
              onClick={() => remove?.(index)}
              variant="outlined"
              size="small"
              sx={{ minWidth: 24, width: 24, height: 24, p: 0 }}
            >
              <AppIcon icon="tabler:minus" />
            </AppIconButton>
          ),
          cellProps: {
            align: 'left',
            sx: {
              border: 'none !important',
              width: 60,
            },
          },
        },
      };
    });
  }, [
    isViewMode,
    contacts,
    fields,
    register,
    remove,
    editingFields,
    toggleEditField,
    initialValues,
  ]);

  // Table styling for edit mode
  const editTableSx = {
    '& .MuiTableCell-root': {
      padding: '6px 10px',
      height: 40,
      fontWeight: 400,
      fontSize: '14px',
      color: '#64707D',
      background: 'white',
      verticalAlign: 'middle',
      '& .MuiInputBase-root': {
        width: '100%',
        '&:hover:not(.Mui-disabled)': {
          backgroundColor: 'transparent',
        },
      },
      '& .MuiInputBase-input': {
        '&:hover': {
          backgroundColor: 'transparent !important',
        },
      },
    },
    '& .MuiTableHead-root .MuiTableCell-root': {
      backgroundColor: '#E6F6FF',
      fontWeight: 500,
      fontSize: '14px',
      color: '#000',
      height: 40,
      padding: '6px 10px',
      '&:hover': {
        backgroundColor: '#E6F6FF',
      },
      '&:first-of-type': {
        borderRight: '1px solid #E5E7EB',
      },
    },
    '& .MuiTableHead-root .MuiTableRow-root': {
      '&:hover': {
        '& .MuiTableCell-root': {
          backgroundColor: '#E6F6FF',
        },
      },
    },
    // Style for action column (last column)
    '& .MuiTableCell-root:last-child': {
      border: 'none !important',
      borderBottom: 'none !important',
      background: 'white',
      width: 60,
      minWidth: 60,
      maxWidth: 60,
      padding: '0 8px',
    },
    '& tr:last-child .MuiTableCell-root:last-child': {
      borderBottom: 'none !important',
    },
    // Style for email column (second column)
    '& .MuiTableCell-root:nth-of-type(2)': {
      borderRight: '1px solid #E5E7EB !important',
      borderBottom: '1px solid #E5E7EB !important',
      width: '50%',
      minWidth: '180px',
      borderLeft: '1px solid #E5E7EB !important',
    },
    // Style for phone column (first column)
    '& .MuiTableCell-root:nth-of-type(1)': {
      borderRight: 'none',
      width: '50%',
      minWidth: '180px',
      position: 'relative',
      '&::after': {
        content: '""',
        position: 'absolute',
        right: 0,
        top: '50%',
        transform: 'translateY(-50%)',
        width: '1px',
        height: '60%',
        backgroundColor: '#E5E7EB',
      },
    },
    // Remove alternate row background
    '& .MuiTableRow-root': {
      background: 'white !important',
      '&:hover': {
        '& .MuiTableCell-root': {
          backgroundColor: '#F9FAFB',
        },
      },
      '&:last-child td': {
        borderBottom: '1px solid #E5E7EB !important',
      },
    },
    // Style for the edit buttons
    '& .edit-button': {
      opacity: 0,
      transition: 'opacity 0.2s ease-in-out',
    },
    '& tr:hover .edit-button': {
      opacity: 1,
    },
  };

  // Table styling for view mode
  const viewTableSx = {
    '& .MuiTableCell-root': {
      padding: '6px 10px',
      height: 40,
      fontWeight: 400,
      fontSize: '14px',
      color: '#64707D',
      background: 'white',
      verticalAlign: 'middle',
    },
    '& .MuiTableHead-root .MuiTableCell-root': {
      backgroundColor: '#DAE1E7',
      fontWeight: 500,
      fontSize: '14px',
      color: '#000',
      height: 40,
      padding: '6px 10px',
    },
    // Style for email column (second column)
    '& .MuiTableCell-root:nth-of-type(2)': {
      width: '50%',
      minWidth: '180px',
    },
    // Style for phone column (first column)
    '& .MuiTableCell-root:nth-of-type(1)': {
      width: '50%',
      minWidth: '180px',
    },
    // Remove alternate row background
    '& .MuiTableRow-root': {
      background: 'white !important',
    },
  };

  return (
    <div className="flex items-start">
      <TableV2
        headers={headers}
        rows={rows}
        tableContainerProps={{
          sx: isViewMode ? viewTableSx : editTableSx,
        }}
      />
    </div>
  );
};

export default ContactsTable;
