import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

export const getModuleSwitchUrl = (
  pathname: string,
  permissions: string[] = []
) => {
  if (pathname.includes('mrd')) {
    const hasEmrDashboardView = permissions.includes(
      PERMISSION_KEYS.EMR_DASHBOARD_VIEW
    );
    return hasEmrDashboardView ? routes.EMR_DASHBOARD : routes.EMR_PATIENT_INFO;
  } else {
    const hasMrdDashboardView = permissions.includes(
      PERMISSION_KEYS.MRD_DASHBOARD_VIEW
    );
    return hasMrdDashboardView
      ? routes.MRD_DASHBOARD
      : routes.MRD_MANAGE_PATIENTS;
  }
};
