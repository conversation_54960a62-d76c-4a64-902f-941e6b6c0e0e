export interface ProratedBillingInfo {
  totalDays: number;
  remainingDays: number;
  usedDays: number;
  paidAmount: number;
  dailyRate: number;
  usedAmount: number;
  remainingAmount: number;
  upgradePlanPrice: number;
  proratedPremiumAmount: number;
  currentPlanName: string;
  upgradePlanName: string;
}

export interface Plan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  subtitle: string;
  features: string[];
  popular?: boolean;
  buttonText: string;
  buttonColor: string;
  savings?: string;
  hasAddOnFeatures?: boolean;
  isOrganizationPlan?: boolean;
  apiPlan?: any;
}

/**
 * Calculate prorated billing information for subscription upgrades
 */
export const getProratedBillingInfo = (
  isFromProfile: boolean,
  isDifferentPlan: boolean,
  subscriberData: any,
  billingCycle: 'monthly' | 'yearly',
  plan: Plan,
  isShowingUpgradePlan: boolean,
  calculatedNextPlan: Plan | null
): ProratedBillingInfo | null => {
  if (
    !isFromProfile ||
    !isDifferentPlan ||
    !subscriberData?.activeSubscription
  ) {
    return null;
  }

  const currentSubscription = subscriberData.activeSubscription;
  const now = new Date();
  const endDate = new Date(currentSubscription.endDate);

  // Calculate total days and remaining days
  const totalDays = currentSubscription.billingType === 'monthly' ? 30 : 365;
  const remainingDays = Math.max(
    0,
    Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  );
  const usedDays = totalDays - remainingDays;

  // Mock paid amount (will be replaced with backend field)
  const paidAmount = currentSubscription.paymentAmount || 0; // Mock: assuming they paid ₹100

  // Calculate daily rate
  const dailyRate = paidAmount / totalDays;

  // Calculate used amount and remaining amount
  const usedAmount = dailyRate * usedDays;
  const remainingAmount = dailyRate * remainingDays;

  // Calculate upgrade plan price
  const upgradePlanPrice =
    isShowingUpgradePlan && calculatedNextPlan
      ? billingCycle === 'monthly'
        ? (calculatedNextPlan.apiPlan?.totalMonthlyBasicAmount ??
          calculatedNextPlan.monthlyPrice)
        : (calculatedNextPlan.apiPlan?.totalYearlyBasicAmount ??
          calculatedNextPlan.yearlyPrice)
      : billingCycle === 'monthly'
        ? (plan.apiPlan?.totalMonthlyBasicAmount ?? plan.monthlyPrice)
        : (plan.apiPlan?.totalYearlyBasicAmount ?? plan.yearlyPrice);

  // Calculate prorated premium amount (upgrade amount minus remaining credit)
  const proratedPremiumAmount = Math.max(0, upgradePlanPrice - remainingAmount);

  return {
    totalDays,
    remainingDays,
    usedDays,
    paidAmount,
    dailyRate,
    usedAmount,
    remainingAmount,
    upgradePlanPrice,
    proratedPremiumAmount,
    currentPlanName: currentSubscription.planName,
    upgradePlanName:
      isShowingUpgradePlan && calculatedNextPlan
        ? calculatedNextPlan.name
        : plan.name,
  };
};
