import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

const {
  PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS,
  NUTRITION_KNOWLEDGE,
  PHYSICAL_ACTIVITY_ATTITUDE,
  PHYSICAL_ACTIVITY_KNOWLEDGE,
  NUTRITION_ATTITUDE,
  NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS,
  NUTRITION_PRACTICE_DIETARY_RECALL,
  NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE,
} = LifestyleSources;

/**
 * Checks if an entry exists for today for the given source and data
 * @param source - The lifestyle source to check
 * @param dataMap - Object containing all the data arrays for different sources
 * @returns boolean - true if an entry exists for today, false otherwise
 */
export const checkTodayEntryExists = (
  source: LifestyleSources | null,
  dataMap: {
    exerciseData?: any[];
    physicalAttitudeData?: any[];
    nutritionAttitudeData?: any[];
    knowledgeData?: any[];
    nutritionKnowledgeData?: any[];
    foodIntakeData?: any[];
    dietaryRecallData?: any[];
    foodFrequencyData?: any[];
  }
): boolean => {
  if (
    source !== PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS &&
    source !== NUTRITION_KNOWLEDGE &&
    source !== PHYSICAL_ACTIVITY_ATTITUDE &&
    source !== PHYSICAL_ACTIVITY_KNOWLEDGE &&
    source !== NUTRITION_ATTITUDE &&
    source !== NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS &&
    source !== NUTRITION_PRACTICE_DIETARY_RECALL &&
    source !== NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE
  ) {
    return false;
  }

  const today = new Date().toISOString().split('T')[0];

  // Helper function to check if any entry exists for today
  const hasEntryToday = (data: any[] = []) => {
    return data.some((entry) => {
      if (!entry.created_on) return false;
      const entryDate = new Date(entry.created_on).toISOString().split('T')[0];
      return entryDate === today;
    });
  };

  switch (source) {
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return hasEntryToday(dataMap.exerciseData);
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return hasEntryToday(dataMap.physicalAttitudeData);
    case NUTRITION_ATTITUDE:
      return hasEntryToday(dataMap.nutritionAttitudeData);
    case PHYSICAL_ACTIVITY_KNOWLEDGE:
      return hasEntryToday(dataMap.knowledgeData);
    case NUTRITION_KNOWLEDGE:
      return hasEntryToday(dataMap.nutritionKnowledgeData);
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return hasEntryToday(dataMap.foodIntakeData);
    case NUTRITION_PRACTICE_DIETARY_RECALL:
      return hasEntryToday(dataMap.dietaryRecallData);
    case NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE:
      return hasEntryToday(dataMap.foodFrequencyData);
    default:
      return false;
  }
};
