'use client';

import useIsMobile from '@/hooks/use-mobile-layout';

import ProfileSidebar from '@/views/emr/doctor-profile/profile-sidebar';

import TopNavigation from '@/core/layout/emr/mobile/TopNavigation';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <>
        <TopNavigation />
        {children}
      </>
    );
  }

  return (
    <div className="grid grid-cols-9 gap-base w-full h-full overflow-hidden">
      <ProfileSidebar />
      {children}
    </div>
  );
}
