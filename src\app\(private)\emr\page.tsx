'use client';

import { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import Loading from '@/lib/common/loading';

import { useUserStore } from '@/store/userStore';

import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

export default function EmrPage() {
  const router = useRouter();
  const { permissions = [] } = useUserStore();

  useEffect(() => {
    // Check if user has EMR dashboard permission
    const hasEmrDashboardView = permissions.includes(
      PERMISSION_KEYS.EMR_DASHBOARD_VIEW
    );

    if (hasEmrDashboardView) {
      router.replace(routes.EMR_DASHBOARD);
    } else {
      router.replace(routes.EMR_PATIENT_INFO);
    }
  }, [router, permissions]);

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-gray-500">
        <Loading />
      </div>
    </div>
  );
}
