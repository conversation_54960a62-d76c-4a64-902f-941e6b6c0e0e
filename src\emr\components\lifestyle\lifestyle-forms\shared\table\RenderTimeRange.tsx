import { useRef } from 'react';

import { Path, useFormContext, useWatch } from 'react-hook-form';

import { FaClock } from 'react-icons/fa';

import colors from '@/utils/colors';

import { LifestyleQuestion, SubQuestions } from '@/emr/types/lifestyle';

import ControlledInput from '../ControlledInput';

type FieldProps = {
  appendName: Path<LifestyleQuestion>;
  isReadOnly?: boolean;
  isMaximized?: boolean;
};

const RenderTimeRange = ({
  appendName,
  isMaximized,
  isReadOnly,
}: FieldProps) => {
  const { control } = useFormContext();
  const containerRef = useRef<HTMLDivElement>(null);

  const fields = useWatch({ control, name: appendName });

  const handleClockClick = (index: number, type: 'from' | 'to') => {
    if (isReadOnly) return;
    const inputName = `${appendName}.${index}.value.${type}`;

    let inputElement = containerRef.current?.querySelector(
      `input[name="${inputName}"]`
    ) as HTMLInputElement;

    if (!inputElement) {
      const timeInputs =
        containerRef.current?.querySelectorAll('input[type="time"]');
      const targetIndex = type === 'from' ? index * 2 : index * 2 + 1;
      inputElement = timeInputs?.[targetIndex] as HTMLInputElement;
    }

    if (inputElement) {
      inputElement.focus();

      if (inputElement.showPicker) {
        try {
          inputElement.showPicker();
        } catch (_error) {
          console.warn('showPicker not supported, falling back to click');
          inputElement.click();
        }
      } else {
        inputElement.click();
      }
    } else {
      console.error('Could not find input element for:', inputName);
    }
  };

  const createClickableClockIcon = (index: number, type: 'from' | 'to') => (
    <div
      style={{
        cursor: isReadOnly ? 'default' : 'pointer',
        display: 'inline-block',
        padding: '2px',
      }}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();

        handleClockClick(index, type);
      }}
    >
      <FaClock
        style={{
          marginTop: 8,
          fontSize: 16,
          color: 'black',
          pointerEvents: 'none',
        }}
      />
    </div>
  );

  return (
    <div className="flex flex-col gap-2" ref={containerRef}>
      {fields?.map((q: SubQuestions, index: number) => (
        <div key={index} className="flex gap-2 items-center">
          <ControlledInput
            control={control}
            name={`${appendName}.${index}.value.from`}
            type="time"
            className="w-full"
            iconClassName="right-[0px]"
            endDecoration={createClickableClockIcon(index, 'from')}
            disabled={isReadOnly}
            variant={isMaximized ? 'transparent' : 'white'}
            showTooltip={false}
            inputClassName={
              isMaximized
                ? `!w-full border border-[${colors.text.slateGray}] rounded-md min-w-[100px]`
                : '!w-full min-w-[85px]'
            }
          />
          <span>to</span>
          <ControlledInput
            control={control}
            name={`${appendName}.${index}.value.to`}
            type="time"
            className="w-full"
            iconClassName="right-[0px]"
            disabled={isReadOnly}
            variant={isMaximized ? 'transparent' : 'white'}
            showTooltip={false}
            endDecoration={createClickableClockIcon(index, 'to')}
            inputClassName={
              isMaximized
                ? `!w-full border border-[${colors.text.slateGray}] rounded-md min-w-[100px]`
                : '!w-full min-w-[85px]'
            }
          />
        </div>
      ))}
    </div>
  );
};

export default RenderTimeRange;
