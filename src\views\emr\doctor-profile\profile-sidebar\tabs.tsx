import { ButtonBase } from '@mui/material';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { routes } from '@/constants/routes';

export default function Tabs() {
  const path = usePathname();
  const { doctorProfile } = useDoctorStore();
  const { data: userStoreData } = useUserStore();

  const isClinicUser = userStoreData?.accountType === 'clinic';

  const tabs: Array<{ label: string; path: string; disabled?: boolean }> = [
    {
      label: 'Personal Info',
      path: `${routes.EMR_PROFILE}/personal-info`,
    },
    {
      label: 'Customise EMR',
      path: '/emr/profile/customise',
      disabled: !Boolean(doctorProfile?.id),
    },
    ...(isClinicUser
      ? [
          {
            label: 'Payments & Plans',
            path: '/emr/profile/payments-plans',
            disabled: false,
          },
        ]
      : []),
    // TODO : Enable this when calendar is ready
    // {
    //   label: 'Calendar',
    //   path: '',
    //   disabled: true,
    // },
  ];

  return (
    <div className="flex flex-col gap-2.5 px-2.5 ">
      {tabs.map((tab) => (
        <ButtonBase
          LinkComponent={Link}
          key={tab.label}
          className={cn(
            'tab-button flex items-center justify-start w-full !rounded-lg h-12 xl:h-15 overflow-hidden disabled:*:text-gray-400 disabled:*:cursor-not-allowed',
            path === tab.path && 'bg-primary text-white'
          )}
          href={tab.path}
          disabled={tab.disabled}
        >
          <div
            className={cn(
              'flex items-center gap-2.5 w-full h-full pl-6 tab-button border-[2px] border-[#DAE1E7] rounded-lg',
              {
                'text-gray-400 cursor-not-allowed': tab?.disabled,
                'text-white bg-primary': path === tab.path,
              }
            )}
          >
            <div
              className={cn(
                'h-5 w-5 xl:h-5 border-black border-2 rounded-full',
                path === tab.path && 'border-white',
                {
                  'border-gray-400 cursor-not-allowed': tab?.disabled,
                }
              )}
            ></div>
            <div className="font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl">
              {tab.label}
            </div>
          </div>
        </ButtonBase>
      ))}
    </div>
  );
}
