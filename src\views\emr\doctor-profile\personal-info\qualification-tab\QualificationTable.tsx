import React, { FC, memo, useMemo } from 'react';

import { Control, FieldArrayWithId } from 'react-hook-form';

import {
  useEnterKeyNavigation,
  createArrayFieldNavigation,
} from '@/hooks/useEnterKeyNavigation';

import {
  allowNumbersTextAndDot,
  allowOnlyNumbers,
  preventNonAlphabeticInput,
  restrictMaxLength,
} from '@/utils/validation';

import { qualificationHeaders } from '@/constants/emr/doctor-profile/personal-info';

import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import {
  actionButtonCellProps,
  getFileCellProps,
  getInputCellProps,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import ActionButton from '../shared/ActionButton';
import TableFilePicker from '../shared/TableFilePicker';
import TableTextarea from '../shared/TableTextarea';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'qualifications', 'id'>[];
  control: Control<FormData>;
  handleItemEdit: (_index: number) => () => void;
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
  itemToEdit: number | null;
};

const QualificationTable: FC<Props> = ({
  control,
  fields,
  itemToEdit,
  handleItemEdit,
  handleOnDelete,
}) => {
  // Create field order for all qualification entries
  const allFieldOrder = useMemo(() => {
    const allFields: string[] = [];
    fields.forEach((_, index) => {
      const baseFieldOrder = [
        'degree',
        'specialization',
        'university',
        'institute',
        'duration',
        'yearOfCompletion',
        'marks',
      ];
      const fieldOrder = createArrayFieldNavigation(
        baseFieldOrder,
        index,
        'qualifications'
      );
      allFields.push(...fieldOrder);
    });
    return allFields;
  }, [fields.length]);

  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder: allFieldOrder,
    shouldTriggerSave: (fieldName: string) => {
      // Trigger save on the last field (marks) of the last qualification
      return (
        fieldName.includes('marks') &&
        fieldName.includes(`${fields.length - 1}`)
      );
    },
  });
  const rows = useMemo<Row[]>(
    () =>
      fields?.map((field, index) => ({
        key: field.id,
        qualification: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.degree`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.degree`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        specialization: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.specialization`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.specialization`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        university: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.university`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.university`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        institute: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.institute`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.institute`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        duration: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.duration`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                allowNumbersTextAndDot(e);
                handleKeyDown(e, `qualifications.${index}.duration`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        yearOfCompletion: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.yearOfCompletion`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) => {
                allowOnlyNumbers(e);
                restrictMaxLength(4)(e);
                handleKeyDown(e, `qualifications.${index}.yearOfCompletion`);
              }}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        marks: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.marks`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={(e) =>
                handleKeyDown(e, `qualifications.${index}.marks`, {
                  shouldTriggerSave: index === fields.length - 1,
                })
              }
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        doc1: {
          value: (
            <TableFilePicker
              control={control}
              name={`qualifications.${index}.doc1`}
              disabled={itemToEdit !== index}
              imageUrl={field?.doc1}
            />
          ),
          cellProps: getFileCellProps(itemToEdit !== index),
        },
        doc2: {
          value: (
            <TableFilePicker
              control={control}
              name={`qualifications.${index}.doc2`}
              disabled={itemToEdit !== index}
              imageUrl={field?.doc2}
            />
          ),
          cellProps: getFileCellProps(itemToEdit !== index),
        },
        status: {
          value: (
            <TableTextarea
              name={`qualifications.${index}.status`}
              control={control}
              disabled={itemToEdit !== index}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        edit: {
          value: itemToEdit !== index && (
            <ActionButton actionFor="edit" onClick={handleItemEdit(index)} />
          ),
          cellProps: actionButtonCellProps,
        },
        delete: {
          value: (
            <ActionButton
              actionFor="delete"
              onClick={() => handleOnDelete({ index, uuId: field?.uuId })}
            />
          ),
          cellProps: actionButtonCellProps,
        },
      })),
    [fields, control, handleOnDelete, itemToEdit, handleItemEdit]
  );

  return (
    <Table
      headers={qualificationHeaders}
      rows={rows}
      tableContainerProps={{
        sx: {
          '& tbody td': {
            minHeight: 30,
          },
        },
      }}
    />
  );
};

export default memo(QualificationTable);
