import { getAppointmentId } from '@/utils/mrd/manage-patient/get-appointment-id';

import {
  AppointmentStatus,
  Department,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

import { getCurrentTimeInString } from '@/core/components/app-time-picker/utils';

export type Doctors = {
  id: string;
  name: string;
  email: string;
  userType: string;
  userRole: string;
  organizationId: string;
  isActive: boolean;
  consultationFee?: number;
};

export type Consultation = {
  doctorId: string;
  patientId: string;
  date: string;
  time: string;
  department: Department;
  status: `${AppointmentStatus}-${PatientStatus}`;
  id: string;
  appointmentId?: string;
  queueId?: string;
  type?: string;
  paymentStatus?: string;
  consultationFee?: number | null;
};

export type ConsultationForm = {
  doctorId: Doctors | null;
  patientId: string;
  date: string;
  time: string;
  department: Department;
  status?: `${AppointmentStatus}-${PatientStatus}`;
  id?: string;
  type: string;
  paymentStatus?: string;
  consultationFee?: number | null;
  queueId?: string;
  paymentId?: string; // Add paymentId for payment tracking
};

export const defaultConsultation: ConsultationForm = {
  doctorId: null,
  patientId: '',
  date: '',
  time: getCurrentTimeInString(),
  department: Department.InPatient,
  id: getAppointmentId(),
  type: 'new',
};
