/**
 * Utility function to check if HTML content is empty (contains no meaningful data)
 * Used for detecting empty HTML patterns in consultation forms
 */

export const isEmptyHtmlContent = (htmlString: string): boolean => {
  if (!htmlString || typeof htmlString !== 'string') {
    return true;
  }

  // Remove all whitespace and convert to lowercase for comparison
  const cleanHtml = htmlString.replace(/\s+/g, '').toLowerCase();

  // Check for empty list patterns
  const emptyListPatterns = [
    '<ul><li></li></ul>',
    '<ul><li/></ul>',
    '<ol><li></li></ol>',
    '<ol><li/></ol>',
  ];

  // Check for empty block group patterns
  const emptyBlockPatterns = [
    '<divclass="bn-block-group"data-node-type="blockgroup"></div>',
    '<divclass="bn-block-group"data-node-type="blockgroup"/>',
  ];

  // Check for any of the empty patterns
  const isEmptyList = emptyListPatterns.some((pattern) =>
    cleanHtml.includes(pattern.replace(/\s+/g, ''))
  );

  const isEmptyBlock = emptyBlockPatterns.some((pattern) =>
    cleanHtml.includes(pattern.replace(/\s+/g, ''))
  );

  // Also check for completely empty tags or tags with only whitespace
  const emptyTagPattern =
    /<(\w+)[^>]*>\s*<\/\1>|<(\w+)[^>]*\/>|<(\w+)[^>]*>\s*$/i;
  const hasOnlyEmptyTags = emptyTagPattern.test(cleanHtml);

  return isEmptyList || isEmptyBlock || hasOnlyEmptyTags;
};
