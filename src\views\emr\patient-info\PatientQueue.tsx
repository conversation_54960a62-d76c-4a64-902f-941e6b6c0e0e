import { memo, useState, useCallback, useRef, useEffect } from 'react';

import SelectConsultationDate from '@/lib/common/select_consultation_date';
import QueueList from '@/lib/queue_list';
import QueueCard, { QueueCardProps } from '@/lib/QueueCard';

import { useUserStore } from '@/store/userStore';

import PlayArrow from '@/assets/svg/PlayArrow';

interface PatientQueueProps {
  handleMarkAsConsulted: (data: QueueCardProps) => void;
  handleMoveAppointmentToQueue: (data: QueueCardProps) => void;
  handleMoveAppointmentToConsulting: (data: QueueCardProps) => void;
  handleCancelAppointment: (data: QueueCardProps) => void;
  handleChangeOrder: (data: QueueCardProps[]) => void;
  handleSelectConsultationDate: (date: Date) => void;
  loadPatientQueueStatus: string;
  onSelectPatient?: (item: QueueCardProps | null) => void;
  selectedPatientId?: string | null;
  onSearch?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  searchQuery?: string;

  // Pagination props
  totalItems?: number;
  currentPage?: number;
  pageSize?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  handleClickNextPatient: () => void;
  isPatientFromQueue?: boolean;
}

const PatientQueue: React.FC<PatientQueueProps> = ({
  handleMarkAsConsulted,
  handleMoveAppointmentToQueue,
  handleMoveAppointmentToConsulting,
  handleCancelAppointment,
  handleSelectConsultationDate,
  loadPatientQueueStatus,
  onSelectPatient,
  selectedPatientId: externalSelectedPatient,
  onSearch,
  searchQuery = '',
  handleClickNextPatient,
  isPatientFromQueue = false,

  // Pagination props with defaults
  totalItems = 0,
  currentPage = 1,
  pageSize = 10,
  totalPages = 1,
  onPageChange = () => {},
  onPageSizeChange = () => {},
}) => {
  const { appointments, currentAppointment, queueFilter, queueInfo } =
    useUserStore();
  const [internalSelectedPatientId, setInternalSelectedPatientId] = useState<
    string | null
  >(null);

  // Use the internal state if no external selectedPatientId is provided
  const selectedPatientId =
    externalSelectedPatient !== undefined
      ? externalSelectedPatient
      : internalSelectedPatientId;

  const queueListRef = useRef<HTMLDivElement>(null);

  const handleSelectPatient = useCallback(
    (item: QueueCardProps) => {
      // Always update the internal state immediately for instant UI feedback
      setInternalSelectedPatientId(item.queueId);

      // Call the parent's onSelectPatient
      if (onSelectPatient) {
        onSelectPatient(item);
      }
    },
    [onSelectPatient]
  );

  // Clear selection when queue changes
  useEffect(() => {
    setInternalSelectedPatientId(null);
  }, [appointments]);

  return (
    <div
      className="w-[37%] 2xl:w-[33%] h-full bg-white overflow-hidden  rounded-base flex flex-col p-4"
      ref={queueListRef}
    >
      <div className="w-full flex justify-between max-h-10 overflow-hidden ">
        <div className="flex justify-between gap-6 items-center flex-grow ">
          <div className="border border-[#DAE1E7] rounded-lg flex-grow flex justify-evenly items-center gap-5 py-1 px-4 max-h-full">
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="font-light -tracking-[2.2%] text-[#637D92] text-[13px] xl:text-base">
                In Queue
              </span>
              <span className="font-medium text-black text-[13px] xl:text-base">
                {queueInfo.inQueue}
              </span>
            </div>

            <div className="border flex-1 bg-black rounded-full h-5 min-w-[1px] max-w-[1px] flex-shrink-0"></div>

            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="font-light -tracking-[2.2%] text-[#637D92] text-[13px] xl:text-base">
                Completed
              </span>
              <span className="font-medium text-black text-[13px] xl:text-base">
                {queueInfo.completed}
              </span>
            </div>
          </div>
          <button
            className="flex items-center justify-center gap-2 py-1.5 px-5 text-white rounded-full transition-colors bg-[#012436] hover:opacity-90 flex-shrink-0 max-h-full"
            onClick={handleClickNextPatient}
          >
            <span className="text-[16px] font-medium whitespace-nowrap">
              Next Patient
            </span>
            <div className="w-2">
              <PlayArrow />
            </div>{' '}
          </button>
        </div>
      </div>
      <div className="rounded-lg py-3  bg-[#FCFCFC] h-full relative overflow-hidden ">
        <div className="grid grid-cols-12 gap-8 mb-2">
          <div className="col-span-5 text-[16px] font-medium text-[#637D92] -tracking-[2.2%] ">
            Consulting Now{' '}
          </div>

          <div className="col-span-7 grid grid-cols-7">
            <div className="col-span-7 flex justify-end">
              <SelectConsultationDate
                value={queueFilter.date}
                onChange={handleSelectConsultationDate}
              />
            </div>
          </div>
        </div>

        {currentAppointment ? (
          <QueueCard
            {...(currentAppointment as any)}
            status={currentAppointment.status}
            onClickMarkAsConsulted={handleMarkAsConsulted}
            onMoveDown={handleMoveAppointmentToQueue}
          />
        ) : (
          <div className="current-selected-placeholder mt-5 w-full h-10 rounded-md bg-[#DAE1E7] border border-[#323F4940]"></div>
        )}

        <div className="h-[calc(67vh-2rem)] overflow-hidden ">
          <QueueList
            queue={appointments as any[]}
            isLoading={loadPatientQueueStatus === 'loading'}
            onStartConsultation={handleMoveAppointmentToConsulting}
            onCancelAppointment={handleCancelAppointment}
            onMarkAsConsulted={handleMarkAsConsulted}
            onSelectPatient={handleSelectPatient}
            selectedPatientId={selectedPatientId}
            isPatientFromQueue={isPatientFromQueue}
            searchQuery={searchQuery}
            onSearch={onSearch}
            totalItems={totalItems}
            currentPage={currentPage}
            pageSize={pageSize}
            totalPages={totalPages}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
            hasConsultingNow={!!currentAppointment}
          />
        </div>
      </div>
    </div>
  );
};

export default memo(PatientQueue);
