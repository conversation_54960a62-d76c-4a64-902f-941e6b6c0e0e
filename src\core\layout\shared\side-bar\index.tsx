'use client';

import React, { memo, useMemo } from 'react';

import clsx from 'clsx';

import usePermission from '@/hooks/use-permission';

import NavLink from './NavLink';
import type { SidebarProps } from './types';

const Sidebar = ({
  items = [],
  highlightColor,
  renderBottom,
}: SidebarProps) => {
  const { hasPermission, hasDepartment } = usePermission();

  const processedItems = useMemo(() => {
    return items.map((item) => {
      // Check department permissions (for MRD, we want all items to show)
      const hasDepartmentAccess =
        !item?.department ||
        item.department.length === 0 ||
        hasDepartment(item.department);

      // Check permission keys
      const hasPermissionAccess =
        !item?.permissions ||
        item.permissions.length === 0 ||
        hasPermission(item.permissions);

      // The item is disabled if no permission access (locked icon)
      const isLocked = !hasPermissionAccess;

      // For MRD navigation, always show locked icons for unauthorized features
      // The menu item is always visible, but locked if no permission access
      const showLocked = isLocked; // Always show locked icon when no permission

      return {
        ...item,
        disabled: item.disabled || false,
        isLocked: isLocked,
        showLocked: showLocked,
      };
    });
  }, [items, hasDepartment, hasPermission]);

  return (
    <div
      className={clsx(
        'flex flex-col flex-grow-0 flex-shrink-0',
        'bg-white rounded-base shadow-base',
        'h-full min-h-full w-17 min-w-17 max-w-17',
        'overflow-y-auto overflow-x-hidden'
      )}
    >
      <div
        className={clsx(
          'flex flex-col flex-shrink-0 flex-grow-0 items-center',
          'w-full min-w-full max-w-full'
        )}
      >
        {processedItems.map((item) => (
          <NavLink
            key={item.path}
            icon={item.icon}
            href={item.path}
            text={item?.label}
            disabled={item.disabled || false}
            isLocked={item.isLocked}
            showLocked={item.showLocked}
            highlightColor={highlightColor}
          />
        ))}
      </div>
      {renderBottom && renderBottom()}
    </div>
  );
};

export default memo(Sidebar);
