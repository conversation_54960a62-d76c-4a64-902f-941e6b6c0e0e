import React, { useState, forwardRef } from 'react';

import { <PERSON>Field, InputAdornment, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';

// Eye icon SVG components
const EyeIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"
      fill="#9CA3AF"
    />
  </svg>
);

const EyeOffIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"
      fill="#9CA3AF"
    />
  </svg>
);

// Styled TextField with bottom border only
const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      border: 'none',
      borderBottom: '1px solid #D1D5DB',
    },
    '&:hover fieldset': {
      borderBottom: '1px solid #9CA3AF',
    },
    '&.Mui-focused fieldset': {
      borderBottom: '2px solid #000000',
    },
    borderRadius: 0,
    paddingLeft: 0,
    paddingRight: 0,
  },
  '& .MuiInputBase-input': {
    padding: '12px 0',
    fontSize: '16px',
    color: '#374151',
    '&::placeholder': {
      color: '#9CA3AF',
      opacity: 1,
    },
  },
  '& .MuiInputLabel-root': {
    color: '#9CA3AF',
    fontSize: '16px',
    transform: 'translate(0, 12px) scale(1)',
    '&.Mui-focused': {
      color: '#000000',
      transform: 'translate(0, -20px) scale(0.75)',
    },
    '&.MuiInputLabel-shrink': {
      transform: 'translate(0, -20px) scale(0.75)',
    },
  },
}));

export interface SignupTextFieldProps {
  label?: string;
  value?: string;
  onChange?: (value: string) => void;
  type?: string;
  placeholder?: string;
  fullWidth?: boolean;
  required?: boolean;
  showPasswordToggle?: boolean;
}

const SignupTextField = forwardRef<HTMLDivElement, SignupTextFieldProps>(
  (
    {
      label,
      value = '',
      onChange,
      type = 'text',
      placeholder,
      fullWidth = true,
      required = false,
      showPasswordToggle = false,
      ...rest
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const [internalValue, setInternalValue] = useState(value);

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setInternalValue(newValue);
      onChange?.(newValue);
    };

    const handleTogglePassword = () => {
      setShowPassword(!showPassword);
    };

    const inputType = showPasswordToggle
      ? showPassword
        ? 'text'
        : 'password'
      : type;

    return (
      <StyledTextField
        ref={ref}
        label={label}
        value={internalValue}
        onChange={handleChange}
        type={inputType}
        placeholder={placeholder}
        fullWidth={fullWidth}
        required={required}
        variant="outlined"
        InputProps={{
          endAdornment: showPasswordToggle ? (
            <InputAdornment position="end">
              <IconButton
                onClick={handleTogglePassword}
                edge="end"
                aria-label="toggle password visibility"
                sx={{ color: '#9CA3AF' }}
              >
                {showPassword ? <EyeIcon /> : <EyeOffIcon />}
              </IconButton>
            </InputAdornment>
          ) : undefined,
        }}
        {...rest}
      />
    );
  }
);

SignupTextField.displayName = 'SignupTextField';

export default SignupTextField;
