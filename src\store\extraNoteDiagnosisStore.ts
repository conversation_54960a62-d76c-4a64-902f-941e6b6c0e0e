import { create } from 'zustand';

import {
  getExtraNoteDiagnosis,
  createExtraNoteDiagnosis,
  updateExtraNoteDiagnosis,
} from '@/query/consultation/extraNoteSection';

import { ModeType, noteModes } from '@/utils/constants/consultation';

export interface DiagnosisRecord {
  record_id: string;
  field: string | null;
  content: string;
  doctor_id: string | undefined;
  timestamp: string;
  version: number;
  status: string;
  diagnosisStatus: 'provisional' | 'confirmed';
  activityStatus: 'active' | 'inactive';
}

export interface DiagnosisRecords {
  id?: string;
  records?: DiagnosisRecord[];
  created_on?: string;
}

interface DiagnosisState {
  records: DiagnosisRecords | null;
  loading: boolean;
  error: string | null;
  mode: ModeType;
  selectedRecord: DiagnosisRecord | null;
  diagnosisStatusFilter: 'provisional' | 'confirmed' | 'all';
  activityStatusFilter: 'active' | 'inactive' | 'all';

  getExtraNotes: (
    _patientId: string,
    _diagnosisStatus?: 'provisional' | 'confirmed',
    _activityStatus?: 'active' | 'inactive'
  ) => Promise<void>;
  addExtraNote: (
    _patientId: string,
    _data: Omit<DiagnosisRecords, 'id' | 'created_on'>
  ) => Promise<void>;
  updateExtraNote: (
    _id: string,
    _data: Partial<DiagnosisRecords>
  ) => Promise<void>;
  setMode: (_mode: ModeType) => void;
  setSelectedRecord: (_record: DiagnosisRecord | null) => void;
  setDiagnosisStatusFilter: (
    _filter: 'provisional' | 'confirmed' | 'all'
  ) => void;
  setActivityStatusFilter: (_filter: 'active' | 'inactive' | 'all') => void;
}

export const useDiagnosisStore = create<DiagnosisState>((set) => ({
  records: null,
  loading: false,
  error: null,
  mode: noteModes.NONE,
  selectedRecord: null,
  diagnosisStatusFilter: 'all',
  activityStatusFilter: 'all',

  getExtraNotes: async (patientId, diagnosisStatus, activityStatus) => {
    try {
      const response = await getExtraNoteDiagnosis(
        patientId,
        diagnosisStatus,
        activityStatus
      );

      if (Array.isArray(response.data)) {
        const allRecords: DiagnosisRecord[] = [];

        response.data.forEach((diagnosisGroup: DiagnosisRecords) => {
          if (diagnosisGroup.records) {
            allRecords.push(...diagnosisGroup.records);
          }
        });

        allRecords.sort(
          (a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );

        const mainDiagnosisNote = response.data[0];

        const combinedRecords: DiagnosisRecords = {
          id: mainDiagnosisNote?.id || 'new',
          records: allRecords,
          created_on: mainDiagnosisNote?.created_on || new Date().toISOString(),
        };

        set({ records: combinedRecords });
      } else {
        set({ records: response.data });
      }
    } catch (error: any) {
      console.error('Error fetching diagnosis notes:', error);
      set({ error: error.message });
    }
  },

  addExtraNote: async (patientId, data) => {
    set({ loading: true, error: null });
    try {
      const response = await createExtraNoteDiagnosis(data, patientId);

      set({ loading: false });
    } catch (error: any) {
      console.error('Error creating diagnosis note:', error);
      set({ error: error.message, loading: false });
    }
  },

  updateExtraNote: async (id, data) => {
    set({ loading: true, error: null });
    try {
      const response = await updateExtraNoteDiagnosis(id, data);

      set({ loading: false });
    } catch (error: any) {
      console.error('Error updating diagnosis note:', error);
      set({ error: error.message, loading: false });
    }
  },

  setMode: (mode) => set({ mode }),
  setSelectedRecord: (record) => set({ selectedRecord: record }),
  setDiagnosisStatusFilter: (filter) => set({ diagnosisStatusFilter: filter }),
  setActivityStatusFilter: (filter) => set({ activityStatusFilter: filter }),
}));
