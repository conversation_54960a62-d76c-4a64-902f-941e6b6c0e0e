import { getOrDefault } from '@/utils/common';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import PrintButton from '@/views/emr/lab/print-button';
import StatusIndicator, {
  getBgColor,
} from '@/views/emr/lab/test-result/StatusIndicator';

import { Row } from '@/core/components/table/types';
import { TestResultItem } from '@/types/emr/lab';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const getTableBgColor = (i: number) => {
  return i % 2 === 0 ? '#C2CDD6 !important' : '#FFFFFF';
};

const getDummyRow = (i: number) => ({
  value: undefined,
  returnNullForEmpty: true,
  cellProps: { sx: { bgcolor: getTableBgColor(i) } },
});

type GetLabTestsTableRowsProps = {
  testResult: TestResultItem[];
  fetchTestResult?: () => void;
};

export const getLabTestsTableRows = ({
  testResult,
  fetchTestResult,
}: GetLabTestsTableRowsProps): Row[] => {
  if (!testResult?.length) return [];

  const rows: Row[] = [];

  testResult.forEach((item, groupIndex) => {
    // 1. Gather all tests under this date group with department info
    const testsFlat: Array<{ test: any; department: string }> = [];
    Object.entries(item.labTests).forEach(([department, tests]) => {
      tests.forEach((test) => {
        testsFlat.push({ test, department });
      });
    });
    const totalTests = testsFlat.length;
    // 2. Sort by status so same statuses are adjacent in date group
    testsFlat.sort((a, b) =>
      (a.test.status || '').localeCompare(b.test.status || '')
    );

    // 3. Build table rows merging date/status appropriately
    let isFirstDateRow = true;
    let j = 0;
    while (j < testsFlat.length) {
      const currentStatus = testsFlat[j].test.status;
      // Find length of this status-run
      let runLength = 1;
      while (
        j + runLength < testsFlat.length &&
        testsFlat[j + runLength].test.status === currentStatus
      ) {
        runLength++;
      }
      // For each row in this run
      for (let k = 0; k < runLength; k++) {
        const flatIndex = j + k;
        // Only merge department if the department is contiguous, else show per row
        const isFirstDeptRow =
          flatIndex === 0 ||
          testsFlat[flatIndex].department !==
            testsFlat[flatIndex - 1].department;
        // Count how many times this department appears in contiguous block (preserve merge for blocks)
        let deptRunLen = 1;
        while (
          flatIndex + deptRunLen < testsFlat.length &&
          testsFlat[flatIndex + deptRunLen].department ===
            testsFlat[flatIndex].department
        ) {
          deptRunLen++;
        }
        const testRow: Row = {
          date: isFirstDateRow
            ? {
                value: formatDate(item?.created_on, DATE_DD_MM_YYYY_SLASH),
                cellProps: {
                  rowSpan: totalTests,
                  sx: { bgcolor: getTableBgColor(groupIndex) },
                },
              }
            : getDummyRow(groupIndex),
          department: isFirstDeptRow
            ? {
                value: testsFlat[flatIndex].department,
                cellProps: {
                  rowSpan: deptRunLen,
                  sx: { bgcolor: getTableBgColor(groupIndex) },
                },
              }
            : getDummyRow(groupIndex),
          status:
            k === 0
              ? {
                  value: (
                    <StatusIndicator
                      status={currentStatus}
                      id={item.id}
                      fetchTestResult={fetchTestResult}
                      fileMetadata={testsFlat[flatIndex].test.fileMetadata}
                      testName={testsFlat[flatIndex].test.testName}
                    />
                  ),
                  cellProps: {
                    rowSpan: runLength,
                    sx:
                      currentStatus === 'Ready'
                        ? { bgcolor: getBgColor('Ready') }
                        : {},
                  },
                }
              : getDummyRow(groupIndex),
          expand:
            k === 0
              ? {
                  value: <PrintButton disabled={false} testItem={item} />,
                  cellProps: {
                    rowSpan: runLength,
                    sx: { bgcolor: getTableBgColor(groupIndex) },
                  },
                }
              : getDummyRow(groupIndex),
          testName: {
            value: getOrDefault(testsFlat[flatIndex].test.testName, '-'),
            cellProps: {
              sx: { bgcolor: getTableBgColor(groupIndex), width: 90 },
            },
          },
          reference: {
            value: getOrDefault(testsFlat[flatIndex].test.reference, '-'),
            cellProps: { sx: { bgcolor: getTableBgColor(groupIndex) } },
          },
          result: {
            value: getOrDefault(testsFlat[flatIndex].test.results, '-'),
            cellProps: { sx: { bgcolor: getTableBgColor(groupIndex) } },
          },
        };
        rows.push(testRow);
        isFirstDateRow = false;
      }
      j += runLength;
    }
  });

  return rows;
};

export const getRecentTestsFiltered = (
  tests: TestResultItem[]
): TestResultItem[] => {
  const filteredTests: TestResultItem[] = tests.filter((test) => {
    const testsWithFiles = Object.entries(test.labTests).flatMap(([_, tests]) =>
      tests.filter(
        (test) => test.fileMetadata?.length > 0 && test.status === 'Ready'
      )
    );
    return testsWithFiles.length > 0;
  });

  return filteredTests;
};
