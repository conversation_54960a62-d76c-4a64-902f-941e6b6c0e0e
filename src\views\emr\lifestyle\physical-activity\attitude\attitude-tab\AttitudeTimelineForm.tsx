import { FC, memo, useEffect, useMemo, useState } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { attitudeStore } from '@/store/emr/lifestyle/physical-activity/attitude/attitude-store';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AttitudeForm from './AttitudeForm';

type Props = {
  data: QuestionnaireResponse;
};

const AttitudeTimelineForm: FC<Props> = ({ data }) => {
  const methods = useForm<QuestionnaireResponse>({
    defaultValues: data,
    mode: 'onChange',
  });

  const [transformedData, setTransformedData] = useState<any>(null);

  const formFields = useMemo(() => {
    if (!transformedData?.questions?.length) {
      return [];
    }
    return transformedData.questions;
  }, [transformedData]);

  useEffect(() => {
    if (data) {
      // The data structure shows fields with only values, not field definitions
      // We need to get the question template and merge it with the values
      const { questions: questionTemplate } = attitudeStore.getState();

      if (questionTemplate?.questions?.length) {
        // Merge template with actual data values
        const mergedData = {
          ...data,
          questions: questionTemplate.questions.map(
            (templateGroup: any, groupIndex: number) => {
              const dataGroup = data.questions?.[groupIndex];
              if (!dataGroup) return templateGroup;

              return {
                ...templateGroup,
                fields: templateGroup.fields?.map(
                  (templateField: any, fieldIndex: number) => {
                    const dataField = dataGroup.fields?.[fieldIndex];
                    if (!dataField) return templateField;

                    // If it's a section field, merge the template with data values
                    if (
                      templateField.type === 'section' &&
                      templateField.fields &&
                      dataField.value?.fields
                    ) {
                      return {
                        ...templateField,
                        value: dataField.value,
                        fields: templateField.fields.map(
                          (templateSubField: any, subFieldIndex: number) => {
                            const dataValue =
                              dataField.value?.fields?.[subFieldIndex]?.value;
                            return {
                              ...templateSubField,
                              value: dataValue || '',
                            };
                          }
                        ),
                      };
                    }

                    // For non-section fields, merge template with data value
                    return {
                      ...templateField,
                      value: dataField.value || '',
                    };
                  }
                ),
              };
            }
          ),
        };

        setTransformedData(mergedData);

        // Create form data structure
        const formData: any = {};
        mergedData.questions?.forEach(
          (questionGroup: any, groupIndex: number) => {
            if (!formData.questions) formData.questions = [];
            if (!formData.questions[groupIndex])
              formData.questions[groupIndex] = { fields: [] };

            questionGroup.fields?.forEach((field: any, fieldIndex: number) => {
              if (field.value !== undefined) {
                if (!formData.questions[groupIndex].fields[fieldIndex]) {
                  formData.questions[groupIndex].fields[fieldIndex] = {};
                }
                formData.questions[groupIndex].fields[fieldIndex].value =
                  field.value;
              }
            });
          }
        );

        methods.reset({ ...mergedData, ...formData });
      }
    }
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <AttitudeForm
        formFields={formFields}
        readonly
        variant="timeline"
        isAmbientForm={
          !!(data as any)?.conversation || !!(data as any)?.recordingDuration
        }
      />
    </FormProvider>
  );
};

export default memo(AttitudeTimelineForm);
