import React, { FC, memo, useCallback, useMemo, useState } from 'react';

import { useForm } from 'react-hook-form';

import Box from '@mui/material/Box';
import { MdOutlineSave } from 'react-icons/md';

import Loading from '@/lib/common/loading';

import { useConsultationStore } from '@/store/consultation-store';
import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { GetPatientHistoryRes, GetPatientQueueRes } from '@/query/patient';

import { FINALIZED } from '@/utils/constants/consultation';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { capitalizeFirstLetter } from '@/utils/string';

import IconamoonEdit from '@/assets/svg/IconamoonEdit';
import RecordViewIcon from '@/assets/svg/RecordViewIcon';
import TranscriptViewIcon from '@/assets/svg/TranscriptViewIcon';

import PrintPreview from '@/views/emr/consultation/shared/Print/PrintPreview';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from '@/components/ui/dialog';

import Transcript from '../transcript';

import Accordion from './Accordion';
import SummaryForm from './summary-form';

type Props = {
  isOpen?: boolean;
  onClose?: () => void;
  selectedHistory: GetPatientHistoryRes[0] | null;
  onFinalise?: (data: GetPatientHistoryRes[0]) => void;
  fetchPatientHistory?: () => Promise<void>;
  showFinaliseButton?: boolean;
};

const expandedView = {
  SUMMARY: 'summary',
  TRANSCRIPT: 'transcript',
} as const;

type ExpandedView = (typeof expandedView)[keyof typeof expandedView];

const { SUMMARY, TRANSCRIPT } = expandedView;

const ConsultationExpanded: FC<Props> = ({
  isOpen,
  onClose,
  selectedHistory,
  onFinalise,
  fetchPatientHistory,
  showFinaliseButton = true,
}) => {
  const { patient } = useCurrentPatientStore();
  const { updating, updatePatientHistory } = useConsultationStore();
  const { data: userData } = useUserStore();
  const { doctorProfile } = useDoctorStore();

  const [isEditMode, setIsEditMode] = useState(false);
  const [view, setView] = useState<ExpandedView>(SUMMARY);
  const [printData, setPrintData] = useState<{
    open: boolean;
    consultation: GetPatientQueueRes | null;
    doctorName: string;
  }>({
    open: false,
    consultation: null,
    doctorName: '',
  });

  const form = useForm();

  const handleClose = useCallback(() => {
    setIsEditMode(false);
    setView(SUMMARY);
    onClose?.();
  }, [onClose]);

  const handlePrint = useCallback(() => {
    if (!selectedHistory) return;

    const doctorName =
      selectedHistory?.summary?.owner?.id === userData.id &&
      doctorProfile?.general?.fullName
        ? doctorProfile.general.fullName
        : selectedHistory?.summary?.owner?.name || '-';

    // Close the expanded view first
    handleClose();

    // Then open the print preview
    setPrintData({
      open: true,
      consultation: selectedHistory as unknown as GetPatientQueueRes,
      doctorName,
    });
  }, [
    selectedHistory,
    userData.id,
    doctorProfile?.general?.fullName,
    handleClose,
  ]);

  const closePrintPreview = useCallback(() => {
    setPrintData((prev) => ({ ...prev, open: false }));
  }, []);

  const onSubmit = form.handleSubmit(async (data) => {
    if (!selectedHistory) return;

    const finalData: GetPatientHistoryRes[0] = {
      ...selectedHistory,
      summary: {
        ...selectedHistory?.summary,

        ...data,
      },
    };

    const dataWithUpdatedOwner = {
      ...finalData,
      summary: {
        ...finalData.summary,
        owner: {
          id: userData.id,
          role: userData.userRole,
          name: doctorProfile?.general?.fullName || userData.name,
          email: userData.email,
        },
      },
    };

    await updatePatientHistory(
      dataWithUpdatedOwner?.id,
      dataWithUpdatedOwner?.patientId,
      dataWithUpdatedOwner,
      fetchPatientHistory
    );

    handleClose();
  });

  const expandView = useMemo<Record<ExpandedView, JSX.Element>>(
    () => ({
      [SUMMARY]: (
        <SummaryForm
          key={selectedHistory?.id || 'default'}
          editable={isEditMode}
          form={form}
          data={selectedHistory}
          expanded
        />
      ),
      [TRANSCRIPT]: (
        <Transcript
          conversation={selectedHistory?.summary?.conversation}
          doctorName={doctorProfile?.general?.fullName || userData.name}
          patientName={patient?.name || '---'}
          date={formatDate(
            selectedHistory?.updated_on,
            DateFormats.DATE_DD_MM_YYYY_SLASH
          )}
          duration={selectedHistory?.summary?.recordingDuration}
        />
      ),
    }),
    [
      form,
      isEditMode,
      selectedHistory,
      doctorProfile?.general?.fullName,
      userData.name,
      patient?.name,
    ]
  );

  const renderEditSaveButton = useCallback(() => {
    if (view === TRANSCRIPT) return <div />;

    if (selectedHistory?.status === FINALIZED) return <div />;

    if (isEditMode) {
      return (
        <button
          className="py-1.5 flex-shrink-0 px-3 sn:px-5 bg-black text-sm md:text-base  text-white hover:bg-black/80 rounded-full flex items-center gap-[5px] border border-transparent"
          type="submit"
          disabled={updating}
          onClick={onSubmit}
        >
          Save Record {updating ? <Loading /> : <MdOutlineSave size={22} />}
        </button>
      );
    } else {
      return (
        <button
          type="button"
          onClick={() => setIsEditMode(true)}
          className="py-1.5 flex-shrink-0 px-3 sn:px-5 bg-black text-sm md:text-base  text-white hover:bg-black/80 rounded-full flex items-center gap-[5px] border border-transparent"
        >
          Edit Record
          <IconamoonEdit fontSize={16} color="#fff" />
        </button>
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditMode, onSubmit, updating, view]);

  const renderViewToggler = useCallback(() => {
    if (isEditMode || !selectedHistory?.summary?.conversation) return <div />;
    if (view === SUMMARY) {
      return (
        <button
          onClick={() => {
            setView(TRANSCRIPT);
          }}
          className="py-2.5  px-2  sn:px-5 text-sm border border-black hover:bg-black/5 rounded-full flex items-center gap-[5px] leading-none"
        >
          Switch to Transcript View <TranscriptViewIcon fontSize={16} />
        </button>
      );
    } else {
      return (
        <button
          onClick={() => {
            setView(SUMMARY);
          }}
          className="py-2 px-2 border text-sm border-black hover:bg-black/5 rounded-full flex items-center gap-[5px] leading-none"
        >
          Go back <RecordViewIcon fontSize={16} />
        </button>
      );
    }
  }, [isEditMode, selectedHistory?.summary?.conversation, view]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="p-0 max-h-[95vh] overflow-hidden max-w-202 gap-0">
        <DialogTitle className="sr-only">Consultation</DialogTitle>
        <DialogDescription className="sr-only">
          Consultation Details
        </DialogDescription>
        <Accordion
          open
          date={formatDate(
            selectedHistory?.updated_on,
            DateFormats.DATE_DD_MM_YYYY_SLASH
          )}
          duration={selectedHistory?.summary?.recordingDuration}
          showFinaliseButton={showFinaliseButton}
          finalized={selectedHistory?.status === FINALIZED}
          onFinalise={() =>
            onFinalise?.(selectedHistory as GetPatientHistoryRes[0])
          }
          designation={capitalizeFirstLetter(
            selectedHistory?.summary?.owner?.role || '---'
          )}
          doctorName={
            selectedHistory?.summary?.owner?.id === userData.id &&
            doctorProfile?.general?.fullName
              ? doctorProfile?.general?.fullName
              : selectedHistory?.summary?.owner?.name || '---'
          }
          department={
            selectedHistory?.summary?.owner?.id === userData.id &&
            doctorProfile?.general?.department
              ? doctorProfile?.general?.department
              : selectedHistory?.summary?.owner?.department || '---'
          }
          onExpand={handleClose}
          onPrint={handlePrint}
          expand
          className="!mb-0"
        >
          {/* Remove custom padding/bg so Accordion's styles show through */}
          <div className="rounded-thin-scrollbar overflow-auto h-[calc(100vh-200px)] w-full">
            {expandView[view]}
          </div>
        </Accordion>
        <DialogFooter>
          <Box className="bg-white w-full h-16 flex justify-between items-center border rounded-lg px-2">
            {renderEditSaveButton()}
            {renderViewToggler()}
          </Box>
        </DialogFooter>
      </DialogContent>
      {printData.consultation && patient && (
        <PrintPreview
          open={printData.open}
          onClose={closePrintPreview}
          consultations={[printData.consultation]}
          patient={patient}
          doctorName={printData.doctorName}
        />
      )}
    </Dialog>
  );
};

export default memo(ConsultationExpanded);
