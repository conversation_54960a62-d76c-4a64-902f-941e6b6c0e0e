'use client';

import { useState, useEffect } from 'react';

import { FaArrowLeft } from 'react-icons/fa';
import { toast } from 'sonner';

import { useRouter } from 'next/navigation';

import { getOrganizationRoles } from '@/query/subscription';

import {
  getSubscriptionUserData,
  saveSubscriptionUserData,
} from '@/utils/subscription';

import { routes } from '@/constants/routes';

import AppSelect from '@/core/components/app-select';

import SubscriptionLoading from '../components/SubscriptionLoading';

interface RoleOption {
  value: string;
  label: string;
  id: string;
}

// API Response interface for role data
interface RoleData {
  id: string;
  name: string;
  description: string;
  isDefault: boolean;
  organizationId: string;
}

interface RolesResponse {
  currentPage: number;
  items: RoleData[];
  totalItemCount: number;
  totalPages: number;
}

const SelectRoleView = () => {
  const router = useRouter();
  const [selectedRole, setSelectedRole] = useState<RoleOption | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [roleOptions, setRoleOptions] = useState<RoleOption[]>([]);
  const [rolesLoading, setRolesLoading] = useState(true);

  // Constant organization ID as specified
  const ORGANIZATION_ID = 'da42e4a1-eecb-4f51-a848-5f83ab76325a';

  useEffect(() => {
    // Check if user has completed previous steps
    const userData = getSubscriptionUserData();
    if (!userData || !userData.signupCompleted) {
      // Redirect back to signup if no user data found
      router.push(routes.SUBSCRIPTION_SIGNUP);
      return;
    }

    if (!userData.establishment) {
      // Redirect back to establishment selection if not completed
      router.push(routes.SUBSCRIPTION_SELECT_ESTABLISHMENT);
      return;
    }

    // Load roles from API
    loadRoles();
  }, [router]);

  const loadRoles = async () => {
    try {
      setRolesLoading(true);

      const response: RolesResponse = await getOrganizationRoles(
        ORGANIZATION_ID,
        1,
        10
      );

      // Filter out admin and organization Super Admin roles
      const filteredItems = response.items.filter((role) => {
        const roleName = role.name.toLowerCase();
        return roleName !== 'admin' && roleName !== 'organization super admin';
      });

      // Transform API response to role options with exact IDs
      const options: RoleOption[] = filteredItems.map((role) => ({
        value: role.name,
        label: role.name
          .split('_')
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(' '),
        id: role.id, // Store the exact role ID
      }));

      setRoleOptions(options);

      // Restore previously selected role if exists
      const userData = getSubscriptionUserData();
      if (userData?.role) {
        const savedRole = options.find(
          (option) => option.value === userData.role
        );
        if (savedRole) {
          setSelectedRole(savedRole);
        }
      }
    } catch (error) {
      console.error('Error loading roles:', error);
      toast.error('Failed to load roles. Please try again.');

      // Don't set fallback data - just clear the options
      setRoleOptions([]);
    } finally {
      setRolesLoading(false);
    }
  };

  const handleBackNavigation = () => {
    // When going back from role to establishment, clear the role selection
    // but keep the establishment selection since user is just adjusting earlier choice
    saveSubscriptionUserData({ role: undefined });
    router.push(routes.SUBSCRIPTION_SELECT_ESTABLISHMENT);
  };

  const handleRoleChange = (option: RoleOption | null) => {
    setSelectedRole(option);
  };

  const handleProceed = async () => {
    // Check if any roles are available
    if (roleOptions.length === 0) {
      toast.error('No roles are currently available. Please try again later.');
      return;
    }

    if (!selectedRole) {
      toast.error('Please select a role');
      return;
    }

    setIsLoading(true);

    try {
      // Store role selection with exact role ID
      saveSubscriptionUserData({
        role: selectedRole.value,
        roleId: selectedRole.id, // Store the exact role ID for API calls
      });

      // Get current user data to check establishment type
      const userData = getSubscriptionUserData();

      // Navigate based on establishment type
      if (userData?.establishment === 'hospital') {
        router.push(routes.SUBSCRIPTION_CUSTOM_PRICING);
      } else {
        // Clinic users go to standard plan selection
        router.push(routes.SUBSCRIPTION_PLAN);
      }
    } catch (error) {
      console.error('Error saving role:', error);
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (rolesLoading) {
    return <SubscriptionLoading />;
  }

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 relative"
      style={{
        background: 'linear-gradient(to bottom, #E6F6FF, #B4E5FE)',
      }}
    >
      <div className="mb-6 absolute left-0 top-0 p-4">
        <button
          onClick={handleBackNavigation}
          className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
        >
          <FaArrowLeft />
        </button>
      </div>
      <div className="w-full max-w-md">
        <div className="text-center mb-6">
          <h1
            className="text-gray-800"
            style={{ fontSize: '34px', fontWeight: 600 }}
          >
            Select the Role
          </h1>
        </div>

        <div className="mb-12">
          <div
            style={{
              backgroundColor: 'white',
              border: '1px solid #E5E7EB',
              borderRadius: '8px',
            }}
          >
            <AppSelect<RoleOption>
              placeholder={
                roleOptions.length === 0 ? 'No roles available' : 'Select'
              }
              options={roleOptions}
              value={selectedRole}
              onChange={handleRoleChange}
              getOptionValue={(option) => option.value}
              getOptionLabel={(option) => option.label}
              required
              isDisabled={roleOptions.length === 0}
            />
          </div>
          {roleOptions.length === 0 && (
            <p className="text-sm text-gray-500 mt-2 text-center">
              No roles are currently available. Please try again later.
            </p>
          )}
        </div>

        <button
          onClick={handleProceed}
          disabled={isLoading || !selectedRole || roleOptions.length === 0}
          className="w-full py-3 px-4 mt-20 bg-black hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
        >
          {isLoading ? 'Processing...' : 'Proceed'}
        </button>
      </div>
    </div>
  );
};

export default SelectRoleView;
