import React, { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import AppModal from '@/core/components/app-modal';
import { SuccessIcon } from '@/core/components/status-modal/modal-icons';

interface FreeTrialSuccessModalProps {
  open: boolean;
  onClose: () => void;
}

const FreeTrialSuccessModal: React.FC<FreeTrialSuccessModalProps> = ({
  open,
  onClose,
}) => {
  const router = useRouter();

  useEffect(() => {
    if (open) {
      // Navigate to login page after 3 seconds
      const timer = setTimeout(() => {
        onClose();
        router.push('/login');
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [open, onClose, router]);
  return (
    <AppModal
      open={open}
      onClose={onClose}
      classes={{
        root: 'w-[350px] !rounded-3xl relative',
        modal: '!rounded-3xl',
        header: 'hidden',
      }}
    >
      <div className="px-6 pb-4 pt-0 text-center">
        <div className="flex justify-center mb-4">
          <div className="scale-75">
            <SuccessIcon />
          </div>
        </div>
        <p className="text-base text-[#001926] mb-2">
          You&apos;re on Free trial Now!
        </p>
        <p className="text-base text-[#53BDF5] mb-2">
          You&apos;ve 30 Days Remaining!
        </p>
      </div>
    </AppModal>
  );
};

export default FreeTrialSuccessModal;
