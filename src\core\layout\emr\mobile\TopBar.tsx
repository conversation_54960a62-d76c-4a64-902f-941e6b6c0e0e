import { memo, useCallback, useEffect } from 'react';

import Image from 'next/image';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';

import { APP_LOGO } from '@/constants/image-url';

import MobileMenu from './MobileMenu';

const TopBar = () => {
  const { setUser, setUserData, data: userData } = useUserStore();
  const { fetchDoctorProfileByEmail } = useDoctorStore();

  const handleFetchUser = useCallback(async () => {
    if (!userData?.id) {
      const response = await getDoctorProfile();
      if (response?.data?.length > 0) {
        setUser(response.data?.[0].name);
        setUserData(response.data?.[0]);
      }
    }
  }, [setUser, setUserData, userData?.id]);

  useEffect(() => {
    handleFetchUser();
  }, [handleFetchUser]);

  useEffect(() => {
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData.email);
    }
  }, [userData?.email, fetchDoctorProfileByEmail]);

  return (
    <header className="h-10 bg-white p-1 flex items-center justify-between">
      <div className="h-full px-2 flex items-center justify-center">
        <Image
          src={APP_LOGO}
          alt="ARCA logo"
          height={32}
          width={32}
          className="object-contain"
        />
      </div>
      <div className="flex items-center justify-end">
        <MobileMenu />
      </div>
    </header>
  );
};

export default memo(TopBar);
