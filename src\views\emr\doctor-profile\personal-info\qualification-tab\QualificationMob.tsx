import React, {
  Dispatch,
  FC,
  memo,
  RefObject,
  SetStateAction,
  useCallback,
  useMemo,
} from 'react';

import {
  Control,
  Controller,
  FieldArrayWithId,
  Path,
  UseFormGetValues,
  UseFormRegister,
  useWatch,
} from 'react-hook-form';

import Stack from '@mui/material/Stack';
import { isEqual } from 'lodash';

import TextInput from '@core/components/text-input';

import {
  useEnterKeyNavigation,
  createArrayFieldNavigation,
} from '@/hooks/useEnterKeyNavigation';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';
import {
  allowNumbersTextAndDot,
  allowOnlyNumbers,
  enforceNumericInput,
  preventNonAlphabeticInput,
  restrictMaxLength,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import {
  defaultQualification,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import AutoResizeTextArea from '../shared/AutoResizeTextArea';
import ControlledFileUpload from '../shared/ControlledFileUpload';
import MobileAddDeleteButtons from '../shared/MobileAddDeleteButtons';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'qualifications', 'id'>[];
  control: Control<FormData>;
  register: UseFormRegister<FormData>;
  editableField: Set<string>;
  getValues: UseFormGetValues<FormData>;
  isSubmitted: boolean;
  setEditableField: Dispatch<SetStateAction<Set<string>>>;
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
  handleOnAdd: () => void;
  lastFieldRef: RefObject<HTMLDivElement>;
};

const QualificationMob: FC<Props> = ({
  fields,
  handleOnDelete,
  setEditableField,
  editableField,
  getValues,
  isSubmitted,
  control,
  lastFieldRef,
}) => {
  const { doctorProfile } = useDoctorStore();

  const watchedQualification = useWatch({
    control,
    name: 'qualifications',
  });

  const handleEditClick = useCallback(
    (fieldName: Path<FormData>) => {
      setEditableField((prev) => new Set(prev.add(fieldName)));
    },
    [setEditableField]
  );

  const isFieldDisabled = useCallback(
    (fieldName: Path<FormData>) => {
      if (editableField.has(fieldName)) {
        return false;
      }

      const formValues = getValues();
      const fieldValue = formValues[fieldName as keyof FormData];
      const doctorFieldValue = getNestedValue(
        doctorProfile?.professionalDetails,
        fieldName
      );

      return (
        !!doctorFieldValue ||
        (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
      );
    },
    [editableField, getValues, doctorProfile?.professionalDetails, isSubmitted]
  );

  const renderEditIcon = useCallback(
    (fieldName: Path<FormData>) => {
      return isFieldDisabled(fieldName) ? (
        <button type="button" onClick={() => handleEditClick(fieldName)}>
          <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
        </button>
      ) : null;
    },
    [isFieldDisabled, handleEditClick]
  );

  // Create field order for all qualification entries
  const allFieldOrder = useMemo(() => {
    const allFields: string[] = [];
    fields.forEach((_, index) => {
      const baseFieldOrder = [
        'degree',
        'specialization',
        'university',
        'institute',
        'duration',
        'yearOfCompletion',
        'marks',
      ];
      const fieldOrder = createArrayFieldNavigation(
        baseFieldOrder,
        index,
        'qualifications'
      );
      allFields.push(...fieldOrder);
    });
    return allFields;
  }, [fields.length]);

  const { handleKeyDown } = useEnterKeyNavigation({
    fieldOrder: allFieldOrder,
    isFieldDisabled: (fieldName: string) =>
      isFieldDisabled(fieldName as Path<FormData>),
    shouldTriggerSave: (fieldName: string) => {
      // Trigger save on the last field (marks) of the last qualification
      return (
        fieldName.includes('marks') &&
        fieldName.includes(`${fields.length - 1}`)
      );
    },
  });

  return (
    <>
      {fields?.map((field, index) => {
        return (
          <Stack
            key={field.id}
            spacing={2}
            ref={index === fields.length - 1 ? lastFieldRef : undefined}
          >
            <AutoResizeTextArea
              label="Qualification"
              placeholder=""
              color="white"
              control={control}
              name={`qualifications.${index}.degree`}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.degree`);
              }}
              endDecoration={renderEditIcon(`qualifications.${index}.degree`)}
              disabled={isFieldDisabled(`qualifications.${index}.degree`)}
              className="w-full"
              autoFocus={false}
            />

            <AutoResizeTextArea
              label="Specialization"
              placeholder=""
              color="white"
              control={control}
              name={`qualifications.${index}.specialization`}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.specialization`);
              }}
              endDecoration={renderEditIcon(
                `qualifications.${index}.specialization`
              )}
              disabled={isFieldDisabled(
                `qualifications.${index}.specialization`
              )}
              className="w-full"
              autoFocus={false}
            />

            <AutoResizeTextArea
              label="University"
              placeholder=""
              color="white"
              control={control}
              name={`qualifications.${index}.university`}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.university`);
              }}
              endDecoration={renderEditIcon(
                `qualifications.${index}.university`
              )}
              disabled={isFieldDisabled(`qualifications.${index}.university`)}
              className="w-full"
              autoFocus={false}
            />

            <AutoResizeTextArea
              label="Institute"
              placeholder=""
              color="white"
              control={control}
              name={`qualifications.${index}.institute`}
              onKeyDown={(e) => {
                preventNonAlphabeticInput(e);
                handleKeyDown(e, `qualifications.${index}.institute`);
              }}
              endDecoration={renderEditIcon(
                `qualifications.${index}.institute`
              )}
              disabled={isFieldDisabled(`qualifications.${index}.institute`)}
              className="w-full"
              autoFocus={false}
            />

            <AutoResizeTextArea
              label="Duration"
              placeholder=""
              color="white"
              control={control}
              name={`qualifications.${index}.duration`}
              onKeyDown={(e) => {
                allowNumbersTextAndDot(e);
                handleKeyDown(e, `qualifications.${index}.duration`);
              }}
              endDecoration={renderEditIcon(`qualifications.${index}.duration`)}
              disabled={isFieldDisabled(`qualifications.${index}.duration`)}
              className="w-full"
              autoFocus={false}
            />

            <Controller
              name={`qualifications.${index}.yearOfCompletion`}
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label="YOC"
                  disableRef
                  endDecoration={renderEditIcon(
                    `qualifications.${index}.yearOfCompletion`
                  )}
                  disabled={isFieldDisabled(
                    `qualifications.${index}.yearOfCompletion`
                  )}
                  placeholder=""
                  onKeyDown={(e) => {
                    allowOnlyNumbers(e);
                    restrictMaxLength(4)(e);
                    handleKeyDown(
                      e,
                      `qualifications.${index}.yearOfCompletion`
                    );
                  }}
                  onInput={enforceNumericInput()}
                  pattern="[0-9]*"
                  inputMode="numeric"
                  className="w-full"
                />
              )}
            />

            <AutoResizeTextArea
              label="Marks"
              placeholder=""
              color="white"
              control={control}
              name={`qualifications.${index}.marks`}
              onKeyDown={(e) =>
                handleKeyDown(e, `qualifications.${index}.marks`, {
                  shouldTriggerSave: index === fields.length - 1,
                })
              }
              endDecoration={renderEditIcon(`qualifications.${index}.marks`)}
              disabled={isFieldDisabled(`qualifications.${index}.marks`)}
              className="w-full"
              autoFocus={false}
            />
            <ControlledFileUpload
              name={`qualifications.${index}.doc1`}
              control={control}
              label="Document 1"
              className="w-full"
              allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
              fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
              showPreview
              maxFileSize={6}
              isBoxStyle
            />
            <ControlledFileUpload
              name={`qualifications.${index}.doc2`}
              control={control}
              label="Document 2"
              className="w-full"
              allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
              fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
              showPreview
              maxFileSize={6}
              isBoxStyle
            />
            <AutoResizeTextArea
              label="Status"
              placeholder=""
              color="white"
              control={control}
              name={`qualifications.${index}.status`}
              onKeyDown={preventNonAlphabeticInput}
              endDecoration={renderEditIcon(`qualifications.${index}.status`)}
              disabled={isFieldDisabled(`qualifications.${index}.status`)}
              className="w-full"
              autoFocus={false}
            />
            <MobileAddDeleteButtons
              onDelete={
                isEqual(watchedQualification[index], defaultQualification)
                  ? undefined
                  : () => handleOnDelete({ index, uuId: field?.uuId })
              }
            />
          </Stack>
        );
      })}
    </>
  );
};

export default memo(QualificationMob);
