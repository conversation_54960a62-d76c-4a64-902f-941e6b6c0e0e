import React, { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import { clearSubscriptionUserData } from '@/utils/subscription';

import { AlertIcon } from '@/assets/svg/AlertIcon';

import AppModal from '@/core/components/app-modal';

interface PaymentFailureModalProps {
  open: boolean;
  onClose: () => void;
  errorMessage?: string | React.ReactElement;
  isFromProfile?: boolean;
}

const PaymentFailureModal: React.FC<PaymentFailureModalProps> = ({
  open,
  onClose,
  errorMessage,
  isFromProfile = false,
}) => {
  const router = useRouter();

  useEffect(() => {
    if (open) {
      if (isFromProfile) {
        // If from profile, auto close after 5 seconds but don't redirect
        const timer = setTimeout(() => {
          handleCloseOnly();
        }, 5000);

        return () => clearTimeout(timer);
      } else {
        // If not from profile, redirect to signup after 5 seconds
        const timer = setTimeout(() => {
          handleCloseAndRedirect();
        }, 5000);

        return () => clearTimeout(timer);
      }
    }
  }, [open, isFromProfile]);

  const handleCloseOnly = () => {
    // Clear all subscription selection data
    clearSubscriptionUserData();

    // Just close modal - no redirection for profile users
    onClose();
  };

  const handleCloseAndRedirect = () => {
    // Clear all subscription selection data
    clearSubscriptionUserData();

    // Close modal
    onClose();

    // Only redirect to signup if not from profile
    if (!isFromProfile) {
      router.push('/subscription/signup');
    }
  };

  return (
    <AppModal
      open={open}
      onClose={handleCloseAndRedirect}
      classes={{
        root: 'w-[350px] !rounded-3xl relative',
        modal: '!rounded-3xl',
        header: 'hidden',
      }}
    >
      <div className="px-6 pb-4 pt-0 text-center">
        <div className="flex justify-center mb-4">
          <div className="scale-75">
            <AlertIcon />
          </div>
        </div>
        {errorMessage ? (
          <div className="text-base text-[#001926] mb-2">
            {typeof errorMessage === 'string' ? (
              // Remove double quotes if present at start and end
              <p>{errorMessage.replace(/^"(.*)"$/, '$1')}</p>
            ) : (
              errorMessage
            )}
          </div>
        ) : (
          <p className="text-base text-[#001926] mb-2">
            Transaction failed!
            <br />
            Please try again later.
          </p>
        )}
      </div>
    </AppModal>
  );
};

export default PaymentFailureModal;
